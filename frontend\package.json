{"name": "artifacts-chat-frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@antv/g2plot": "^2.4.33", "@arco-design/web-vue": "^2.57.0", "@vue/compiler-sfc": "^3.5.17", "highlight.js": "^11.11.1", "markdown-it": "^14.1.0", "mermaid": "^11.8.1", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/markdown-it": "^14.1.2", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^8.0.4", "typescript": "~5.8.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}