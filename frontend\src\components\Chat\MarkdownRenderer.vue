<template>
  <div class="markdown-renderer">
    <div v-html="renderedContent" class="markdown-content"></div>
    <!-- 渲染 Artifact Blocks -->
    <div v-for="artifact in detectedArtifacts" :key="artifact.id" class="artifact-container">
      <ArtifactBlock :artifact="artifact" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'
import { useLayoutStore } from '@/stores/layout'
import type { ArtifactData } from '@/stores/layout'
import ArtifactBlock from '../Artifact/ArtifactBlock.vue'

interface Props {
  content: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  artifactDetected: [artifact: ArtifactData]
}>()

const layoutStore = useLayoutStore()

// 存储检测到的 artifacts
const detectedArtifacts = ref<ArtifactData[]>([])

// 配置 markdown-it
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        const highlighted = hljs.highlight(str, { language: lang }).value
        
        // 检查是否是可渲染的 artifact
        if (isArtifactLanguage(lang)) {
          const artifactId = generateArtifactId()
          const artifact: ArtifactData = {
            id: artifactId,
            type: lang as any,
            code: str,
            title: getArtifactTitle(lang, str),
            messageId: Date.now().toString()
          }
          
          // 显示 artifact
          layoutStore.showArtifact(artifact)
          emit('artifactDetected', artifact)

          // 添加到检测到的 artifacts 列表
          detectedArtifacts.value.push(artifact)
          
          // 返回带有 artifact 标识的代码块
          return `<div class="artifact-code-block" data-artifact-id="${artifactId}">
            <div class="artifact-header">
              <span class="artifact-title">${artifact.title}</span>
              <span class="artifact-type">${lang.toUpperCase()}</span>
              <button class="artifact-preview-btn" onclick="window.showArtifact('${artifactId}')">
                预览
              </button>
            </div>
            <pre><code class="hljs language-${lang}">${highlighted}</code></pre>
          </div>`
        }
        
        return `<pre><code class="hljs language-${lang}">${highlighted}</code></pre>`
      } catch (__) {}
    }
    
    return `<pre><code class="hljs">${md.utils.escapeHtml(str)}</code></pre>`
  }
})

const renderedContent = computed(() => {
  return md.render(props.content)
})

// 判断是否是可渲染的 artifact 语言
const isArtifactLanguage = (lang: string): boolean => {
  const artifactLanguages = ['html', 'vue', 'g2plot', 'mermaid', 'svg', 'javascript']
  return artifactLanguages.includes(lang.toLowerCase())
}

// 生成 artifact ID
const generateArtifactId = (): string => {
  return `artifact_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// 获取 artifact 标题
const getArtifactTitle = (lang: string, code: string): string => {
  switch (lang.toLowerCase()) {
    case 'html':
      // 尝试从 HTML 中提取 title
      const titleMatch = code.match(/<title>(.*?)<\/title>/i)
      return titleMatch ? titleMatch[1] : 'HTML 页面'
    case 'vue':
      return 'Vue 组件'
    case 'g2plot':
      return '数据图表'
    case 'mermaid':
      return '流程图'
    case 'svg':
      return 'SVG 图形'
    case 'javascript':
      return 'JavaScript 代码'
    default:
      return 'Artifact'
  }
}

// 全局函数，用于从代码块按钮调用
onMounted(() => {
  (window as any).showArtifact = (artifactId: string) => {
    layoutStore.switchToArtifact(artifactId)
  }
})
</script>

<style scoped>
.markdown-renderer {
  width: 100%;
}

.markdown-content {
  line-height: 1.6;
  color: #1d2129;
}

/* Markdown 基础样式 */
.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
  margin: 1.5em 0 0.5em 0;
  font-weight: 600;
  line-height: 1.3;
}

.markdown-content :deep(p) {
  margin: 0.8em 0;
}

.markdown-content :deep(code) {
  background: #f2f3f5;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

.markdown-content :deep(pre) {
  background: #f7f8fa;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  padding: 1em;
  overflow-x: auto;
  margin: 1em 0;
}

.markdown-content :deep(pre code) {
  background: none;
  padding: 0;
  border-radius: 0;
}

/* Artifact 代码块样式 */
.markdown-content :deep(.artifact-code-block) {
  border: 2px solid #165dff;
  border-radius: 8px;
  margin: 1.5em 0;
  overflow: hidden;
  background: #fff;
}

.markdown-content :deep(.artifact-header) {
  background: #165dff;
  color: white;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
}

.markdown-content :deep(.artifact-title) {
  font-weight: 500;
}

.markdown-content :deep(.artifact-type) {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.markdown-content :deep(.artifact-preview-btn) {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.markdown-content :deep(.artifact-preview-btn:hover) {
  background: rgba(255, 255, 255, 0.3);
}

.markdown-content :deep(.artifact-code-block pre) {
  margin: 0;
  border: none;
  border-radius: 0;
  background: #f7f8fa;
}
</style>
