"""
Knowledge Base Manager for Context Engine

Manages the knowledge base including documents, code examples, and their vectors.
"""
import asyncio
import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
import uuid

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.db.database import get_db
from app.models.context_engine import Collection, Document, DocumentChunk, CodeExample
from app.services.context_engine.vector_store import VectorStoreManager
from app.services.context_engine.embedding_service import get_embedding_service
from app.services.context_engine.document_processor import DocumentProcessor
from app.core.context_config import context_settings
import structlog

logger = structlog.get_logger(__name__)

class KnowledgeManager:
    """Manages knowledge base operations"""
    
    def __init__(self, db: Session):
        self.db = db
        self.vector_store = VectorStoreManager()
        self.embedding_service = get_embedding_service()
        self.document_processor = DocumentProcessor()
        
        logger.info("Initialized KnowledgeManager")
    
    async def initialize(self) -> bool:
        """Initialize the knowledge manager"""
        try:
            # Initialize vector store
            await self.vector_store.initialize()
            
            # Create default collections if they don't exist
            await self._create_default_collections()
            
            logger.info("Knowledge manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error("Failed to initialize knowledge manager", error=str(e))
            return False
    
    async def _create_default_collections(self):
        """Create default collections"""
        default_collections = [
            {
                'name': 'frontend-docs',
                'description': 'Frontend development documentation',
                'category': 'frontend',
                'subcategory': 'general'
            },
            {
                'name': 'backend-docs',
                'description': 'Backend development documentation',
                'category': 'backend',
                'subcategory': 'general'
            },
            {
                'name': 'vue-examples',
                'description': 'Vue.js code examples and components',
                'category': 'frontend',
                'subcategory': 'vue'
            },
            {
                'name': 'fastapi-examples',
                'description': 'FastAPI code examples and patterns',
                'category': 'backend',
                'subcategory': 'fastapi'
            }
        ]
        
        for collection_data in default_collections:
            existing = self.db.query(Collection).filter(
                Collection.name == collection_data['name']
            ).first()
            
            if not existing:
                collection = Collection(**collection_data)
                self.db.add(collection)
        
        self.db.commit()
    
    async def add_document(
        self,
        file_path: str,
        collection_name: str = 'general-docs',
        title: Optional[str] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Add a document to the knowledge base"""
        try:
            # Get or create collection
            collection = await self._get_or_create_collection(collection_name)
            
            # Process the document
            processed_doc = await self.document_processor.process_file(file_path)
            
            # Create document record
            document = Document(
                id=str(uuid.uuid4()),
                collection_id=collection.id,
                title=title or Path(file_path).stem,
                content=processed_doc['content'],
                file_path=file_path,
                file_size=processed_doc['file_size'],
                file_hash=processed_doc['file_hash'],
                document_type=processed_doc['file_type'],
                tags=tags or [],
                metadata=metadata or {}
            )
            
            self.db.add(document)
            self.db.flush()  # Get the ID
            
            # Process chunks and generate embeddings
            chunks_data = []
            chunk_texts = []
            
            for chunk in processed_doc['chunks']:
                chunk_record = DocumentChunk(
                    id=str(uuid.uuid4()),
                    document_id=document.id,
                    chunk_index=chunk['chunk_index'],
                    content=chunk['content'],
                    token_count=chunk['token_count'],
                    metadata=chunk['metadata']
                )
                
                self.db.add(chunk_record)
                chunks_data.append(chunk_record)
                chunk_texts.append(chunk['content'])
            
            # Generate embeddings for chunks
            if chunk_texts:
                embeddings = await self.embedding_service.embed_texts(chunk_texts)
                
                # Store vectors in vector database
                vector_payloads = []
                vector_ids = []
                
                for i, (chunk_record, embedding) in enumerate(zip(chunks_data, embeddings)):
                    vector_id = f"doc_{document.id}_chunk_{i}"
                    chunk_record.vector_id = vector_id
                    vector_ids.append(vector_id)
                    
                    vector_payloads.append({
                        'document_id': document.id,
                        'chunk_id': chunk_record.id,
                        'content': chunk_record.content,
                        'title': document.title,
                        'file_path': document.file_path,
                        'document_type': document.document_type,
                        'collection': collection_name,
                        'tags': document.tags,
                        'metadata': chunk_record.metadata
                    })
                
                # Add to vector store
                await self.vector_store.add_documents(
                    vector_payloads,
                    embeddings,
                    collection_name
                )
            
            # Update document status
            document.status = 'completed'
            document.processed_at = datetime.utcnow()
            
            self.db.commit()
            
            logger.info("Added document to knowledge base", 
                       document_id=document.id, 
                       chunks=len(chunks_data),
                       collection=collection_name)
            
            return document.id
            
        except Exception as e:
            self.db.rollback()
            logger.error("Failed to add document", file_path=file_path, error=str(e))
            raise
    
    async def add_code_example(
        self,
        title: str,
        description: str,
        code: str,
        language: str,
        framework: Optional[str] = None,
        category: Optional[str] = None,
        tags: Optional[List[str]] = None,
        dependencies: Optional[List[str]] = None,
        collection_name: str = 'code-examples'
    ) -> str:
        """Add a code example to the knowledge base"""
        try:
            # Get or create collection
            collection = await self._get_or_create_collection(collection_name)
            
            # Create code example record
            code_example = CodeExample(
                id=str(uuid.uuid4()),
                collection_id=collection.id,
                title=title,
                description=description,
                code=code,
                language=language,
                framework=framework,
                category=category,
                tags=tags or [],
                dependencies=dependencies or []
            )
            
            self.db.add(code_example)
            self.db.flush()
            
            # Generate embedding for code
            from app.services.context_engine.embedding_service import embed_code_snippet
            embedding = await embed_code_snippet(code, language, description)
            
            # Store in vector database
            vector_id = f"code_{code_example.id}"
            code_example.vector_id = vector_id
            
            vector_payload = {
                'code_example_id': code_example.id,
                'title': title,
                'description': description,
                'code': code,
                'language': language,
                'framework': framework,
                'category': category,
                'tags': tags or [],
                'dependencies': dependencies or [],
                'collection': collection_name,
                'type': 'code_example'
            }
            
            await self.vector_store.add_documents(
                [vector_payload],
                [embedding],
                collection_name
            )
            
            self.db.commit()
            
            logger.info("Added code example to knowledge base", 
                       code_id=code_example.id,
                       language=language,
                       collection=collection_name)
            
            return code_example.id
            
        except Exception as e:
            self.db.rollback()
            logger.error("Failed to add code example", title=title, error=str(e))
            raise
    
    async def search_knowledge(
        self,
        query: str,
        collection_names: Optional[List[str]] = None,
        document_types: Optional[List[str]] = None,
        languages: Optional[List[str]] = None,
        limit: int = 10,
        similarity_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """Search the knowledge base"""
        try:
            # Generate query embedding
            query_embedding = await self.embedding_service.embed_text(query)
            
            # Build filters
            filters = {}
            if collection_names:
                filters['collection'] = {'$in': collection_names}
            if document_types:
                filters['document_type'] = {'$in': document_types}
            if languages:
                filters['language'] = {'$in': languages}
            
            # Search in vector store
            results = await self.vector_store.search_similar(
                query_embedding,
                limit=limit * 2,  # Get more results to filter
                filters=filters if filters else None
            )
            
            # Filter by similarity threshold and format results
            formatted_results = []
            for vector_id, score, payload in results:
                if score >= similarity_threshold:
                    formatted_results.append({
                        'id': vector_id,
                        'score': score,
                        'content': payload.get('content', ''),
                        'title': payload.get('title', ''),
                        'type': payload.get('type', 'document'),
                        'metadata': payload
                    })
            
            # Limit final results
            formatted_results = formatted_results[:limit]
            
            logger.info("Searched knowledge base", 
                       query_length=len(query),
                       results=len(formatted_results))
            
            return formatted_results
            
        except Exception as e:
            logger.error("Failed to search knowledge base", query=query[:100], error=str(e))
            return []
    
    async def _get_or_create_collection(self, name: str) -> Collection:
        """Get existing collection or create new one"""
        collection = self.db.query(Collection).filter(Collection.name == name).first()
        
        if not collection:
            collection = Collection(
                id=str(uuid.uuid4()),
                name=name,
                description=f"Auto-created collection: {name}",
                category='general',
                subcategory='auto'
            )
            self.db.add(collection)
            self.db.flush()
        
        return collection
    
    async def get_collections(self) -> List[Dict[str, Any]]:
        """Get all collections"""
        collections = self.db.query(Collection).all()
        return [
            {
                'id': c.id,
                'name': c.name,
                'description': c.description,
                'category': c.category,
                'subcategory': c.subcategory,
                'created_at': c.created_at.isoformat(),
                'document_count': len(c.documents),
                'code_example_count': len(c.code_examples)
            }
            for c in collections
        ]
    
    async def get_collection_stats(self, collection_name: str) -> Dict[str, Any]:
        """Get statistics for a collection"""
        collection = self.db.query(Collection).filter(Collection.name == collection_name).first()
        
        if not collection:
            return {}
        
        document_count = self.db.query(Document).filter(Document.collection_id == collection.id).count()
        code_count = self.db.query(CodeExample).filter(CodeExample.collection_id == collection.id).count()
        
        return {
            'name': collection.name,
            'document_count': document_count,
            'code_example_count': code_count,
            'total_items': document_count + code_count,
            'created_at': collection.created_at.isoformat()
        }

# Dependency injection
async def get_knowledge_manager(db: Session = Depends(get_db)) -> KnowledgeManager:
    """Get knowledge manager instance"""
    return KnowledgeManager(db)
