import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface ArtifactData {
  id: string
  type: 'html' | 'vue' | 'g2plot' | 'mermaid' | 'svg' | 'javascript'
  code: string
  title: string
  messageId: string
}

export const useLayoutStore = defineStore('layout', () => {
  // State
  const siderCollapsed = ref(false)
  const showArtifactPanel = ref(false)
  const currentArtifact = ref<ArtifactData | null>(null)
  const artifactHistory = ref<ArtifactData[]>([])

  // Getters
  const isDesktop = computed(() => window.innerWidth >= 768)
  const isMobile = computed(() => window.innerWidth < 768)

  // Actions
  const setSiderCollapsed = (collapsed: boolean) => {
    siderCollapsed.value = collapsed
  }

  const toggleSider = () => {
    siderCollapsed.value = !siderCollapsed.value
  }

  const showArtifact = (artifact: ArtifactData) => {
    currentArtifact.value = artifact
    showArtifactPanel.value = true
    
    // 添加到历史记录
    const existingIndex = artifactHistory.value.findIndex(a => a.id === artifact.id)
    if (existingIndex > -1) {
      artifactHistory.value.splice(existingIndex, 1)
    }
    artifactHistory.value.unshift(artifact)
    
    // 限制历史记录数量
    if (artifactHistory.value.length > 10) {
      artifactHistory.value = artifactHistory.value.slice(0, 10)
    }
  }

  const hideArtifact = () => {
    showArtifactPanel.value = false
    currentArtifact.value = null
  }

  const switchToArtifact = (artifactId: string) => {
    const artifact = artifactHistory.value.find(a => a.id === artifactId)
    if (artifact) {
      currentArtifact.value = artifact
      showArtifactPanel.value = true
    }
  }

  const removeArtifactFromHistory = (artifactId: string) => {
    const index = artifactHistory.value.findIndex(a => a.id === artifactId)
    if (index > -1) {
      artifactHistory.value.splice(index, 1)
      
      // 如果删除的是当前显示的 artifact，隐藏面板
      if (currentArtifact.value?.id === artifactId) {
        hideArtifact()
      }
    }
  }

  const clearArtifactHistory = () => {
    artifactHistory.value = []
    hideArtifact()
  }

  // 响应式布局处理
  const handleResize = () => {
    // 在移动端自动收起侧边栏
    if (window.innerWidth < 768 && !siderCollapsed.value) {
      setSiderCollapsed(true)
    }
  }

  // 初始化时设置响应式监听
  if (typeof window !== 'undefined') {
    window.addEventListener('resize', handleResize)
    handleResize() // 初始检查
  }

  return {
    // State
    siderCollapsed,
    showArtifactPanel,
    currentArtifact,
    artifactHistory,
    
    // Getters
    isDesktop,
    isMobile,
    
    // Actions
    setSiderCollapsed,
    toggleSider,
    showArtifact,
    hideArtifact,
    switchToArtifact,
    removeArtifactFromHistory,
    clearArtifactHistory,
    handleResize
  }
})
