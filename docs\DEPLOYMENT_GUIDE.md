# 🚀 Artifacts Chat 部署指南

## 📋 目录
- [部署方式概览](#部署方式概览)
- [Docker Compose 部署 (推荐)](#docker-compose-部署)
- [手动部署](#手动部署)
- [生产环境配置](#生产环境配置)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)

## 🎯 部署方式概览

### 部署选项对比

| 部署方式 | 难度 | 适用场景 | 优点 | 缺点 |
|---------|------|----------|------|------|
| **Docker Compose** | ⭐⭐ | 开发/测试/小规模生产 | 简单快速、环境一致 | 单机限制 |
| **手动部署** | ⭐⭐⭐ | 定制化需求 | 灵活可控 | 配置复杂 |
| **Kubernetes** | ⭐⭐⭐⭐⭐ | 大规模生产环境 | 高可用、自动扩缩容 | 复杂度高 |

### 系统要求

#### 最低配置
- **CPU**: 2核
- **内存**: 4GB
- **存储**: 20GB
- **网络**: 1Mbps

#### 推荐配置
- **CPU**: 4核+
- **内存**: 8GB+
- **存储**: 50GB+ SSD
- **网络**: 10Mbps+

## 🐳 Docker Compose 部署 (推荐)

### 1. 环境准备

#### 1.1 安装Docker
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# CentOS/RHEL
sudo yum install -y docker
sudo systemctl start docker
sudo systemctl enable docker

# macOS
brew install docker
# 或下载 Docker Desktop

# 验证安装
docker --version
docker-compose --version
```

#### 1.2 克隆项目
```bash
git clone <repository-url>
cd artifacts-chat
```

### 2. 配置环境变量

#### 2.1 复制配置模板
```bash
cp backend/.env.example backend/.env
```

#### 2.2 编辑生产配置
```bash
vim backend/.env
```

**关键配置项**:
```bash
# 应用配置
APP_NAME="Artifacts Chat"
DEBUG=false
LOG_LEVEL=WARNING

# 数据库配置
DATABASE_URL=********************************************************/artifacts_chat

# Redis配置
REDIS_URL=redis://redis:6379/0

# 向量数据库配置
QDRANT_URL=http://qdrant:6333

# LLM API配置 (必需)
OPENAI_API_KEY=sk-your-openai-api-key-here
CLAUDE_API_KEY=sk-ant-your-claude-api-key-here

# 安全配置 (重要!)
SECRET_KEY=your-very-secure-secret-key-change-this-in-production

# CORS配置
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com
```

### 3. 启动服务

#### 3.1 一键启动
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

#### 3.2 验证部署
```bash
# 检查服务健康状态
curl http://localhost:8080/health

# 检查前端访问
curl http://localhost:3000

# 检查API文档
open http://localhost:8080/docs
```

### 4. 域名和SSL配置

#### 4.1 配置Nginx (可选)
```bash
# 创建nginx配置
mkdir -p nginx
cat > nginx/nginx.conf << 'EOF'
server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL证书配置
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    # 前端
    location / {
        proxy_pass http://frontend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 后端API
    location /api/ {
        proxy_pass http://backend:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
EOF
```

#### 4.2 SSL证书配置
```bash
# 使用Let's Encrypt (推荐)
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com

# 或使用自签名证书 (测试)
mkdir -p nginx/ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout nginx/ssl/key.pem \
  -out nginx/ssl/cert.pem
```

### 5. 数据持久化

#### 5.1 数据备份
```bash
# 数据库备份
docker-compose exec postgres pg_dump -U postgres artifacts_chat > backup.sql

# 向量数据库备份
docker-compose exec qdrant tar -czf - /qdrant/storage > qdrant_backup.tar.gz

# 文件上传备份
docker-compose exec backend tar -czf - /app/uploads > uploads_backup.tar.gz
```

#### 5.2 数据恢复
```bash
# 数据库恢复
docker-compose exec -T postgres psql -U postgres artifacts_chat < backup.sql

# 向量数据库恢复
docker-compose exec -T qdrant tar -xzf - -C / < qdrant_backup.tar.gz

# 文件恢复
docker-compose exec -T backend tar -xzf - -C /app < uploads_backup.tar.gz
```

## 🔧 手动部署

### 1. 系统环境准备

#### 1.1 安装系统依赖
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y python3.11 python3.11-venv python3-pip nodejs npm postgresql redis-server nginx

# CentOS/RHEL
sudo yum install -y python3.11 nodejs npm postgresql-server redis nginx
```

#### 1.2 配置数据库
```bash
# PostgreSQL配置
sudo -u postgres createuser artifacts_chat
sudo -u postgres createdb artifacts_chat -O artifacts_chat
sudo -u postgres psql -c "ALTER USER artifacts_chat PASSWORD 'your-password';"

# Redis配置
sudo systemctl start redis
sudo systemctl enable redis
```

### 2. 后端部署

#### 2.1 部署Python应用
```bash
# 创建应用目录
sudo mkdir -p /opt/artifacts-chat
sudo chown $USER:$USER /opt/artifacts-chat
cd /opt/artifacts-chat

# 克隆代码
git clone <repository-url> .

# 创建虚拟环境
cd backend
python3.11 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

#### 2.2 配置环境变量
```bash
# 创建生产配置
cat > .env << 'EOF'
DEBUG=false
DATABASE_URL=postgresql://artifacts_chat:your-password@localhost:5432/artifacts_chat
REDIS_URL=redis://localhost:6379/0
OPENAI_API_KEY=your-openai-key
SECRET_KEY=your-secret-key
EOF
```

#### 2.3 创建系统服务
```bash
# 创建systemd服务文件
sudo cat > /etc/systemd/system/artifacts-chat-backend.service << 'EOF'
[Unit]
Description=Artifacts Chat Backend
After=network.target postgresql.service redis.service

[Service]
Type=exec
User=artifacts-chat
Group=artifacts-chat
WorkingDirectory=/opt/artifacts-chat/backend
Environment=PATH=/opt/artifacts-chat/backend/venv/bin
ExecStart=/opt/artifacts-chat/backend/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8080
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

# 启动服务
sudo systemctl daemon-reload
sudo systemctl start artifacts-chat-backend
sudo systemctl enable artifacts-chat-backend
```

### 3. 前端部署

#### 3.1 构建前端应用
```bash
cd /opt/artifacts-chat/frontend

# 安装依赖
npm install

# 构建生产版本
npm run build
```

#### 3.2 配置Nginx
```bash
# 创建nginx配置
sudo cat > /etc/nginx/sites-available/artifacts-chat << 'EOF'
server {
    listen 80;
    server_name your-domain.com;
    
    root /opt/artifacts-chat/frontend/dist;
    index index.html;
    
    # 前端路由
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 后端API代理
    location /api/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# 启用站点
sudo ln -s /etc/nginx/sites-available/artifacts-chat /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 🔒 生产环境配置

### 1. 安全配置

#### 1.1 防火墙配置
```bash
# UFW (Ubuntu)
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# iptables
sudo iptables -A INPUT -p tcp --dport 22 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT
sudo iptables -A INPUT -j DROP
```

#### 1.2 SSL/TLS配置
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

#### 1.3 数据库安全
```bash
# PostgreSQL安全配置
sudo -u postgres psql << 'EOF'
-- 修改默认密码
ALTER USER postgres PASSWORD 'strong-password';

-- 创建应用用户
CREATE USER artifacts_chat WITH PASSWORD 'app-password';
GRANT ALL PRIVILEGES ON DATABASE artifacts_chat TO artifacts_chat;
EOF

# 配置pg_hba.conf
sudo vim /etc/postgresql/15/main/pg_hba.conf
# 修改: local all all md5
```

### 2. 性能优化

#### 2.1 数据库优化
```sql
-- PostgreSQL配置优化
-- 编辑 /etc/postgresql/15/main/postgresql.conf

shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
```

#### 2.2 应用优化
```bash
# 增加worker进程数
# 编辑 systemd 服务文件
ExecStart=/opt/artifacts-chat/backend/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8080 --workers 4

# 配置Redis缓存
# 编辑 /etc/redis/redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
```

#### 2.3 Nginx优化
```nginx
# 编辑 /etc/nginx/nginx.conf
worker_processes auto;
worker_connections 1024;

# 启用gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml;

# 启用HTTP/2
listen 443 ssl http2;
```

## 📊 监控和维护

### 1. 健康检查

#### 1.1 服务监控脚本
```bash
#!/bin/bash
# health_check.sh

# 检查后端服务
if curl -f http://localhost:8080/health > /dev/null 2>&1; then
    echo "✅ Backend is healthy"
else
    echo "❌ Backend is down"
    sudo systemctl restart artifacts-chat-backend
fi

# 检查数据库
if pg_isready -h localhost -p 5432 > /dev/null 2>&1; then
    echo "✅ Database is healthy"
else
    echo "❌ Database is down"
fi

# 检查Redis
if redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis is healthy"
else
    echo "❌ Redis is down"
fi
```

#### 1.2 定时监控
```bash
# 添加到crontab
crontab -e
# 每5分钟检查一次
*/5 * * * * /opt/artifacts-chat/scripts/health_check.sh >> /var/log/health_check.log 2>&1
```

### 2. 日志管理

#### 2.1 日志轮转配置
```bash
# 创建logrotate配置
sudo cat > /etc/logrotate.d/artifacts-chat << 'EOF'
/var/log/artifacts-chat/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 artifacts-chat artifacts-chat
    postrotate
        systemctl reload artifacts-chat-backend
    endscript
}
EOF
```

#### 2.2 日志监控
```bash
# 实时查看日志
sudo journalctl -u artifacts-chat-backend -f

# 查看错误日志
sudo journalctl -u artifacts-chat-backend --since "1 hour ago" -p err
```

### 3. 备份策略

#### 3.1 自动备份脚本
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/opt/backups/artifacts-chat"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 数据库备份
pg_dump -h localhost -U artifacts_chat artifacts_chat > $BACKUP_DIR/db_$DATE.sql

# 文件备份
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz /opt/artifacts-chat/uploads

# 清理旧备份 (保留7天)
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
```

#### 3.2 定时备份
```bash
# 添加到crontab
crontab -e
# 每天凌晨2点备份
0 2 * * * /opt/artifacts-chat/scripts/backup.sh >> /var/log/backup.log 2>&1
```

## 🔧 故障排除

### 常见问题及解决方案

#### Q1: 服务启动失败
```bash
# 检查服务状态
sudo systemctl status artifacts-chat-backend

# 查看详细日志
sudo journalctl -u artifacts-chat-backend --no-pager

# 检查端口占用
sudo netstat -tlnp | grep :8080

# 检查配置文件
python -c "from app.core.config import settings; print(settings)"
```

#### Q2: 数据库连接失败
```bash
# 检查数据库状态
sudo systemctl status postgresql

# 测试连接
psql -h localhost -U artifacts_chat -d artifacts_chat

# 检查配置
sudo -u postgres psql -c "\l"
```

#### Q3: 前端无法访问
```bash
# 检查Nginx状态
sudo systemctl status nginx

# 测试配置
sudo nginx -t

# 查看错误日志
sudo tail -f /var/log/nginx/error.log
```

#### Q4: SSL证书问题
```bash
# 检查证书状态
sudo certbot certificates

# 手动续期
sudo certbot renew

# 测试SSL配置
openssl s_client -connect your-domain.com:443
```

#### Q5: 性能问题
```bash
# 检查系统资源
htop
df -h
free -h

# 检查数据库性能
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"

# 检查应用日志
grep "slow" /var/log/artifacts-chat/app.log
```

## 📞 技术支持

### 获取帮助
- **文档**: 查看项目docs目录
- **日志**: 检查应用和系统日志
- **监控**: 使用健康检查端点
- **社区**: 提交GitHub Issue

### 紧急联系
- **系统管理员**: <EMAIL>
- **开发团队**: <EMAIL>
- **技术支持**: <EMAIL>

---

**部署成功！** 🎉 享受您的智能聊天平台吧！
