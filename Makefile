# Context Engine Development Makefile (OrbStack optimized)

.PHONY: help setup start stop health clean install-deps

# Default target
help:
	@echo "🚀 Context Engine Development Commands (OrbStack)"
	@echo "=================================================="
	@echo "make setup      - Initial project setup"
	@echo "make start      - Start development environment"
	@echo "make stop       - Stop all services"
	@echo "make health     - Check service health"
	@echo "make clean      - Clean up containers and volumes"
	@echo "make install    - Install dependencies"
	@echo "make quick      - Quick start core services only"
	@echo "make logs       - View all service logs"
	@echo "make restart    - Restart all services"

# Initial setup
setup:
	@echo "🔧 Setting up Context Engine development environment..."
	@chmod +x scripts/*.sh
	@chmod +x quick-start-orbstack.sh
	@mkdir -p knowledge/{frontend,backend,examples}
	@mkdir -p data/{chroma,models}
	@mkdir -p backend/logs
	@mkdir -p monitoring
	@echo "✅ Setup completed!"

# Start full development environment
start: setup
	@echo "🚀 Starting full development environment..."
	@./scripts/start-dev-orbstack.sh

# Quick start (core services only)
quick: setup
	@echo "⚡ Quick starting core services..."
	@./quick-start-orbstack.sh

# Stop all services
stop:
	@echo "🛑 Stopping all services..."
	@./scripts/stop-dev.sh

# Health check
health:
	@./scripts/health-check.sh

# Clean up everything
clean:
	@echo "🧹 Cleaning up containers and volumes..."
	@./scripts/stop-dev.sh --clean-volumes
	@docker system prune -f

# Install dependencies
install:
	@echo "📦 Installing dependencies..."
	@cd backend && python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt
	@cd frontend && (command -v pnpm >/dev/null 2>&1 && pnpm install) || (command -v yarn >/dev/null 2>&1 && yarn install) || npm install

# View logs
logs:
	@docker-compose -f docker-compose.dev.yml logs -f

# Restart services
restart: stop start

# Development shortcuts
dev-backend:
	@echo "🐍 Starting backend development server..."
	@cd backend && source venv/bin/activate && uvicorn app.main:app --reload --host 0.0.0.0 --port 8080

dev-frontend:
	@echo "📱 Starting frontend development server..."
	@cd frontend && (command -v pnpm >/dev/null 2>&1 && pnpm dev) || (command -v yarn >/dev/null 2>&1 && yarn dev) || npm run dev

# Database operations
db-reset:
	@echo "🗄️ Resetting database..."
	@docker-compose -f docker-compose.dev.yml exec postgres psql -U context_user -d context_engine -c "DROP SCHEMA IF EXISTS context_engine CASCADE; DROP SCHEMA IF EXISTS monitoring CASCADE;"
	@docker-compose -f docker-compose.dev.yml exec postgres psql -U context_user -d context_engine -f /docker-entrypoint-initdb.d/init.sql

# Vector database operations
vector-reset:
	@echo "🔍 Resetting vector database..."
	@curl -X DELETE http://localhost:6333/collections/context_engine_main || true

# Show service URLs
urls:
	@echo "🔗 Service URLs:"
	@echo "Frontend:     http://localhost:5173"
	@echo "Backend API:  http://localhost:8080"
	@echo "API Docs:     http://localhost:8080/docs"
	@echo "Qdrant UI:    http://localhost:6333/dashboard"
	@echo "Prometheus:   http://localhost:9090"
	@echo "Grafana:      http://localhost:3000"

# Check OrbStack status
orbstack-status:
	@echo "📊 OrbStack Status:"
	@docker info | grep -E "(Server Version|Operating System|Total Memory|CPUs)"
	@echo ""
	@echo "📈 Container Stats:"
	@docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

# Backup data
backup:
	@echo "💾 Creating backup..."
	@mkdir -p backups
	@docker-compose -f docker-compose.dev.yml exec postgres pg_dump -U context_user context_engine > backups/postgres_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✅ Database backup created in backups/"

# Test environment
test:
	@echo "🧪 Running tests..."
	@cd backend && source venv/bin/activate && python -m pytest
	@cd frontend && (command -v pnpm >/dev/null 2>&1 && pnpm test) || (command -v yarn >/dev/null 2>&1 && yarn test) || npm test
