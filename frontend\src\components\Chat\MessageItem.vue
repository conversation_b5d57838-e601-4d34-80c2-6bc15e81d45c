<template>
  <div class="message-item" :class="{ 'user-message': message.role === 'user' }">
    <div class="message-avatar">
      <a-avatar v-if="message.role === 'user'" :size="32">
        <icon-user />
      </a-avatar>
      <a-avatar v-else :size="32" class="ai-avatar">
        <icon-robot />
      </a-avatar>
    </div>
    
    <div class="message-content">
      <div class="message-header">
        <span class="message-role">{{ message.role === 'user' ? '您' : 'AI 助手' }}</span>
        <span class="message-time">{{ formatTime(message.timestamp) }}</span>
      </div>
      
      <div  :class="{ 'message-body':true, 'user': message.role === 'user' ,'ai': message.role !== 'user' }">
        <div v-if="message.role === 'user'" class="user-content">
          {{ message.content }}
        </div>
        <div v-else class="ai-content">
          <MarkdownRenderer :content="message.content" @artifact-detected="handleArtifactDetected" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IconUser, IconRobot } from '@arco-design/web-vue/es/icon'
import MarkdownRenderer from './MarkdownRenderer.vue'
import type { Message } from '@/stores/chat'

interface Props {
  message: Message
}

defineProps<Props>()

const formatTime = (timestamp: Date) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const handleArtifactDetected = (artifact: any) => {
  // 处理检测到的 artifact
  console.log('Artifact detected:', artifact)
}
</script>

<style scoped>
.message-item {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.message-item.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
}

.ai-avatar {
  background: #165dff;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.user-message .message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.user-message .message-header {
  flex-direction: row-reverse;
}

.message-role {
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
}

.message-time {
  font-size: 12px;
  color: #86909c;
}

.message-body {
  max-width: 100%;
  /* display: flex;
  flex-direction: row-reverse; */
}
.message-body.user {
  display: flex;
  flex-direction: row-reverse;
  width: 90%;
}

.user-content {
  background: #165dff;
  color: white;
  padding: 12px 16px;
  border-radius: 12px;
  border-bottom-right-radius: 4px;
  word-wrap: break-word;
  white-space: pre-wrap;
  width: 100%;
}

.user-message .user-content {
  border-bottom-right-radius: 12px;
  border-bottom-left-radius: 4px;
}

.ai-content {
  background: #f7f8fa;
  padding: 16px;
  border-radius: 12px;
  border-bottom-left-radius: 4px;
  max-width: 85%;
}

.user-message .ai-content {
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-content,
  .ai-content {
    max-width: 90%;
  }
  
  .message-item {
    margin-bottom: 16px;
  }
}
</style>
