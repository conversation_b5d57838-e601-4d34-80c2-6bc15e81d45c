<template>
  <a-modal
    v-model:visible="visible"
    title="登录 Artifacts Chat"
    :footer="false"
    :mask-closable="false"
    width="400px"
  >
    <div class="login-form">
      <div class="login-header">
        <h2>欢迎回来</h2>
        <p>登录以保存您的聊天历史和 Artifacts</p>
      </div>
      
      <a-form
        :model="form"
        layout="vertical"
        @submit="handleSubmit"
      >
        <a-form-item
          label="邮箱"
          field="email"
          :rules="[
            { required: true, message: '请输入邮箱' },
            { type: 'email', message: '请输入有效的邮箱地址' }
          ]"
        >
          <a-input
            v-model="form.email"
            placeholder="请输入您的邮箱"
            size="large"
          >
            <template #prefix>
              <icon-user />
            </template>
          </a-input>
        </a-form-item>
        
        <a-form-item
          label="密码"
          field="password"
          :rules="[
            { required: true, message: '请输入密码' },
            { minLength: 6, message: '密码至少6位' }
          ]"
        >
          <a-input-password
            v-model="form.password"
            placeholder="请输入您的密码"
            size="large"
          >
            <template #prefix>
              <icon-lock />
            </template>
          </a-input-password>
        </a-form-item>
        
        <a-form-item>
          <a-checkbox v-model="form.remember">
            记住我
          </a-checkbox>
        </a-form-item>
        
        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            long
            size="large"
            :loading="authStore.isLoading"
          >
            登录
          </a-button>
        </a-form-item>
      </a-form>
      
      <div class="login-footer">
        <a-divider>或</a-divider>
        <a-button long size="large" @click="guestLogin">
          <template #icon>
            <icon-user />
          </template>
          以访客身份继续
        </a-button>
        
        <div class="login-links">
          <a href="#" @click.prevent="showRegister">注册账号</a>
          <a href="#" @click.prevent="showForgotPassword">忘记密码？</a>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { IconUser, IconLock } from '@arco-design/web-vue/es/icon'
import { Message } from '@arco-design/web-vue'
import { useAuthStore } from '@/stores/auth'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const authStore = useAuthStore()

const form = reactive({
  email: '<EMAIL>',
  password: '123456',
  remember: true
})

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const handleSubmit = async () => {
  try {
    const result = await authStore.login(form.email, form.password)
    if (result.success) {
      Message.success('登录成功！')
      visible.value = false
      emit('success')
    } else {
      Message.error(result.error || '登录失败')
    }
  } catch (error) {
    Message.error('登录失败，请稍后重试')
  }
}

const guestLogin = () => {
  // 设置访客用户
  authStore.setUser({
    id: 0,
    username: 'Guest',
    email: '<EMAIL>',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=guest'
  })
  authStore.setToken('guest_token')
  
  Message.success('以访客身份登录成功！')
  visible.value = false
  emit('success')
}

const showRegister = () => {
  Message.info('注册功能开发中...')
}

const showForgotPassword = () => {
  Message.info('密码重置功能开发中...')
}
</script>

<style scoped>
.login-form {
  padding: 20px 0;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-header h2 {
  margin: 0 0 8px 0;
  color: #1d2129;
  font-size: 24px;
  font-weight: 600;
}

.login-header p {
  margin: 0;
  color: #86909c;
  font-size: 14px;
}

.login-footer {
  margin-top: 24px;
}

.login-links {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
}

.login-links a {
  color: #165dff;
  text-decoration: none;
  font-size: 14px;
}

.login-links a:hover {
  text-decoration: underline;
}
</style>
