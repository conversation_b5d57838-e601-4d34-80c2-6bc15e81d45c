"""
上下文质量评估器

实现上下文内容的质量评估、过滤和排序机制。
"""
import re
import math
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

from app.services.context_engine.retrieval_engine import SearchResult

logger = logging.getLogger(__name__)

class QualityDimension(str, Enum):
    """质量评估维度"""
    COMPLETENESS = "completeness"      # 完整性
    ACCURACY = "accuracy"              # 准确性
    CLARITY = "clarity"                # 清晰度
    RELEVANCE = "relevance"            # 相关性
    FRESHNESS = "freshness"            # 时效性
    AUTHORITY = "authority"            # 权威性
    USABILITY = "usability"            # 可用性
    STRUCTURE = "structure"            # 结构性

@dataclass
class QualityScore:
    """质量评分"""
    overall: float
    dimensions: Dict[QualityDimension, float]
    confidence: float
    reasons: List[str]

@dataclass
class ContentFeatures:
    """内容特征"""
    # 基础特征
    length: int
    word_count: int
    sentence_count: int
    paragraph_count: int
    
    # 结构特征
    has_headers: bool
    has_code_blocks: bool
    has_lists: bool
    has_links: bool
    has_images: bool
    
    # 代码特征
    code_lines: int
    code_languages: List[str]
    has_comments: bool
    has_examples: bool
    
    # 语言特征
    avg_sentence_length: float
    readability_score: float
    technical_terms_count: int
    
    # 元数据特征
    has_title: bool
    has_description: bool
    has_tags: bool
    has_metadata: bool

class ContentAnalyzer:
    """内容分析器"""
    
    def __init__(self):
        # 技术术语词典
        self.technical_terms = {
            'frontend': ['component', 'props', 'state', 'hook', 'lifecycle', 'render', 'dom', 'css', 'html', 'javascript'],
            'backend': ['api', 'database', 'server', 'endpoint', 'middleware', 'authentication', 'authorization'],
            'general': ['function', 'method', 'class', 'object', 'variable', 'parameter', 'return', 'import', 'export']
        }
    
    def extract_features(self, content: str, metadata: Dict[str, Any] = None) -> ContentFeatures:
        """提取内容特征"""
        metadata = metadata or {}
        
        # 基础统计
        length = len(content)
        words = content.split()
        word_count = len(words)
        sentences = re.split(r'[.!?]+', content)
        sentence_count = len([s for s in sentences if s.strip()])
        paragraphs = content.split('\n\n')
        paragraph_count = len([p for p in paragraphs if p.strip()])
        
        # 结构检测
        has_headers = bool(re.search(r'^#+\s', content, re.MULTILINE))
        has_code_blocks = bool(re.search(r'```[\s\S]*?```', content))
        has_lists = bool(re.search(r'^\s*[-*+]\s', content, re.MULTILINE))
        has_links = bool(re.search(r'\[.*?\]\(.*?\)', content))
        has_images = bool(re.search(r'!\[.*?\]\(.*?\)', content))
        
        # 代码分析
        code_blocks = re.findall(r'```(\w+)?\n([\s\S]*?)```', content)
        code_lines = sum(len(code.split('\n')) for _, code in code_blocks)
        code_languages = [lang for lang, _ in code_blocks if lang]
        has_comments = bool(re.search(r'//|#|/\*|\*/', content))
        has_examples = bool(re.search(r'例子|示例|example|demo', content, re.IGNORECASE))
        
        # 语言特征
        avg_sentence_length = word_count / max(sentence_count, 1)
        readability_score = self._calculate_readability(content, word_count, sentence_count)
        technical_terms_count = self._count_technical_terms(content.lower())
        
        # 元数据特征
        has_title = bool(metadata.get('title'))
        has_description = bool(metadata.get('description'))
        has_tags = bool(metadata.get('tags'))
        has_metadata = bool(metadata)
        
        return ContentFeatures(
            length=length,
            word_count=word_count,
            sentence_count=sentence_count,
            paragraph_count=paragraph_count,
            has_headers=has_headers,
            has_code_blocks=has_code_blocks,
            has_lists=has_lists,
            has_links=has_links,
            has_images=has_images,
            code_lines=code_lines,
            code_languages=code_languages,
            has_comments=has_comments,
            has_examples=has_examples,
            avg_sentence_length=avg_sentence_length,
            readability_score=readability_score,
            technical_terms_count=technical_terms_count,
            has_title=has_title,
            has_description=has_description,
            has_tags=has_tags,
            has_metadata=has_metadata
        )
    
    def _calculate_readability(self, content: str, word_count: int, sentence_count: int) -> float:
        """计算可读性分数（简化版Flesch Reading Ease）"""
        if sentence_count == 0 or word_count == 0:
            return 0.0
        
        # 计算平均句长
        avg_sentence_length = word_count / sentence_count
        
        # 计算复杂词汇比例（简化：长度>6的词）
        words = content.split()
        complex_words = [w for w in words if len(w) > 6]
        complex_word_ratio = len(complex_words) / max(word_count, 1)
        
        # 简化的可读性公式
        readability = 100 - (1.015 * avg_sentence_length) - (84.6 * complex_word_ratio)
        return max(0, min(100, readability)) / 100  # 归一化到0-1
    
    def _count_technical_terms(self, content: str) -> int:
        """统计技术术语数量"""
        count = 0
        for category, terms in self.technical_terms.items():
            for term in terms:
                count += len(re.findall(r'\b' + re.escape(term) + r'\b', content))
        return count

class QualityEvaluator:
    """质量评估器"""
    
    def __init__(self):
        self.analyzer = ContentAnalyzer()
        
        # 质量评估权重
        self.dimension_weights = {
            QualityDimension.COMPLETENESS: 0.20,
            QualityDimension.ACCURACY: 0.15,
            QualityDimension.CLARITY: 0.15,
            QualityDimension.RELEVANCE: 0.15,
            QualityDimension.FRESHNESS: 0.10,
            QualityDimension.AUTHORITY: 0.10,
            QualityDimension.USABILITY: 0.10,
            QualityDimension.STRUCTURE: 0.05
        }
    
    def evaluate_quality(self, result: SearchResult, query: str = "") -> QualityScore:
        """评估内容质量"""
        features = self.analyzer.extract_features(result.content, result.metadata)
        
        # 计算各维度分数
        dimension_scores = {}
        reasons = []
        
        # 完整性评估
        completeness_score, completeness_reasons = self._evaluate_completeness(features)
        dimension_scores[QualityDimension.COMPLETENESS] = completeness_score
        reasons.extend(completeness_reasons)
        
        # 准确性评估
        accuracy_score, accuracy_reasons = self._evaluate_accuracy(features, result.metadata)
        dimension_scores[QualityDimension.ACCURACY] = accuracy_score
        reasons.extend(accuracy_reasons)
        
        # 清晰度评估
        clarity_score, clarity_reasons = self._evaluate_clarity(features)
        dimension_scores[QualityDimension.CLARITY] = clarity_score
        reasons.extend(clarity_reasons)
        
        # 相关性评估
        relevance_score, relevance_reasons = self._evaluate_relevance(result, query)
        dimension_scores[QualityDimension.RELEVANCE] = relevance_score
        reasons.extend(relevance_reasons)
        
        # 时效性评估
        freshness_score, freshness_reasons = self._evaluate_freshness(result.metadata)
        dimension_scores[QualityDimension.FRESHNESS] = freshness_score
        reasons.extend(freshness_reasons)
        
        # 权威性评估
        authority_score, authority_reasons = self._evaluate_authority(result.metadata)
        dimension_scores[QualityDimension.AUTHORITY] = authority_score
        reasons.extend(authority_reasons)
        
        # 可用性评估
        usability_score, usability_reasons = self._evaluate_usability(features)
        dimension_scores[QualityDimension.USABILITY] = usability_score
        reasons.extend(usability_reasons)
        
        # 结构性评估
        structure_score, structure_reasons = self._evaluate_structure(features)
        dimension_scores[QualityDimension.STRUCTURE] = structure_score
        reasons.extend(structure_reasons)
        
        # 计算总分
        overall_score = sum(
            score * self.dimension_weights[dim]
            for dim, score in dimension_scores.items()
        )
        
        # 计算置信度
        confidence = self._calculate_confidence(features, dimension_scores)
        
        return QualityScore(
            overall=overall_score,
            dimensions=dimension_scores,
            confidence=confidence,
            reasons=reasons
        )
    
    def _evaluate_completeness(self, features: ContentFeatures) -> Tuple[float, List[str]]:
        """评估完整性"""
        score = 0.0
        reasons = []
        
        # 内容长度
        if features.length > 500:
            score += 0.3
            reasons.append("内容长度充足")
        elif features.length > 200:
            score += 0.2
        
        # 结构完整性
        if features.has_headers:
            score += 0.2
            reasons.append("包含标题结构")
        
        if features.has_code_blocks and features.code_lines > 5:
            score += 0.2
            reasons.append("包含代码示例")
        
        if features.has_examples:
            score += 0.15
            reasons.append("包含实例说明")
        
        if features.has_metadata:
            score += 0.15
            reasons.append("元数据完整")
        
        return min(1.0, score), reasons
    
    def _evaluate_accuracy(self, features: ContentFeatures, metadata: Dict[str, Any]) -> Tuple[float, List[str]]:
        """评估准确性"""
        score = 0.5  # 基础分
        reasons = []
        
        # 技术术语密度
        if features.technical_terms_count > 5:
            score += 0.2
            reasons.append("技术术语丰富")
        
        # 代码质量
        if features.has_code_blocks and features.has_comments:
            score += 0.15
            reasons.append("代码包含注释")
        
        # 来源可信度
        source_type = metadata.get('source_type', '')
        if source_type in ['official_docs', 'tutorial', 'guide']:
            score += 0.15
            reasons.append("来源可信")
        
        return min(1.0, score), reasons
    
    def _evaluate_clarity(self, features: ContentFeatures) -> Tuple[float, List[str]]:
        """评估清晰度"""
        score = 0.0
        reasons = []
        
        # 可读性
        if features.readability_score > 0.6:
            score += 0.3
            reasons.append("可读性良好")
        elif features.readability_score > 0.4:
            score += 0.2
        
        # 结构清晰
        if features.has_headers and features.has_lists:
            score += 0.25
            reasons.append("结构清晰")
        
        # 句子长度适中
        if 10 <= features.avg_sentence_length <= 25:
            score += 0.2
            reasons.append("句子长度适中")
        
        # 段落组织
        if features.paragraph_count > 2:
            score += 0.25
            reasons.append("段落组织良好")
        
        return min(1.0, score), reasons
    
    def _evaluate_relevance(self, result: SearchResult, query: str) -> Tuple[float, List[str]]:
        """评估相关性"""
        score = result.score  # 使用检索分数作为基础
        reasons = []
        
        if score > 0.8:
            reasons.append("高度相关")
        elif score > 0.6:
            reasons.append("相关性良好")
        
        return score, reasons
    
    def _evaluate_freshness(self, metadata: Dict[str, Any]) -> Tuple[float, List[str]]:
        """评估时效性"""
        score = 0.7  # 默认分数
        reasons = []
        
        # 这里可以根据创建时间、更新时间等评估
        created_at = metadata.get('created_at')
        if created_at:
            # 简化处理，实际应该计算时间差
            score = 0.8
            reasons.append("有时间信息")
        
        return score, reasons
    
    def _evaluate_authority(self, metadata: Dict[str, Any]) -> Tuple[float, List[str]]:
        """评估权威性"""
        score = 0.5  # 基础分
        reasons = []
        
        # 来源权威性
        source = metadata.get('source', '').lower()
        if 'official' in source or 'docs' in source:
            score += 0.3
            reasons.append("官方文档")
        elif 'tutorial' in source or 'guide' in source:
            score += 0.2
            reasons.append("教程指南")
        
        # 作者信息
        if metadata.get('author'):
            score += 0.2
            reasons.append("有作者信息")
        
        return min(1.0, score), reasons
    
    def _evaluate_usability(self, features: ContentFeatures) -> Tuple[float, List[str]]:
        """评估可用性"""
        score = 0.0
        reasons = []
        
        # 代码可执行性
        if features.has_code_blocks:
            score += 0.4
            reasons.append("包含代码")
            
            if features.has_comments:
                score += 0.2
                reasons.append("代码有注释")
        
        # 实例和示例
        if features.has_examples:
            score += 0.3
            reasons.append("包含示例")
        
        # 链接和引用
        if features.has_links:
            score += 0.1
            reasons.append("包含相关链接")
        
        return min(1.0, score), reasons
    
    def _evaluate_structure(self, features: ContentFeatures) -> Tuple[float, List[str]]:
        """评估结构性"""
        score = 0.0
        reasons = []
        
        if features.has_headers:
            score += 0.4
            reasons.append("有标题结构")
        
        if features.has_lists:
            score += 0.3
            reasons.append("有列表组织")
        
        if features.paragraph_count > 1:
            score += 0.3
            reasons.append("段落分明")
        
        return min(1.0, score), reasons
    
    def _calculate_confidence(self, features: ContentFeatures, dimension_scores: Dict[QualityDimension, float]) -> float:
        """计算评估置信度"""
        confidence = 0.5  # 基础置信度
        
        # 内容长度影响置信度
        if features.length > 1000:
            confidence += 0.2
        elif features.length > 500:
            confidence += 0.1
        
        # 结构完整性影响置信度
        if features.has_metadata:
            confidence += 0.1
        
        if features.has_code_blocks:
            confidence += 0.1
        
        # 评分一致性影响置信度
        scores = list(dimension_scores.values())
        if scores:
            score_variance = sum((s - sum(scores)/len(scores))**2 for s in scores) / len(scores)
            if score_variance < 0.1:  # 分数比较一致
                confidence += 0.1
        
        return min(1.0, confidence)

class QualityFilter:
    """质量过滤器"""
    
    def __init__(self, min_quality_score: float = 0.5):
        self.min_quality_score = min_quality_score
        self.evaluator = QualityEvaluator()
    
    def filter_results(self, results: List[SearchResult], query: str = "") -> List[SearchResult]:
        """过滤低质量结果"""
        filtered_results = []
        
        for result in results:
            quality_score = self.evaluator.evaluate_quality(result, query)
            
            # 添加质量信息到结果中
            result.metadata['quality_score'] = quality_score.overall
            result.metadata['quality_dimensions'] = {
                dim.value: score for dim, score in quality_score.dimensions.items()
            }
            result.metadata['quality_confidence'] = quality_score.confidence
            result.metadata['quality_reasons'] = quality_score.reasons
            
            # 过滤低质量内容
            if quality_score.overall >= self.min_quality_score:
                filtered_results.append(result)
            else:
                logger.debug(f"Filtered low quality result: {result.title} (score: {quality_score.overall:.2f})")
        
        # 按质量分数排序
        filtered_results.sort(key=lambda x: x.metadata['quality_score'], reverse=True)
        
        return filtered_results

# 全局质量评估器实例
quality_evaluator = QualityEvaluator()
quality_filter = QualityFilter()
