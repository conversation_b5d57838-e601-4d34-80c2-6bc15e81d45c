"""
错误处理与重试机制

提供统一的错误处理、重试策略和故障恢复机制。
"""
import asyncio
import time
import random
from typing import Any, Callable, Dict, List, Optional, Type, Union
from dataclasses import dataclass
from enum import Enum
import logging
from functools import wraps

logger = logging.getLogger(__name__)

class ErrorType(str, Enum):
    """错误类型枚举"""
    NETWORK_ERROR = "network_error"
    TIMEOUT_ERROR = "timeout_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    AUTHENTICATION_ERROR = "auth_error"
    VALIDATION_ERROR = "validation_error"
    INTERNAL_ERROR = "internal_error"
    EXTERNAL_API_ERROR = "external_api_error"
    DATABASE_ERROR = "database_error"
    VECTOR_STORE_ERROR = "vector_store_error"

@dataclass
class RetryConfig:
    """重试配置"""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    retryable_errors: List[ErrorType] = None
    
    def __post_init__(self):
        if self.retryable_errors is None:
            self.retryable_errors = [
                ErrorType.NETWORK_ERROR,
                ErrorType.TIMEOUT_ERROR,
                ErrorType.RATE_LIMIT_ERROR,
                ErrorType.INTERNAL_ERROR
            ]

@dataclass
class ErrorContext:
    """错误上下文信息"""
    error_type: ErrorType
    original_error: Exception
    attempt: int
    total_attempts: int
    elapsed_time: float
    function_name: str
    args: tuple
    kwargs: dict

class CircuitBreakerState(str, Enum):
    """熔断器状态"""
    CLOSED = "closed"      # 正常状态
    OPEN = "open"          # 熔断状态
    HALF_OPEN = "half_open"  # 半开状态

@dataclass
class CircuitBreakerConfig:
    """熔断器配置"""
    failure_threshold: int = 5      # 失败阈值
    recovery_timeout: float = 60.0  # 恢复超时时间
    expected_exception: Type[Exception] = Exception

class CircuitBreaker:
    """熔断器实现"""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.last_failure_time = 0
        self.success_count = 0
    
    async def call(self, func: Callable, *args, **kwargs):
        """通过熔断器调用函数"""
        if self.state == CircuitBreakerState.OPEN:
            if time.time() - self.last_failure_time > self.config.recovery_timeout:
                self.state = CircuitBreakerState.HALF_OPEN
                self.success_count = 0
            else:
                raise CircuitBreakerOpenError("Circuit breaker is open")
        
        try:
            result = await func(*args, **kwargs)
            self._on_success()
            return result
        except self.config.expected_exception as e:
            self._on_failure()
            raise e
    
    def _on_success(self):
        """成功回调"""
        if self.state == CircuitBreakerState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= 3:  # 连续3次成功则关闭熔断器
                self.state = CircuitBreakerState.CLOSED
                self.failure_count = 0
        else:
            self.failure_count = 0
    
    def _on_failure(self):
        """失败回调"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.config.failure_threshold:
            self.state = CircuitBreakerState.OPEN

class CustomException(Exception):
    """自定义异常基类"""
    
    def __init__(self, message: str, error_type: ErrorType, details: Dict[str, Any] = None):
        super().__init__(message)
        self.error_type = error_type
        self.details = details or {}
        self.timestamp = time.time()

class NetworkError(CustomException):
    """网络错误"""
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, ErrorType.NETWORK_ERROR, details)

class TimeoutError(CustomException):
    """超时错误"""
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, ErrorType.TIMEOUT_ERROR, details)

class RateLimitError(CustomException):
    """限流错误"""
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, ErrorType.RATE_LIMIT_ERROR, details)

class CircuitBreakerOpenError(CustomException):
    """熔断器开启错误"""
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, ErrorType.INTERNAL_ERROR, details)

class RetryableError(CustomException):
    """可重试错误"""
    def __init__(self, message: str, error_type: ErrorType, details: Dict[str, Any] = None):
        super().__init__(message, error_type, details)

class ErrorHandler:
    """统一错误处理器"""
    
    def __init__(self):
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.error_stats: Dict[str, Dict[str, int]] = {}
    
    def get_circuit_breaker(self, name: str, config: CircuitBreakerConfig = None) -> CircuitBreaker:
        """获取或创建熔断器"""
        if name not in self.circuit_breakers:
            config = config or CircuitBreakerConfig()
            self.circuit_breakers[name] = CircuitBreaker(config)
        return self.circuit_breakers[name]
    
    def classify_error(self, error: Exception) -> ErrorType:
        """错误分类"""
        error_name = type(error).__name__.lower()
        
        if isinstance(error, CustomException):
            return error.error_type
        elif 'timeout' in error_name or 'timeouterror' in error_name:
            return ErrorType.TIMEOUT_ERROR
        elif 'connection' in error_name or 'network' in error_name:
            return ErrorType.NETWORK_ERROR
        elif 'ratelimit' in error_name or '429' in str(error):
            return ErrorType.RATE_LIMIT_ERROR
        elif 'auth' in error_name or '401' in str(error) or '403' in str(error):
            return ErrorType.AUTHENTICATION_ERROR
        elif 'validation' in error_name or '400' in str(error):
            return ErrorType.VALIDATION_ERROR
        elif 'database' in error_name or 'sql' in error_name:
            return ErrorType.DATABASE_ERROR
        else:
            return ErrorType.INTERNAL_ERROR
    
    def is_retryable(self, error: Exception, retryable_errors: List[ErrorType]) -> bool:
        """判断错误是否可重试"""
        error_type = self.classify_error(error)
        return error_type in retryable_errors
    
    def calculate_delay(self, attempt: int, config: RetryConfig) -> float:
        """计算重试延迟"""
        delay = config.base_delay * (config.exponential_base ** (attempt - 1))
        delay = min(delay, config.max_delay)
        
        if config.jitter:
            # 添加随机抖动，避免雷群效应
            jitter = random.uniform(0.1, 0.3) * delay
            delay += jitter
        
        return delay
    
    async def record_error(self, error_context: ErrorContext):
        """记录错误统计"""
        func_name = error_context.function_name
        error_type = error_context.error_type.value
        
        if func_name not in self.error_stats:
            self.error_stats[func_name] = {}
        
        if error_type not in self.error_stats[func_name]:
            self.error_stats[func_name][error_type] = 0
        
        self.error_stats[func_name][error_type] += 1
        
        logger.error(
            f"Error in {func_name}: {error_context.original_error}",
            extra={
                "error_type": error_type,
                "attempt": error_context.attempt,
                "total_attempts": error_context.total_attempts,
                "elapsed_time": error_context.elapsed_time
            }
        )
    
    def get_error_stats(self) -> Dict[str, Dict[str, int]]:
        """获取错误统计"""
        return self.error_stats.copy()

# 全局错误处理器实例
error_handler = ErrorHandler()

def retry_async(config: RetryConfig = None):
    """异步重试装饰器"""
    if config is None:
        config = RetryConfig()
    
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            last_error = None
            
            for attempt in range(1, config.max_attempts + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_error = e
                    error_type = error_handler.classify_error(e)
                    
                    # 记录错误上下文
                    error_context = ErrorContext(
                        error_type=error_type,
                        original_error=e,
                        attempt=attempt,
                        total_attempts=config.max_attempts,
                        elapsed_time=time.time() - start_time,
                        function_name=func.__name__,
                        args=args,
                        kwargs=kwargs
                    )
                    
                    await error_handler.record_error(error_context)
                    
                    # 检查是否可重试
                    if not error_handler.is_retryable(e, config.retryable_errors):
                        logger.warning(f"Non-retryable error in {func.__name__}: {e}")
                        raise e
                    
                    # 最后一次尝试，不再重试
                    if attempt == config.max_attempts:
                        logger.error(f"Max retry attempts reached for {func.__name__}")
                        break
                    
                    # 计算延迟并等待
                    delay = error_handler.calculate_delay(attempt, config)
                    logger.info(f"Retrying {func.__name__} in {delay:.2f}s (attempt {attempt}/{config.max_attempts})")
                    await asyncio.sleep(delay)
            
            # 所有重试都失败了
            raise last_error
        
        return wrapper
    return decorator

def circuit_breaker(name: str, config: CircuitBreakerConfig = None):
    """熔断器装饰器"""
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            breaker = error_handler.get_circuit_breaker(name, config)
            return await breaker.call(func, *args, **kwargs)
        return wrapper
    return decorator

def handle_errors(retryable_errors: List[ErrorType] = None):
    """错误处理装饰器"""
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                error_type = error_handler.classify_error(e)
                
                # 转换为自定义异常
                if not isinstance(e, CustomException):
                    if retryable_errors and error_type in retryable_errors:
                        raise RetryableError(str(e), error_type)
                    else:
                        raise CustomException(str(e), error_type)
                
                raise e
        return wrapper
    return decorator

# 预定义的重试配置
NETWORK_RETRY_CONFIG = RetryConfig(
    max_attempts=3,
    base_delay=1.0,
    max_delay=10.0,
    retryable_errors=[ErrorType.NETWORK_ERROR, ErrorType.TIMEOUT_ERROR]
)

API_RETRY_CONFIG = RetryConfig(
    max_attempts=5,
    base_delay=2.0,
    max_delay=30.0,
    retryable_errors=[
        ErrorType.NETWORK_ERROR,
        ErrorType.TIMEOUT_ERROR,
        ErrorType.RATE_LIMIT_ERROR,
        ErrorType.EXTERNAL_API_ERROR
    ]
)

DATABASE_RETRY_CONFIG = RetryConfig(
    max_attempts=3,
    base_delay=0.5,
    max_delay=5.0,
    retryable_errors=[ErrorType.DATABASE_ERROR, ErrorType.NETWORK_ERROR]
)

# 预定义的熔断器配置
API_CIRCUIT_BREAKER_CONFIG = CircuitBreakerConfig(
    failure_threshold=5,
    recovery_timeout=60.0,
    expected_exception=Exception
)

DATABASE_CIRCUIT_BREAKER_CONFIG = CircuitBreakerConfig(
    failure_threshold=3,
    recovery_timeout=30.0,
    expected_exception=Exception
)
