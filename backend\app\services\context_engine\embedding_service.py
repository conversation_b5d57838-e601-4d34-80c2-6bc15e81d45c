"""
Embedding Service for Context Engine

Handles text embedding generation using various models.
"""
import asyncio
import hashlib
from typing import List, Dict, Any, Optional, Union
from functools import lru_cache
import numpy as np

from sentence_transformers import SentenceTransformer
import torch

from app.core.context_config import context_settings, EmbeddingModel, MODEL_DIMENSIONS
import structlog

logger = structlog.get_logger(__name__)

class EmbeddingService:
    """Service for generating text embeddings"""
    
    def __init__(self):
        self.model_name = context_settings.EMBEDDING_MODEL.value
        self.dimension = context_settings.EMBEDDING_DIMENSION
        self.device = context_settings.EMBEDDING_DEVICE
        self.batch_size = context_settings.EMBEDDING_BATCH_SIZE
        
        # Initialize model
        self._model = None
        self._model_loaded = False
        
        logger.info(
            "Initializing EmbeddingService",
            model=self.model_name,
            dimension=self.dimension,
            device=self.device
        )
    
    async def _load_model(self) -> SentenceTransformer:
        """Load the embedding model lazily"""
        if not self._model_loaded:
            try:
                logger.info("Loading embedding model", model=self.model_name)
                
                # Load model in thread to avoid blocking
                self._model = await asyncio.to_thread(
                    SentenceTransformer,
                    self.model_name,
                    device=self.device
                )
                
                # Verify model dimension
                test_embedding = self._model.encode(["test"], convert_to_numpy=True)
                actual_dimension = test_embedding.shape[1]
                
                if actual_dimension != self.dimension:
                    logger.warning(
                        "Model dimension mismatch",
                        expected=self.dimension,
                        actual=actual_dimension
                    )
                    self.dimension = actual_dimension
                
                self._model_loaded = True
                logger.info("Embedding model loaded successfully", dimension=self.dimension)
                
            except Exception as e:
                logger.error("Failed to load embedding model", error=str(e))
                raise
        
        return self._model
    
    async def embed_text(self, text: str) -> List[float]:
        """Generate embedding for a single text"""
        embeddings = await self.embed_texts([text])
        return embeddings[0]
    
    async def embed_texts(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple texts"""
        if not texts:
            return []
        
        try:
            model = await self._load_model()
            
            # Process in batches to manage memory
            all_embeddings = []
            
            for i in range(0, len(texts), self.batch_size):
                batch = texts[i:i + self.batch_size]
                
                # Generate embeddings in thread
                batch_embeddings = await asyncio.to_thread(
                    model.encode,
                    batch,
                    convert_to_numpy=True,
                    normalize_embeddings=True,
                    show_progress_bar=False
                )
                
                # Convert to list of lists
                batch_embeddings_list = [
                    embedding.tolist() for embedding in batch_embeddings
                ]
                all_embeddings.extend(batch_embeddings_list)
            
            logger.info("Generated embeddings", count=len(texts), dimension=self.dimension)
            return all_embeddings
            
        except Exception as e:
            logger.error("Failed to generate embeddings", error=str(e), count=len(texts))
            raise
    
    async def embed_documents(self, documents: List[Dict[str, Any]]) -> List[List[float]]:
        """Generate embeddings for documents with content field"""
        texts = []
        for doc in documents:
            # Extract text content from document
            content = doc.get('content', '')
            title = doc.get('title', '')
            
            # Combine title and content for better embeddings
            if title and content:
                text = f"{title}\n\n{content}"
            elif title:
                text = title
            elif content:
                text = content
            else:
                text = str(doc)  # Fallback to string representation
            
            texts.append(text)
        
        return await self.embed_texts(texts)
    
    def get_embedding_hash(self, text: str) -> str:
        """Generate a hash for text to cache embeddings"""
        content = f"{self.model_name}:{text}"
        return hashlib.md5(content.encode()).hexdigest()
    
    async def compute_similarity(
        self, 
        embedding1: List[float], 
        embedding2: List[float]
    ) -> float:
        """Compute cosine similarity between two embeddings"""
        try:
            # Convert to numpy arrays
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)
            
            # Compute cosine similarity
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
            
        except Exception as e:
            logger.error("Failed to compute similarity", error=str(e))
            return 0.0
    
    async def find_most_similar(
        self, 
        query_embedding: List[float], 
        candidate_embeddings: List[List[float]],
        top_k: int = 5
    ) -> List[Tuple[int, float]]:
        """Find most similar embeddings from candidates"""
        try:
            similarities = []
            
            for i, candidate in enumerate(candidate_embeddings):
                similarity = await self.compute_similarity(query_embedding, candidate)
                similarities.append((i, similarity))
            
            # Sort by similarity (descending)
            similarities.sort(key=lambda x: x[1], reverse=True)
            
            return similarities[:top_k]
            
        except Exception as e:
            logger.error("Failed to find similar embeddings", error=str(e))
            return []
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model"""
        return {
            "model_name": self.model_name,
            "dimension": self.dimension,
            "device": self.device,
            "batch_size": self.batch_size,
            "loaded": self._model_loaded
        }

# Cache for embedding service instance
@lru_cache(maxsize=1)
def get_embedding_service() -> EmbeddingService:
    """Get singleton embedding service instance"""
    return EmbeddingService()

# Utility functions for different embedding models
async def get_model_for_language(language: str = "en") -> EmbeddingService:
    """Get appropriate embedding model for language"""
    # For now, return default service
    # In future, could switch models based on language
    return get_embedding_service()

async def embed_code_snippet(
    code: str, 
    language: str, 
    description: Optional[str] = None
) -> List[float]:
    """Generate embedding for code snippet with context"""
    service = get_embedding_service()
    
    # Create enhanced text for code embedding
    text_parts = []
    
    if description:
        text_parts.append(f"Description: {description}")
    
    text_parts.append(f"Language: {language}")
    text_parts.append(f"Code:\n{code}")
    
    combined_text = "\n\n".join(text_parts)
    return await service.embed_text(combined_text)

async def embed_documentation(
    title: str,
    content: str,
    category: Optional[str] = None,
    tags: Optional[List[str]] = None
) -> List[float]:
    """Generate embedding for documentation with metadata"""
    service = get_embedding_service()
    
    # Create enhanced text for documentation embedding
    text_parts = [title]
    
    if category:
        text_parts.append(f"Category: {category}")
    
    if tags:
        text_parts.append(f"Tags: {', '.join(tags)}")
    
    text_parts.append(content)
    
    combined_text = "\n\n".join(text_parts)
    return await service.embed_text(combined_text)
