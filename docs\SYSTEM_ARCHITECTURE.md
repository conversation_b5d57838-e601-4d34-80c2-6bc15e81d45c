# 🏗️ 智能上下文引擎系统架构设计

## 概述

智能上下文引擎采用微服务架构设计，通过模块化组件实现高可用、可扩展的AI知识管理系统。

## 🎯 设计原则

### 1. 模块化设计
- **单一职责** - 每个组件专注于特定功能
- **松耦合** - 组件间通过标准接口通信
- **高内聚** - 相关功能集中在同一模块

### 2. 可扩展性
- **水平扩展** - 支持多实例部署
- **插件化** - 支持新的文档处理器和检索算法
- **配置驱动** - 通过配置文件调整系统行为

### 3. 高可用性
- **故障隔离** - 单个组件故障不影响整体系统
- **自动恢复** - 支持自动重试和故障转移
- **监控告警** - 完整的监控和日志系统

## 🏛️ 整体架构

```mermaid
graph TB
    subgraph "前端层"
        A[Vue.js 前端应用]
        B[智能搜索界面]
        C[聊天对话界面]
    end
    
    subgraph "API网关层"
        D[FastAPI 网关]
        E[认证中间件]
        F[限流中间件]
    end
    
    subgraph "业务逻辑层"
        G[智能上下文引擎]
        H[聊天管理服务]
        I[知识库管理服务]
    end
    
    subgraph "AI服务层"
        J[LLM路由器]
        K[OpenAI适配器]
        L[Claude适配器]
        M[提示词优化器]
    end
    
    subgraph "数据处理层"
        N[文档处理器]
        O[向量化服务]
        P[检索引擎]
        Q[排序算法]
    end
    
    subgraph "存储层"
        R[PostgreSQL]
        S[Qdrant向量库]
        T[Redis缓存]
        U[文件存储]
    end
    
    A --> D
    B --> D
    C --> D
    D --> G
    D --> H
    D --> I
    G --> J
    G --> P
    J --> K
    J --> L
    J --> M
    I --> N
    N --> O
    O --> S
    P --> S
    P --> T
    H --> R
    I --> R
    N --> U
```

## 🔧 核心组件设计

### 1. 智能上下文引擎 (IntelligentContextEngine)

```python
class IntelligentContextEngine:
    """智能上下文引擎主控制器"""
    
    def __init__(self):
        self.retriever = HybridRetriever()
        self.ranker = ContextRanker()
        self.prompt_builder = PromptBuilder()
        self.query_processor = QueryProcessor()
    
    async def get_context(self, request: ContextRequest) -> ContextResponse:
        """获取智能上下文"""
        # 1. 查询预处理
        processed_query = await self.query_processor.process(request.query)
        
        # 2. 混合检索
        search_results = await self.retriever.search(processed_query)
        
        # 3. 智能排序
        ranked_results = await self.ranker.rank(search_results, request)
        
        # 4. 构建提示词
        prompt, metadata = await self.prompt_builder.build(
            request.query, ranked_results
        )
        
        return ContextResponse(
            contexts=ranked_results,
            prompt=prompt,
            metadata=metadata
        )
```

### 2. 混合检索引擎 (HybridRetriever)

```python
class HybridRetriever:
    """混合检索引擎"""
    
    def __init__(self):
        self.vector_store = VectorStoreManager()
        self.keyword_engine = KeywordSearchEngine()
        self.embedding_service = EmbeddingService()
    
    async def search(self, query: ProcessedQuery) -> List[SearchResult]:
        """执行混合检索"""
        # 并行执行向量搜索和关键词搜索
        vector_task = self._vector_search(query)
        keyword_task = self._keyword_search(query)
        
        vector_results, keyword_results = await asyncio.gather(
            vector_task, keyword_task
        )
        
        # 合并和去重
        combined_results = self._merge_results(vector_results, keyword_results)
        
        return combined_results
    
    async def _vector_search(self, query: ProcessedQuery) -> List[SearchResult]:
        """向量相似度搜索"""
        embedding = await self.embedding_service.embed_text(query.text)
        return await self.vector_store.search_similar(embedding, limit=20)
    
    async def _keyword_search(self, query: ProcessedQuery) -> List[SearchResult]:
        """关键词搜索"""
        return await self.keyword_engine.search(query.keywords, limit=20)
```

### 3. 上下文排序器 (ContextRanker)

```python
class ContextRanker:
    """上下文智能排序器"""
    
    def __init__(self):
        self.quality_analyzer = ContentQualityAnalyzer()
        self.relevance_calculator = RelevanceCalculator()
        self.weights = RankingWeights()
    
    async def rank(
        self, 
        results: List[SearchResult], 
        request: ContextRequest
    ) -> List[SearchResult]:
        """智能排序搜索结果"""
        
        for result in results:
            # 计算多维度评分
            features = await self._extract_features(result, request)
            
            # 加权计算最终分数
            final_score = self._calculate_weighted_score(features)
            
            result.score = final_score
            result.ranking_features = features
        
        # 按分数排序
        return sorted(results, key=lambda x: x.score, reverse=True)
    
    async def _extract_features(
        self, 
        result: SearchResult, 
        request: ContextRequest
    ) -> RankingFeatures:
        """提取排序特征"""
        return RankingFeatures(
            semantic_similarity=result.similarity_score,
            keyword_relevance=self._calc_keyword_relevance(result, request),
            content_quality=await self.quality_analyzer.analyze(result.content),
            recency=self._calc_recency(result.metadata),
            popularity=self._calc_popularity(result.metadata),
            completeness=self._calc_completeness(result)
        )
```

### 4. LLM统一路由器 (UnifiedLLMRouter)

```python
class UnifiedLLMRouter:
    """LLM统一路由器"""
    
    def __init__(self):
        self.adapters = {
            LLMProvider.OPENAI: OpenAIAdapter(),
            LLMProvider.CLAUDE: ClaudeAdapter()
        }
        self.health_checker = HealthChecker()
        self.load_balancer = LoadBalancer()
    
    async def chat_completion(
        self, 
        request: ChatRequest
    ) -> Union[ChatResponse, AsyncGenerator]:
        """智能路由聊天请求"""
        
        # 1. 选择最优提供商
        provider = await self._select_provider(request)
        
        # 2. 获取适配器
        adapter = self.adapters[provider]
        
        # 3. 执行请求（带重试）
        try:
            return await self._execute_with_retry(adapter, request)
        except Exception as e:
            # 4. 故障转移
            return await self._fallback_execution(request, provider, e)
    
    async def _select_provider(self, request: ChatRequest) -> LLMProvider:
        """智能选择LLM提供商"""
        # 检查健康状态
        health_status = await self.health_checker.check_all()
        
        # 根据模型类型、负载、响应时间等选择
        if request.model.startswith('gpt') and health_status['openai']:
            return LLMProvider.OPENAI
        elif request.model.startswith('claude') and health_status['claude']:
            return LLMProvider.CLAUDE
        else:
            # 选择可用的提供商
            return self.load_balancer.select_available(health_status)
```

## 📊 数据流设计

### 1. 查询处理流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant G as API网关
    participant E as 上下文引擎
    participant R as 检索引擎
    participant L as LLM路由器
    participant A as LLM适配器
    
    U->>F: 输入查询
    F->>G: POST /api/enhanced-chat/completions
    G->>E: 请求上下文增强
    E->>R: 执行混合检索
    R-->>E: 返回搜索结果
    E->>E: 智能排序和压缩
    E-->>G: 返回增强提示词
    G->>L: 路由到LLM
    L->>A: 调用具体适配器
    A-->>L: 返回AI回答
    L-->>G: 返回结果
    G-->>F: 流式响应
    F-->>U: 显示回答
```

### 2. 知识库更新流程

```mermaid
sequenceDiagram
    participant A as 管理员
    participant K as 知识库服务
    participant P as 文档处理器
    participant V as 向量化服务
    participant S as 向量存储
    participant D as 数据库
    
    A->>K: 上传文档
    K->>P: 解析文档
    P->>P: 智能分块
    P-->>K: 返回文档块
    K->>V: 生成向量
    V-->>K: 返回嵌入向量
    K->>S: 存储向量
    K->>D: 存储元数据
    K-->>A: 返回处理结果
```

## 🔌 接口设计

### 1. 核心接口定义

```python
# 上下文请求接口
class ContextRequest(BaseModel):
    query: str
    conversation_history: Optional[List[Dict[str, str]]] = None
    max_results: int = 10
    include_code: bool = True
    include_docs: bool = True
    frameworks: Optional[List[str]] = None
    languages: Optional[List[str]] = None

# 上下文响应接口
class ContextResponse(BaseModel):
    query: str
    contexts: List[SearchResult]
    total_found: int
    processing_time_ms: int
    suggestions: List[str]
    metadata: Dict[str, Any]

# 搜索结果接口
class SearchResult(BaseModel):
    id: str
    content: str
    title: str
    score: float
    source_type: str
    metadata: Dict[str, Any]
    ranking_features: Optional[Dict[str, float]] = None
```

### 2. 服务间通信接口

```python
# 检索引擎接口
class IRetrievalEngine(ABC):
    @abstractmethod
    async def search(self, query: str, filters: Dict[str, Any]) -> List[SearchResult]:
        pass

# 排序引擎接口
class IRankingEngine(ABC):
    @abstractmethod
    async def rank(self, results: List[SearchResult], context: Dict[str, Any]) -> List[SearchResult]:
        pass

# LLM适配器接口
class ILLMAdapter(ABC):
    @abstractmethod
    async def chat_completion(self, request: ChatRequest) -> Union[ChatResponse, AsyncGenerator]:
        pass
    
    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        pass
```

## 🗄️ 数据模型设计

### 1. 知识库数据模型

```sql
-- 集合表
CREATE TABLE collections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    category VARCHAR(100),
    subcategory VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 文档表
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    collection_id UUID REFERENCES collections(id),
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    file_path VARCHAR(1000),
    file_type VARCHAR(50),
    file_size INTEGER,
    language VARCHAR(50),
    framework VARCHAR(100),
    tags TEXT[], -- PostgreSQL数组类型
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 代码示例表
CREATE TABLE code_examples (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    collection_id UUID REFERENCES collections(id),
    title VARCHAR(500) NOT NULL,
    description TEXT,
    code TEXT NOT NULL,
    language VARCHAR(50) NOT NULL,
    framework VARCHAR(100),
    category VARCHAR(100),
    tags TEXT[],
    dependencies TEXT[],
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 向量索引表
CREATE TABLE vector_embeddings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID,
    code_example_id UUID,
    chunk_index INTEGER,
    embedding_vector VECTOR(384), -- 使用pgvector扩展
    content_hash VARCHAR(64),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_document FOREIGN KEY (document_id) REFERENCES documents(id),
    CONSTRAINT fk_code_example FOREIGN KEY (code_example_id) REFERENCES code_examples(id)
);
```

### 2. 系统监控数据模型

```sql
-- 查询日志表
CREATE TABLE query_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255),
    session_id VARCHAR(255),
    query_text TEXT NOT NULL,
    query_type VARCHAR(50),
    processing_time_ms INTEGER,
    results_count INTEGER,
    context_used BOOLEAN DEFAULT FALSE,
    satisfaction_score FLOAT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 系统指标表
CREATE TABLE system_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metric_name VARCHAR(100) NOT NULL,
    metric_value FLOAT NOT NULL,
    metric_unit VARCHAR(50),
    tags JSONB,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🚀 部署架构

### 1. 容器化部署

```yaml
# docker-compose.yml
version: '3.8'
services:
  # 前端服务
  frontend:
    build: ./frontend
    ports: ["3000:3000"]
    depends_on: [backend]
    
  # 后端API服务
  backend:
    build: ./backend
    ports: ["8080:8080"]
    environment:
      - DATABASE_URL=********************************************/artifacts_chat
      - QDRANT_URL=http://qdrant:6333
      - REDIS_URL=redis://redis:6379/0
    depends_on: [postgres, qdrant, redis]
    
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: artifacts_chat
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    
  # Qdrant向量数据库
  qdrant:
    image: qdrant/qdrant:latest
    ports: ["6333:6333"]
    volumes:
      - qdrant_data:/qdrant/storage
    
  # Redis缓存
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
```

### 2. 生产环境架构

```mermaid
graph TB
    subgraph "负载均衡层"
        LB[Nginx/HAProxy]
    end
    
    subgraph "应用层"
        API1[API服务实例1]
        API2[API服务实例2]
        API3[API服务实例3]
    end
    
    subgraph "数据层"
        PG[(PostgreSQL主从)]
        QD[(Qdrant集群)]
        RD[(Redis集群)]
    end
    
    subgraph "监控层"
        PROM[Prometheus]
        GRAF[Grafana]
        ALERT[AlertManager]
    end
    
    LB --> API1
    LB --> API2
    LB --> API3
    
    API1 --> PG
    API1 --> QD
    API1 --> RD
    
    API2 --> PG
    API2 --> QD
    API2 --> RD
    
    API3 --> PG
    API3 --> QD
    API3 --> RD
    
    API1 --> PROM
    API2 --> PROM
    API3 --> PROM
    
    PROM --> GRAF
    PROM --> ALERT
```

## 📈 性能优化策略

### 1. 缓存策略
- **查询结果缓存** - Redis缓存常见查询结果
- **向量缓存** - 缓存计算好的嵌入向量
- **模板缓存** - 缓存渲染好的提示词模板

### 2. 并发优化
- **异步处理** - 全面使用async/await
- **连接池** - 数据库和HTTP连接池
- **批量操作** - 批量向量化和存储

### 3. 资源管理
- **内存监控** - 监控内存使用情况
- **磁盘管理** - 定期清理临时文件
- **网络优化** - 压缩响应和请求

## 🔒 安全设计

### 1. 认证授权
- **JWT Token** - 无状态认证
- **RBAC权限** - 基于角色的访问控制
- **API限流** - 防止滥用

### 2. 数据安全
- **数据加密** - 敏感数据加密存储
- **传输安全** - HTTPS/TLS加密
- **输入验证** - 严格的输入验证

### 3. 系统安全
- **沙箱隔离** - 代码执行沙箱
- **依赖扫描** - 定期扫描安全漏洞
- **访问日志** - 完整的访问日志记录

---

**智能上下文引擎系统架构** - 构建可扩展、高可用的AI知识管理平台 🏗️
