"""
Artifact-related Pydantic schemas
"""
from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class ArtifactBase(BaseModel):
    """Base artifact schema"""
    type: str  # "html", "vue", "g2plot", etc.
    code: str
    title: Optional[str] = "Artifact"

class ArtifactCreate(ArtifactBase):
    """Schema for creating an artifact"""
    message_id: int

class ArtifactResponse(ArtifactBase):
    """Schema for artifact response"""
    id: int
    message_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True
