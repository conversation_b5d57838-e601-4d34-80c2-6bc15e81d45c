#!/bin/bash

# 停止 Artifacts Chat 开发环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${YELLOW}🛑 停止 Artifacts Chat 开发环境${NC}"
echo "=================================================="

# 停止开发服务器
stop_dev_servers() {
    echo -e "${YELLOW}🔌 停止开发服务器...${NC}"
    
    # 停止后端服务器
    if [ -f .backend.pid ]; then
        echo "停止后端服务器..."
        kill $(cat .backend.pid) 2>/dev/null || true
        rm .backend.pid
        echo -e "${GREEN}✅ 后端服务器已停止${NC}"
    fi
    
    # 停止前端服务器
    if [ -f .frontend.pid ]; then
        echo "停止前端服务器..."
        kill $(cat .frontend.pid) 2>/dev/null || true
        rm .frontend.pid
        echo -e "${GREEN}✅ 前端服务器已停止${NC}"
    fi
    
    # 清理可能残留的进程
    pkill -f "uvicorn app.main:app" 2>/dev/null || true
    pkill -f "vite" 2>/dev/null || true
    
    # 清理日志文件
    rm -f backend.log frontend.log
}

# 显示最终状态
show_final_status() {
    echo ""
    echo -e "${GREEN}✅ 所有服务已停止${NC}"
    echo ""
    echo -e "${YELLOW}💡 提示:${NC}"
    echo "• 重新启动: ./scripts/start-dev-local.sh"
    echo "• 检查进程: ps aux | grep -E '(uvicorn|vite)'"
}

# 主函数
main() {
    stop_dev_servers
    show_final_status
}

# 运行主函数
main
