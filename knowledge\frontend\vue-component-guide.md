# Vue.js 组件开发指南

## 概述

Vue.js 是一个渐进式 JavaScript 框架，用于构建用户界面。本指南将介绍如何创建和使用 Vue 组件。

## 基础组件结构

### 单文件组件 (SFC)

Vue 组件通常使用单文件组件格式，包含三个部分：

```vue
<template>
  <div class="my-component">
    <h1>{{ title }}</h1>
    <p>{{ description }}</p>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Props
interface Props {
  title: string
  description?: string
}

const props = withDefaults(defineProps<Props>(), {
  description: '默认描述'
})

// Reactive data
const count = ref(0)

// Computed properties
const displayTitle = computed(() => {
  return `${props.title} (${count.value})`
})

// Methods
const increment = () => {
  count.value++
}
</script>

<style scoped>
.my-component {
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
}

h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

p {
  color: #7f8c8d;
  line-height: 1.6;
}
</style>
```

## 组件通信

### Props 传递数据

父组件向子组件传递数据：

```vue
<!-- 父组件 -->
<template>
  <ChildComponent 
    :title="pageTitle" 
    :items="dataList"
    @update="handleUpdate"
  />
</template>

<!-- 子组件 -->
<script setup lang="ts">
interface Props {
  title: string
  items: Array<any>
}

const props = defineProps<Props>()

// 事件发射
const emit = defineEmits<{
  update: [value: string]
}>()

const handleClick = () => {
  emit('update', 'new value')
}
</script>
```

### 插槽 (Slots)

使用插槽实现内容分发：

```vue
<!-- 组件定义 -->
<template>
  <div class="card">
    <header class="card-header">
      <slot name="header">默认标题</slot>
    </header>
    
    <main class="card-body">
      <slot>默认内容</slot>
    </main>
    
    <footer class="card-footer">
      <slot name="footer" :data="footerData">
        默认页脚
      </slot>
    </footer>
  </div>
</template>

<!-- 使用组件 -->
<template>
  <Card>
    <template #header>
      <h2>自定义标题</h2>
    </template>
    
    <p>这是卡片内容</p>
    
    <template #footer="{ data }">
      <button>{{ data.buttonText }}</button>
    </template>
  </Card>
</template>
```

## 响应式系统

### ref 和 reactive

```typescript
import { ref, reactive, computed, watch } from 'vue'

// 基本类型使用 ref
const count = ref(0)
const message = ref('Hello')

// 对象类型可以使用 reactive
const state = reactive({
  user: {
    name: 'John',
    age: 30
  },
  settings: {
    theme: 'dark'
  }
})

// 计算属性
const doubleCount = computed(() => count.value * 2)

// 监听器
watch(count, (newValue, oldValue) => {
  console.log(`Count changed from ${oldValue} to ${newValue}`)
})

// 监听对象
watch(() => state.user.name, (newName) => {
  console.log(`User name changed to ${newName}`)
})
```

## 生命周期钩子

```typescript
import { 
  onMounted, 
  onUpdated, 
  onUnmounted,
  onBeforeMount,
  onBeforeUpdate,
  onBeforeUnmount
} from 'vue'

export default {
  setup() {
    onBeforeMount(() => {
      console.log('组件即将挂载')
    })
    
    onMounted(() => {
      console.log('组件已挂载')
      // DOM 操作
      // API 调用
    })
    
    onUpdated(() => {
      console.log('组件已更新')
    })
    
    onUnmounted(() => {
      console.log('组件即将卸载')
      // 清理工作
      // 取消订阅
    })
  }
}
```

## 组合式函数 (Composables)

创建可复用的逻辑：

```typescript
// composables/useCounter.ts
import { ref, computed } from 'vue'

export function useCounter(initialValue = 0) {
  const count = ref(initialValue)
  
  const increment = () => count.value++
  const decrement = () => count.value--
  const reset = () => count.value = initialValue
  
  const isEven = computed(() => count.value % 2 === 0)
  
  return {
    count,
    increment,
    decrement,
    reset,
    isEven
  }
}

// 在组件中使用
<script setup lang="ts">
import { useCounter } from '@/composables/useCounter'

const { count, increment, decrement, isEven } = useCounter(10)
</script>
```

## 最佳实践

### 1. 组件命名
- 使用 PascalCase 命名组件
- 组件名应该是多个单词
- 避免与 HTML 元素冲突

### 2. Props 验证
```typescript
interface Props {
  // 必需的字符串
  title: string
  
  // 可选的数字，带默认值
  count?: number
  
  // 联合类型
  status: 'loading' | 'success' | 'error'
  
  // 对象类型
  user: {
    id: number
    name: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  count: 0
})
```

### 3. 事件命名
- 使用 kebab-case
- 使用动词描述动作
- 避免与原生事件冲突

```typescript
// 好的事件命名
emit('user-updated', user)
emit('item-selected', item)
emit('form-submitted', formData)

// 避免的命名
emit('click', event)  // 与原生事件冲突
emit('change', value) // 太通用
```

### 4. 样式作用域
- 使用 `scoped` 属性避免样式污染
- 使用 CSS Modules 或 CSS-in-JS
- 遵循 BEM 命名规范

```vue
<style scoped>
.component-name {
  /* 组件根样式 */
}

.component-name__element {
  /* 元素样式 */
}

.component-name--modifier {
  /* 修饰符样式 */
}
</style>
```

## 性能优化

### 1. 使用 v-memo
```vue
<template>
  <div v-memo="[valueA, valueB]">
    <!-- 只有当 valueA 或 valueB 改变时才重新渲染 -->
  </div>
</template>
```

### 2. 异步组件
```typescript
import { defineAsyncComponent } from 'vue'

const AsyncComponent = defineAsyncComponent(() =>
  import('./HeavyComponent.vue')
)
```

### 3. 使用 shallowRef 和 shallowReactive
```typescript
import { shallowRef, shallowReactive } from 'vue'

// 对于大型对象，只监听第一层
const largeObject = shallowReactive({
  nested: {
    deep: {
      value: 1
    }
  }
})
```

这个指南涵盖了 Vue.js 组件开发的核心概念和最佳实践。
