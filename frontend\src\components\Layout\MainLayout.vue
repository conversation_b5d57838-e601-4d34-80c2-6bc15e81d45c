<template>
  <div class="main-layout">
    <a-layout class="layout">
      <!-- 顶部导航栏 -->
      <a-layout-header class="header">
        <div class="header-content">
          <div class="logo">
            <h1>Artifacts Chat</h1>
          </div>
          <div class="header-actions">
            <UserAvatar />
            <a-button type="text" @click="toggleSider">
              <template #icon>
                <icon-menu-fold v-if="!siderCollapsed" />
                <icon-menu-unfold v-else />
              </template>
            </a-button>
          </div>
        </div>
      </a-layout-header>

      <a-layout class="layout-content">
        <!-- 左侧边栏 -->
        <a-layout-sider 
          :width="280" 
          :collapsed="siderCollapsed"
          :collapsible="false"
          class="sider"
          :class="{ 'sider-collapsed': siderCollapsed }"
        >
          <ChatHistorySidebar />
        </a-layout-sider>

        <!-- 主内容区域 -->
        <a-layout-content class="main-content">
          <div class="content-wrapper" :class="{ 'two-column': showArtifactPanel }">
            <!-- 聊天区域 -->
            <div class="chat-section" :class="{ 'with-artifact': showArtifactPanel }">
              <ChatInterface />
            </div>
            
            <!-- Artifact 面板 -->
            <div 
              v-if="showArtifactPanel" 
              class="artifact-section"
              :class="{ 'artifact-visible': showArtifactPanel }"
            >
              <ArtifactPanel />
            </div>
          </div>
        </a-layout-content>
      </a-layout>
    </a-layout>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { IconMenuFold, IconMenuUnfold } from '@arco-design/web-vue/es/icon'
import ChatHistorySidebar from './ChatHistorySidebar.vue'
import ChatInterface from '../Chat/ChatInterface.vue'
import ArtifactPanel from '../Artifact/ArtifactPanel.vue'
import UserAvatar from '../Auth/UserAvatar.vue'
import { useLayoutStore } from '@/stores/layout'

const layoutStore = useLayoutStore()

// 侧边栏状态
const siderCollapsed = computed({
  get: () => layoutStore.siderCollapsed,
  set: (value) => layoutStore.setSiderCollapsed(value)
})

// Artifact 面板显示状态
const showArtifactPanel = computed(() => layoutStore.showArtifactPanel)

// 切换侧边栏
const toggleSider = () => {
  siderCollapsed.value = !siderCollapsed.value
}
</script>

<style scoped>
.main-layout {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.layout {
  width: 100%;
  height: 100vh;
}

.header {
  background: #fff;
  border-bottom: 1px solid #e5e6eb;
  padding: 0;
  height: 60px;
  line-height: 60px;
  position: relative;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 24px;
}

.logo h1 {
  margin: 0;
  color: #1d2129;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.layout-content {
  width: 100%;
  height: calc(100vh - 60px);
  position: relative;
}

.sider {
  background: #f7f8fa;
  border-right: 1px solid #e5e6eb;
  transition: all 0.3s ease;
  position: relative;
  z-index: 50;
}

.sider-collapsed {
  width: 0 !important;
  min-width: 0 !important;
  max-width: 0 !important;
  overflow: hidden;
}

.main-content {
  flex: 1;
  width: 100%;
  background: #fff;
  position: relative;
  overflow: hidden;
}

.content-wrapper {
  display: flex;
  height: 100%;
  transition: all 0.3s ease;
}

.content-wrapper.two-column {
  /* 双栏布局时的样式 */
  animation: expandToTwoColumn 0.3s ease;
}

@keyframes expandToTwoColumn {
  from {
    transform: scaleX(1);
  }
  to {
    transform: scaleX(1);
  }
}

.chat-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  transition: all 0.3s ease;
}

.chat-section.with-artifact {
  flex: 0 0 50%;
  border-right: 1px solid #e5e6eb;
}

.artifact-section {
  flex: 0 0 50%;
  display: flex;
  flex-direction: column;
  background: #fafafa;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.artifact-section.artifact-visible {
  transform: translateX(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-wrapper.two-column {
    flex-direction: column;
  }
  
  .chat-section.with-artifact {
    flex: 0 0 40%;
    border-right: none;
    border-bottom: 1px solid #e5e6eb;
  }
  
  .artifact-section {
    flex: 0 0 60%;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 16px;
  }
  
  .logo h1 {
    font-size: 18px;
  }
  
  .sider {
    position: absolute;
    height: 100%;
    z-index: 1000;
  }
}
</style>
