<template>
  <div class="chat-history-sidebar">
    <!-- 新建聊天按钮 -->
    <a-button type="primary" long class="new-chat-btn" @click="createNewChat">
      <template #icon>
        <icon-plus />
      </template>
      新建聊天
    </a-button>

    <!-- 搜索框 -->
    <div class="search-container">
      <a-input
        v-model="searchQuery"
        placeholder="搜索聊天记录"
        allow-clear
      >
        <template #prefix>
          <icon-search />
        </template>
      </a-input>
    </div>

    <!-- 聊天历史列表 -->
    <div class="chat-list-container">
      <a-spin :loading="loading" class="chat-list-spinner">
        <div class="chat-list">
          <template v-if="filteredSessions.length > 0">
            <div 
              v-for="session in filteredSessions" 
              :key="session.id"
              class="chat-item"
              :class="{ 'active': currentSessionId === session.id }"
              @click="selectSession(session.id)"
            >
              <div class="chat-item-content">
                <div class="chat-title">{{ session.title }}</div>
                <div class="chat-time">{{ formatDate(session.updatedAt) }}</div>
              </div>
              <div class="chat-actions">
                <a-dropdown trigger="hover" @select="handleSessionAction($event, session.id)">
                  <a-button type="text" size="mini">
                    <template #icon>
                      <icon-more-vertical />
                    </template>
                  </a-button>
                  <template #content>
                    <a-doption value="rename">
                      <template #icon>
                        <icon-edit />
                      </template>
                      重命名
                    </a-doption>
                    <a-doption value="delete">
                      <template #icon>
                        <icon-delete />
                      </template>
                      删除
                    </a-doption>
                  </template>
                </a-dropdown>
              </div>
            </div>
          </template>
          <a-empty v-else description="没有找到聊天记录" />
        </div>
      </a-spin>
    </div>

    <!-- 重命名对话框 -->
    <a-modal
      v-model:visible="renameModalVisible"
      title="重命名聊天"
      @ok="confirmRename"
      @cancel="cancelRename"
      :ok-button-props="{ disabled: !newTitle.trim() }"
    >
      <a-input v-model="newTitle" placeholder="请输入新标题" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  IconPlus, 
  IconSearch, 
  IconMoreVertical, 
  IconEdit, 
  IconDelete 
} from '@arco-design/web-vue/es/icon'
import { useChatStore } from '@/stores/chat'
import { Message } from '@arco-design/web-vue'

const chatStore = useChatStore()

// 状态
const loading = ref(false)
const searchQuery = ref('')
const renameModalVisible = ref(false)
const newTitle = ref('')
const sessionToRename = ref('')

// 计算属性
const sessions = computed(() => chatStore.sessions)
const currentSessionId = computed(() => chatStore.currentSessionId)

const filteredSessions = computed(() => {
  if (!searchQuery.value) return sessions.value
  
  const query = searchQuery.value.toLowerCase()
  return sessions.value.filter(session => 
    session.title.toLowerCase().includes(query)
  )
})

// 方法
const createNewChat = () => {
  chatStore.createSession()
}

const selectSession = (sessionId: string) => {
  chatStore.setCurrentSession(sessionId)
}

const handleSessionAction = (action: string, sessionId: string) => {
  if (action === 'rename') {
    const session = sessions.value.find(s => s.id === sessionId)
    if (session) {
      newTitle.value = session.title
      sessionToRename.value = sessionId
      renameModalVisible.value = true
    }
  } else if (action === 'delete') {
    confirmDelete(sessionId)
  }
}

const confirmRename = () => {
  if (newTitle.value.trim() && sessionToRename.value) {
    chatStore.renameSession(sessionToRename.value, newTitle.value)
    renameModalVisible.value = false
    newTitle.value = ''
    sessionToRename.value = ''
    Message.success('聊天已重命名')
  }
}

const cancelRename = () => {
  renameModalVisible.value = false
  newTitle.value = ''
  sessionToRename.value = ''
}

const confirmDelete = (sessionId: string) => {
  chatStore.deleteSession(sessionId)
  Message.success('聊天已删除')
}

// 格式化日期
const formatDate = (date: Date) => {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)
  
  const dateObj = new Date(date)
  
  if (dateObj >= today) {
    return dateObj.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else if (dateObj >= yesterday) {
    return '昨天'
  } else {
    return dateObj.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' })
  }
}
</script>

<style scoped>
.chat-history-sidebar {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px;
}

.new-chat-btn {
  margin-bottom: 16px;
}

.search-container {
  margin-bottom: 16px;
}

.chat-list-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.chat-list-spinner {
  height: 100%;
  display: block;
}

.chat-list {
  height: 100%;
  overflow-y: auto;
  padding-right: 4px;
}

.chat-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chat-item:hover {
  background-color: #f2f3f5;
}

.chat-item.active {
  background-color: #e8f3ff;
}

.chat-item-content {
  flex: 1;
  min-width: 0;
}

.chat-title {
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-time {
  font-size: 12px;
  color: #86909c;
  margin-top: 4px;
}

.chat-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.chat-item:hover .chat-actions {
  opacity: 1;
}
</style>
