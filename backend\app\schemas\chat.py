"""
Chat-related Pydantic schemas
"""
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

class MessageBase(BaseModel):
    """Base message schema"""
    role: str  # "user" or "assistant"
    content: str

class MessageCreate(MessageBase):
    """Schema for creating a message"""
    session_id: int

class MessageResponse(MessageBase):
    """Schema for message response"""
    id: int
    session_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True

class ChatRequest(BaseModel):
    """Schema for chat request"""
    messages: List[MessageBase]
    session_id: Optional[int] = None
