"""
Intelligent Context Engine

Main interface for the intelligent context retrieval system.
Combines all components for optimal context generation.
"""
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

from app.services.context_engine.retrieval_engine import HybridRetriever, QueryProcessor, SearchResult
from app.services.context_engine.context_ranker import <PERSON><PERSON><PERSON><PERSON><PERSON>, ResultFilter, RankingWeights
from app.services.context_engine.knowledge_manager import KnowledgeManager
from app.core.context_config import context_settings
import structlog

logger = structlog.get_logger(__name__)

@dataclass
class ContextRequest:
    """Request for intelligent context"""
    query: str
    conversation_history: Optional[List[Dict[str, str]]] = None
    user_preferences: Optional[Dict[str, Any]] = None
    max_results: int = 10
    include_code: bool = True
    include_docs: bool = True
    frameworks: Optional[List[str]] = None
    languages: Optional[List[str]] = None

@dataclass
class ContextResponse:
    """Response with intelligent context"""
    query: str
    contexts: List[SearchResult]
    total_found: int
    processing_time_ms: int
    suggestions: List[str]
    metadata: Dict[str, Any]

class ConversationAnalyzer:
    """Analyze conversation history for better context"""
    
    def __init__(self):
        self.max_history_length = 10
    
    async def analyze_conversation(
        self,
        current_query: str,
        history: List[Dict[str, str]]
    ) -> Dict[str, Any]:
        """Analyze conversation to extract context"""
        if not history:
            return {}
        
        # Take recent messages
        recent_history = history[-self.max_history_length:]
        
        # Extract topics and technologies mentioned
        topics = set()
        technologies = set()
        
        for message in recent_history:
            content = message.get('content', '').lower()
            
            # Extract technology keywords
            tech_keywords = self._extract_tech_keywords(content)
            technologies.update(tech_keywords)
            
            # Extract topic keywords
            topic_keywords = self._extract_topic_keywords(content)
            topics.update(topic_keywords)
        
        # Determine conversation context
        context_type = self._determine_context_type(recent_history)
        
        return {
            'topics': list(topics),
            'technologies': list(technologies),
            'context_type': context_type,
            'conversation_length': len(recent_history)
        }
    
    def _extract_tech_keywords(self, text: str) -> List[str]:
        """Extract technology keywords from text"""
        tech_patterns = [
            'vue', 'react', 'angular', 'svelte',
            'javascript', 'typescript', 'python', 'java',
            'fastapi', 'django', 'flask', 'express',
            'nodejs', 'nextjs', 'nuxtjs', 'gatsby',
            'css', 'scss', 'tailwind', 'bootstrap',
            'html', 'jsx', 'tsx', 'component',
            'api', 'rest', 'graphql', 'websocket',
            'database', 'sql', 'mongodb', 'redis',
            'docker', 'kubernetes', 'aws', 'azure'
        ]
        
        found_tech = []
        for tech in tech_patterns:
            if tech in text:
                found_tech.append(tech)
        
        return found_tech
    
    def _extract_topic_keywords(self, text: str) -> List[str]:
        """Extract topic keywords from text"""
        topic_patterns = [
            'authentication', 'authorization', 'security',
            'testing', 'deployment', 'performance',
            'optimization', 'debugging', 'error',
            'form', 'validation', 'routing', 'state',
            'component', 'hook', 'lifecycle', 'event',
            'styling', 'responsive', 'mobile', 'desktop',
            'animation', 'transition', 'ui', 'ux'
        ]
        
        found_topics = []
        for topic in topic_patterns:
            if topic in text:
                found_topics.append(topic)
        
        return found_topics
    
    def _determine_context_type(self, history: List[Dict[str, str]]) -> str:
        """Determine the type of conversation context"""
        if not history:
            return 'general'
        
        # Analyze recent messages for patterns
        recent_content = ' '.join([
            msg.get('content', '') for msg in history[-3:]
        ]).lower()
        
        if any(word in recent_content for word in ['error', 'bug', 'issue', 'problem']):
            return 'debugging'
        elif any(word in recent_content for word in ['how to', 'tutorial', 'guide', 'learn']):
            return 'learning'
        elif any(word in recent_content for word in ['example', 'demo', 'sample', 'code']):
            return 'example_seeking'
        elif any(word in recent_content for word in ['best practice', 'recommend', 'should']):
            return 'best_practices'
        else:
            return 'general'

class QueryEnhancer:
    """Enhance queries based on context and conversation"""
    
    def __init__(self):
        self.conversation_analyzer = ConversationAnalyzer()
    
    async def enhance_query(
        self,
        request: ContextRequest
    ) -> Tuple[str, Dict[str, Any]]:
        """Enhance query with conversation context"""
        enhanced_query = request.query
        context_info = {}
        
        # Analyze conversation history
        if request.conversation_history:
            conversation_context = await self.conversation_analyzer.analyze_conversation(
                request.query,
                request.conversation_history
            )
            context_info.update(conversation_context)
            
            # Enhance query with conversation context
            enhanced_query = self._add_conversation_context(
                enhanced_query,
                conversation_context
            )
        
        # Add user preferences
        if request.user_preferences:
            enhanced_query = self._add_user_preferences(
                enhanced_query,
                request.user_preferences
            )
            context_info['user_preferences'] = request.user_preferences
        
        return enhanced_query, context_info
    
    def _add_conversation_context(
        self,
        query: str,
        conversation_context: Dict[str, Any]
    ) -> str:
        """Add conversation context to query"""
        enhanced_query = query
        
        # Add relevant technologies from conversation
        technologies = conversation_context.get('technologies', [])
        if technologies and not any(tech in query.lower() for tech in technologies):
            # Add the most relevant technology
            main_tech = technologies[0] if technologies else None
            if main_tech:
                enhanced_query = f"{query} {main_tech}"
        
        return enhanced_query
    
    def _add_user_preferences(
        self,
        query: str,
        preferences: Dict[str, Any]
    ) -> str:
        """Add user preferences to query"""
        enhanced_query = query
        
        # Add preferred framework if not mentioned
        preferred_framework = preferences.get('framework')
        if preferred_framework and preferred_framework.lower() not in query.lower():
            enhanced_query = f"{query} {preferred_framework}"
        
        return enhanced_query

class SuggestionGenerator:
    """Generate helpful suggestions based on search results"""
    
    def __init__(self):
        self.max_suggestions = 5
    
    async def generate_suggestions(
        self,
        query: str,
        results: List[SearchResult],
        context_info: Dict[str, Any]
    ) -> List[str]:
        """Generate suggestions for follow-up queries"""
        suggestions = []
        
        # Extract common topics from results
        topics = self._extract_result_topics(results)
        
        # Generate topic-based suggestions
        topic_suggestions = self._generate_topic_suggestions(query, topics)
        suggestions.extend(topic_suggestions)
        
        # Generate context-based suggestions
        context_suggestions = self._generate_context_suggestions(query, context_info)
        suggestions.extend(context_suggestions)
        
        # Generate framework-specific suggestions
        framework_suggestions = self._generate_framework_suggestions(query, results)
        suggestions.extend(framework_suggestions)
        
        # Remove duplicates and limit
        unique_suggestions = list(dict.fromkeys(suggestions))
        return unique_suggestions[:self.max_suggestions]
    
    def _extract_result_topics(self, results: List[SearchResult]) -> List[str]:
        """Extract common topics from search results"""
        topics = []
        
        for result in results:
            # Extract from metadata
            metadata = result.metadata
            if metadata.get('category'):
                topics.append(metadata['category'])
            if metadata.get('framework'):
                topics.append(metadata['framework'])
            if metadata.get('tags'):
                topics.extend(metadata['tags'])
        
        # Count and return most common
        from collections import Counter
        topic_counts = Counter(topics)
        return [topic for topic, count in topic_counts.most_common(5)]
    
    def _generate_topic_suggestions(self, query: str, topics: List[str]) -> List[str]:
        """Generate suggestions based on topics"""
        suggestions = []
        
        for topic in topics[:3]:  # Top 3 topics
            if topic.lower() not in query.lower():
                suggestions.append(f"{query} {topic}")
                suggestions.append(f"How to use {topic}")
                suggestions.append(f"{topic} best practices")
        
        return suggestions
    
    def _generate_context_suggestions(
        self,
        query: str,
        context_info: Dict[str, Any]
    ) -> List[str]:
        """Generate suggestions based on conversation context"""
        suggestions = []
        
        context_type = context_info.get('context_type', 'general')
        
        if context_type == 'debugging':
            suggestions.extend([
                f"{query} troubleshooting",
                f"Common {query} errors",
                f"How to debug {query}"
            ])
        elif context_type == 'learning':
            suggestions.extend([
                f"{query} tutorial",
                f"{query} examples",
                f"Learn {query} step by step"
            ])
        elif context_type == 'example_seeking':
            suggestions.extend([
                f"{query} code examples",
                f"{query} demo",
                f"Real world {query} examples"
            ])
        
        return suggestions
    
    def _generate_framework_suggestions(
        self,
        query: str,
        results: List[SearchResult]
    ) -> List[str]:
        """Generate framework-specific suggestions"""
        suggestions = []
        
        # Extract frameworks from results
        frameworks = set()
        for result in results:
            framework = result.metadata.get('framework')
            if framework:
                frameworks.add(framework)
        
        # Generate framework-specific suggestions
        for framework in list(frameworks)[:2]:  # Top 2 frameworks
            suggestions.extend([
                f"{query} in {framework}",
                f"{framework} {query} example",
                f"Best {framework} practices for {query}"
            ])
        
        return suggestions

class IntelligentContextEngine:
    """Main intelligent context engine"""
    
    def __init__(self):
        self.retriever = HybridRetriever()
        self.query_processor = QueryProcessor()
        self.ranker = ContextRanker()
        self.filter = ResultFilter()
        self.query_enhancer = QueryEnhancer()
        self.suggestion_generator = SuggestionGenerator()
        
        logger.info("Initialized IntelligentContextEngine")
    
    async def initialize(self):
        """Initialize the context engine"""
        await self.retriever.initialize()
        logger.info("IntelligentContextEngine initialized successfully")
    
    async def get_context(self, request: ContextRequest) -> ContextResponse:
        """Get intelligent context for a query"""
        start_time = datetime.now()
        
        try:
            # Enhance query with conversation context
            enhanced_query, context_info = await self.query_enhancer.enhance_query(request)
            
            # Build search filters
            filters = self._build_filters(request)
            
            # Perform hybrid search
            search_results = await self.retriever.search(
                query=enhanced_query,
                filters=filters,
                limit=request.max_results * 2,  # Get more for ranking
                rerank=False  # We'll do our own ranking
            )
            
            # Advanced ranking
            ranked_results = await self.ranker.rank_results(
                search_results,
                enhanced_query,
                context_info
            )
            
            # Filter results
            filtered_results = await self.filter.filter_results(
                ranked_results,
                filters
            )
            
            # Limit final results
            final_results = filtered_results[:request.max_results]
            
            # Generate suggestions
            suggestions = await self.suggestion_generator.generate_suggestions(
                request.query,
                final_results,
                context_info
            )
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            
            # Build response
            response = ContextResponse(
                query=request.query,
                contexts=final_results,
                total_found=len(search_results),
                processing_time_ms=int(processing_time),
                suggestions=suggestions,
                metadata={
                    'enhanced_query': enhanced_query,
                    'context_info': context_info,
                    'filters_applied': filters,
                    'ranking_enabled': True
                }
            )
            
            logger.info(
                "Generated intelligent context",
                query_length=len(request.query),
                results_found=len(search_results),
                final_results=len(final_results),
                processing_time_ms=int(processing_time)
            )
            
            return response
            
        except Exception as e:
            logger.error("Context generation failed", query=request.query[:100], error=str(e))
            
            # Return empty response on error
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            return ContextResponse(
                query=request.query,
                contexts=[],
                total_found=0,
                processing_time_ms=int(processing_time),
                suggestions=[],
                metadata={'error': str(e)}
            )
    
    def _build_filters(self, request: ContextRequest) -> Dict[str, Any]:
        """Build search filters from request"""
        filters = {}
        
        # Source type filters
        source_types = []
        if request.include_docs:
            source_types.append('document')
        if request.include_code:
            source_types.append('code')
        
        if source_types:
            filters['source_type'] = source_types
        
        # Framework filters
        if request.frameworks:
            filters['framework'] = request.frameworks
        
        # Language filters
        if request.languages:
            filters['language'] = request.languages
        
        return filters

# Singleton instance
_context_engine = None

async def get_context_engine() -> IntelligentContextEngine:
    """Get singleton context engine instance"""
    global _context_engine
    
    if _context_engine is None:
        _context_engine = IntelligentContextEngine()
        await _context_engine.initialize()
    
    return _context_engine
