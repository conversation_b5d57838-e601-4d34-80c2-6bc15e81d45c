import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

export interface ChatSession {
  id: string
  title: string
  messages: Message[]
  createdAt: Date
  updatedAt: Date
}

export const useChatStore = defineStore('chat', () => {
  // State
  const sessions = ref<ChatSession[]>([])
  const currentSessionId = ref<string | null>(null)
  const isLoading = ref(false)

  // Getters
  const currentSession = computed(() => {
    if (!currentSessionId.value) return null
    return sessions.value.find(session => session.id === currentSessionId.value) || null
  })

  const currentMessages = computed(() => {
    return currentSession.value?.messages || []
  })

  // Actions
  const createSession = (title: string = '新建聊天') => {
    const newSession: ChatSession = {
      id: Date.now().toString(),
      title,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    }
    sessions.value.unshift(newSession)
    currentSessionId.value = newSession.id
    return newSession
  }

  const addMessage = (content: string, role: 'user' | 'assistant') => {
    if (!currentSession.value) {
      createSession()
    }
    
    const message: Message = {
      id: Date.now().toString(),
      role,
      content,
      timestamp: new Date()
    }
    
    currentSession.value!.messages.push(message)
    currentSession.value!.updatedAt = new Date()
  }

  const setCurrentSession = (sessionId: string) => {
    currentSessionId.value = sessionId
  }

  const deleteSession = (sessionId: string) => {
    const index = sessions.value.findIndex(session => session.id === sessionId)
    if (index > -1) {
      sessions.value.splice(index, 1)
      if (currentSessionId.value === sessionId) {
        currentSessionId.value = sessions.value.length > 0 ? sessions.value[0].id : null
      }
    }
  }

  const renameSession = (sessionId: string, newTitle: string) => {
    const session = sessions.value.find(session => session.id === sessionId)
    if (session) {
      session.title = newTitle
      session.updatedAt = new Date()
    }
  }

  return {
    // State
    sessions,
    currentSessionId,
    isLoading,
    // Getters
    currentSession,
    currentMessages,
    // Actions
    createSession,
    addMessage,
    setCurrentSession,
    deleteSession,
    renameSession
  }
})
