"""
Context Engine specific configuration
"""
import os
from typing import Optional, List
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings
from pydantic import validator
from enum import Enum

class Environment(str, Enum):
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"

class VectorDBType(str, Enum):
    QDRANT = "qdrant"
    CHROMA = "chroma"

class EmbeddingModel(str, Enum):
    MINI_LM = "sentence-transformers/all-MiniLM-L6-v2"
    BGE_LARGE_ZH = "BAAI/bge-large-zh-v1.5"
    MPNET_BASE = "sentence-transformers/all-mpnet-base-v2"
    MULTILINGUAL = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"

class ContextEngineSettings(BaseSettings):
    # Environment
    ENVIRONMENT: Environment = Environment.DEVELOPMENT
    DEBUG: bool = True
    
    # Vector Database
    VECTOR_DB_TYPE: VectorDBType = VectorDBType.QDRANT
    VECTOR_DB_URL: str = "http://localhost:6333"
    VECTOR_DB_API_KEY: Optional[str] = None
    VECTOR_DB_COLLECTION_PREFIX: str = "context_engine"
    
    # Chroma specific settings
    CHROMA_PERSIST_DIRECTORY: str = "./data/chroma"
    
    # Qdrant specific settings
    QDRANT_GRPC_PORT: int = 6334
    QDRANT_PREFER_GRPC: bool = False
    
    # Redis Cache
    REDIS_URL: str = "redis://localhost:6379"
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    CACHE_TTL: int = 3600  # 1 hour
    
    # Embedding Model
    EMBEDDING_MODEL: EmbeddingModel = EmbeddingModel.MINI_LM
    EMBEDDING_DIMENSION: int = 384
    EMBEDDING_BATCH_SIZE: int = 32
    EMBEDDING_DEVICE: str = "cpu"  # or "cuda" if GPU available
    
    # Document Processing
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    SUPPORTED_FILE_TYPES: List[str] = [
        "pdf", "docx", "txt", "md", "html", 
        "py", "js", "ts", "vue", "jsx", "tsx"
    ]
    CHUNK_SIZE: int = 1000
    CHUNK_OVERLAP: int = 200
    
    # Legacy LLM settings (deprecated - use app.core.config.llm_settings instead)
    OPENAI_API_KEY: Optional[str] = os.getenv("OPENAI_API_KEY")
    ANTHROPIC_API_KEY: Optional[str] = os.getenv("ANTHROPIC_API_KEY")
    
    # Context Engine
    MAX_CONTEXT_LENGTH: int = 4000
    MAX_SEARCH_RESULTS: int = 20
    SIMILARITY_THRESHOLD: float = 0.7
    RERANK_TOP_K: int = 10
    
    # Knowledge Base
    KNOWLEDGE_BASE_PATH: str = "./knowledge"
    AUTO_SYNC_INTERVAL: int = 300  # 5 minutes
    
    # Monitoring
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    HEALTH_CHECK_INTERVAL: int = 30
    
    class Config:
        env_file = ".env"
        case_sensitive = True
    
    @validator("ENVIRONMENT", pre=True)
    def validate_environment(cls, v):
        if isinstance(v, str):
            return Environment(v.lower())
        return v
    
    @validator("VECTOR_DB_TYPE", pre=True)
    def validate_vector_db_type(cls, v):
        if isinstance(v, str):
            return VectorDBType(v.lower())
        return v
    
    @validator("EMBEDDING_MODEL", pre=True)
    def validate_embedding_model(cls, v):
        if isinstance(v, str):
            # Handle different model name formats
            model_mapping = {
                "all-MiniLM-L6-v2": EmbeddingModel.MINI_LM,
                "sentence-transformers/all-MiniLM-L6-v2": EmbeddingModel.MINI_LM,
                "bge-large-zh-v1.5": EmbeddingModel.BGE_LARGE_ZH,
                "BAAI/bge-large-zh-v1.5": EmbeddingModel.BGE_LARGE_ZH,
                "all-mpnet-base-v2": EmbeddingModel.MPNET_BASE,
                "sentence-transformers/all-mpnet-base-v2": EmbeddingModel.MPNET_BASE,
                "paraphrase-multilingual-MiniLM-L12-v2": EmbeddingModel.MULTILINGUAL,
                "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2": EmbeddingModel.MULTILINGUAL,
            }
            return model_mapping.get(v, EmbeddingModel.MINI_LM)
        return v
    
    @validator("EMBEDDING_DIMENSION")
    def validate_embedding_dimension(cls, v, values):
        # Set dimension based on model
        model = values.get("EMBEDDING_MODEL")
        if model == EmbeddingModel.MINI_LM:
            return 384
        elif model == EmbeddingModel.BGE_LARGE_ZH:
            return 1024
        elif model == EmbeddingModel.MPNET_BASE:
            return 768
        elif model == EmbeddingModel.MULTILINGUAL:
            return 384
        return v
    
    @validator("DEBUG")
    def validate_debug(cls, v, values):
        env = values.get("ENVIRONMENT")
        if env == Environment.PRODUCTION:
            return False
        return v
    
    @property
    def is_development(self) -> bool:
        return self.ENVIRONMENT == Environment.DEVELOPMENT
    
    @property
    def is_production(self) -> bool:
        return self.ENVIRONMENT == Environment.PRODUCTION
    
    @property
    def vector_collection_name(self) -> str:
        """Get vector collection name with prefix"""
        return f"{self.VECTOR_DB_COLLECTION_PREFIX}_main"

# Global context engine settings instance
context_settings = ContextEngineSettings()

# Model dimension mapping
MODEL_DIMENSIONS = {
    EmbeddingModel.MINI_LM: 384,
    EmbeddingModel.BGE_LARGE_ZH: 1024,
    EmbeddingModel.MPNET_BASE: 768,
    EmbeddingModel.MULTILINGUAL: 384,
}

# Supported languages for code processing
SUPPORTED_LANGUAGES = {
    "python": [".py"],
    "javascript": [".js", ".jsx"],
    "typescript": [".ts", ".tsx"],
    "vue": [".vue"],
    "html": [".html", ".htm"],
    "css": [".css", ".scss", ".sass"],
    "json": [".json"],
    "yaml": [".yml", ".yaml"],
    "markdown": [".md", ".mdx"],
    "sql": [".sql"],
}
