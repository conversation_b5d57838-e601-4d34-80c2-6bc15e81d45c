#!/usr/bin/env python3
"""
开源依赖安装脚本

自动安装开源文档处理所需的依赖包。
"""
import subprocess
import sys
from pathlib import Path

# 基础依赖 - 必需的
REQUIRED_PACKAGES = [
    "markdown>=3.5.0",
    "beautifulsoup4>=4.12.0", 
    "pyyaml>=6.0.1",
    "lxml>=4.9.0",
]

# 文档处理依赖 - 可选的
DOCUMENT_PACKAGES = [
    "PyPDF2>=3.0.0",           # PDF处理
    "python-docx>=1.1.0",      # Word文档
    "openpyxl>=3.1.0",         # Excel文档
    "python-pptx>=0.6.0",      # PowerPoint文档
]

# 向量化依赖 - 可选的
VECTORIZATION_PACKAGES = [
    "sentence-transformers>=2.2.0",
    "transformers>=4.35.0",
    "torch>=2.1.0",
    "numpy>=1.24.0",
    "scikit-learn>=1.3.0",
]

# 向量数据库 - 可选的
VECTOR_DB_PACKAGES = [
    "chromadb>=0.4.0",
]

# 文本处理增强 - 可选的
TEXT_PROCESSING_PACKAGES = [
    "nltk>=3.8.0",
    "jieba>=0.42.0",
    "textstat>=0.7.0",
]

def run_pip_install(packages, description):
    """运行pip安装命令"""
    print(f"\n📦 安装 {description}...")
    
    for package in packages:
        try:
            print(f"  安装 {package}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], capture_output=True, text=True, check=True)
            
            if result.returncode == 0:
                print(f"  ✅ {package} 安装成功")
            else:
                print(f"  ❌ {package} 安装失败: {result.stderr}")
                
        except subprocess.CalledProcessError as e:
            print(f"  ❌ {package} 安装失败: {e}")
            print(f"     错误输出: {e.stderr}")

def check_package_installed(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    """主函数"""
    print("🚀 开源文档处理依赖安装器")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    
    print(f"✅ Python版本: {sys.version}")
    
    # 询问用户要安装哪些组件
    print("\n请选择要安装的组件:")
    print("1. 基础依赖 (必需)")
    print("2. 文档处理依赖 (PDF, Word, Excel, PowerPoint)")
    print("3. 向量化依赖 (文本嵌入和机器学习)")
    print("4. 向量数据库依赖 (ChromaDB)")
    print("5. 文本处理增强 (NLTK, 中文分词等)")
    print("6. 全部安装")
    
    choice = input("\n请输入选择 (1-6, 默认为6): ").strip() or "6"
    
    try:
        # 始终安装基础依赖
        run_pip_install(REQUIRED_PACKAGES, "基础依赖")
        
        if choice in ["2", "6"]:
            run_pip_install(DOCUMENT_PACKAGES, "文档处理依赖")
        
        if choice in ["3", "6"]:
            print("\n⚠️  向量化依赖包较大，可能需要较长时间...")
            confirm = input("是否继续安装向量化依赖? (y/N): ").strip().lower()
            if confirm in ['y', 'yes']:
                run_pip_install(VECTORIZATION_PACKAGES, "向量化依赖")
        
        if choice in ["4", "6"]:
            run_pip_install(VECTOR_DB_PACKAGES, "向量数据库依赖")
        
        if choice in ["5", "6"]:
            run_pip_install(TEXT_PROCESSING_PACKAGES, "文本处理增强依赖")
        
        # 检查安装结果
        print("\n🔍 检查安装结果...")
        
        # 检查核心包
        core_packages = {
            'markdown': 'Markdown处理',
            'bs4': 'HTML解析 (BeautifulSoup4)',
            'yaml': 'YAML处理',
        }
        
        for package, description in core_packages.items():
            if check_package_installed(package):
                print(f"  ✅ {description}")
            else:
                print(f"  ❌ {description} - 未安装")
        
        # 检查可选包
        optional_packages = {
            'PyPDF2': 'PDF处理',
            'docx': 'Word文档处理',
            'openpyxl': 'Excel处理',
            'pptx': 'PowerPoint处理',
            'sentence_transformers': '文本嵌入',
            'chromadb': '向量数据库',
            'nltk': '自然语言处理',
            'jieba': '中文分词',
        }
        
        print("\n📋 可选组件状态:")
        for package, description in optional_packages.items():
            if check_package_installed(package):
                print(f"  ✅ {description}")
            else:
                print(f"  ⚪ {description} - 未安装")
        
        print("\n🎉 安装完成!")
        print("\n📖 使用说明:")
        print("1. 基础功能已可用 (Markdown, HTML, YAML, JSON)")
        print("2. 如需处理PDF/Word等格式，确保对应依赖已安装")
        print("3. 如需向量化功能，确保sentence-transformers已安装")
        print("4. 运行测试: python -c \"from app.services.context_engine.open_source_document_processor import open_source_processor; print('导入成功!')\"")
        
    except KeyboardInterrupt:
        print("\n\n❌ 安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装过程中出现错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
