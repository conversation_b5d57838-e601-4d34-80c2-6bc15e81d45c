<template>
  <div class="artifact-block" @click="openArtifact">
    <div class="artifact-header">
      <div class="artifact-info">
        <icon-code class="artifact-icon" />
        <span class="artifact-title">{{ title }}</span>
        <a-tag :color="getTypeColor(type)" size="small">{{ type.toUpperCase() }}</a-tag>
      </div>
      <div class="artifact-actions">
        <a-button type="text" size="mini" @click.stop="openArtifact">
          <template #icon>
            <icon-eye />
          </template>
          预览
        </a-button>
      </div>
    </div>
    
    <div class="artifact-preview">
      <div class="preview-content">
        <div class="preview-description">
          {{ description }}
        </div>
        <div class="preview-meta">
          <span class="meta-item">
            <icon-calendar />
            {{ formatDate(createdAt) }}
          </span>
          <span class="meta-item">
            <icon-file />
            {{ getTypeLabel(type) }}
          </span>
        </div>
      </div>
      <div class="preview-thumbnail">
        <div class="thumbnail-placeholder">
          <component :is="getTypeIcon(type)" class="type-icon" />
        </div>
      </div>
    </div>
    
    <div class="artifact-footer">
      <div class="footer-actions">
        <a-button type="text" size="mini" @click.stop="copyCode">
          <template #icon>
            <icon-copy />
          </template>
          复制代码
        </a-button>
        <a-button type="text" size="mini" @click.stop="downloadCode">
          <template #icon>
            <icon-download />
          </template>
          下载
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  IconCode,
  IconEye,
  IconCopy,
  IconDownload,
  IconCalendar,
  IconFile,
  IconCodeSquare,
  IconBarChart,
  IconMindMapping
} from '@arco-design/web-vue/es/icon'
import { Message } from '@arco-design/web-vue'
import { useLayoutStore } from '@/stores/layout'
import type { ArtifactData } from '@/stores/layout'

interface Props {
  artifact: ArtifactData
}

const props = defineProps<Props>()
const layoutStore = useLayoutStore()

// 计算属性
const title = computed(() => props.artifact.title)
const type = computed(() => props.artifact.type)
const description = computed(() => getDescription(props.artifact))
const createdAt = computed(() => new Date())

// 方法
const openArtifact = () => {
  layoutStore.showArtifact(props.artifact)
}

const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(props.artifact.code)
    Message.success('代码已复制到剪贴板')
  } catch (error) {
    Message.error('复制失败')
  }
}

const downloadCode = () => {
  const blob = new Blob([props.artifact.code], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${props.artifact.title}.${getFileExtension(props.artifact.type)}`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  Message.success('文件下载已开始')
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    html: 'orange',
    vue: 'green',
    g2plot: 'blue',
    mermaid: 'purple',
    svg: 'cyan',
    javascript: 'gold'
  }
  return colors[type] || 'gray'
}

const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    html: 'HTML 页面',
    vue: 'Vue 组件',
    g2plot: '数据图表',
    mermaid: '流程图',
    svg: 'SVG 图形',
    javascript: 'JavaScript'
  }
  return labels[type] || type.toUpperCase()
}

const getTypeIcon = (type: string) => {
  const icons: Record<string, any> = {
    html: IconCode,
    vue: IconCodeSquare,
    g2plot: IconBarChart,
    mermaid: IconMindMapping,
    svg: IconCodeSquare,
    javascript: IconCodeSquare
  }
  return icons[type] || IconCode
}

const getDescription = (artifact: ArtifactData) => {
  const descriptions: Record<string, string> = {
    html: '可交互的 HTML 页面，包含样式和脚本',
    vue: 'Vue 3 组件，支持响应式数据和事件处理',
    g2plot: '基于 G2Plot 的数据可视化图表',
    mermaid: '使用 Mermaid 语法的流程图或图表',
    svg: '可缩放的矢量图形',
    javascript: '可执行的 JavaScript 代码'
  }
  return descriptions[artifact.type] || '代码片段'
}

const getFileExtension = (type: string) => {
  const extensions: Record<string, string> = {
    html: 'html',
    vue: 'vue',
    g2plot: 'json',
    mermaid: 'mmd',
    svg: 'svg',
    javascript: 'js'
  }
  return extensions[type] || 'txt'
}

const formatDate = (date: Date) => {
  return date.toLocaleString('zh-CN', {
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.artifact-block {
  border: 2px solid #e5e6eb;
  border-radius: 12px;
  margin: 16px 0;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.artifact-block:hover {
  border-color: #165dff;
  box-shadow: 0 4px 12px rgba(22, 93, 255, 0.15);
  transform: translateY(-2px);
}

.artifact-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: linear-gradient(135deg, #165dff 0%, #246fff 100%);
  color: white;
}

.artifact-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.artifact-icon {
  font-size: 16px;
}

.artifact-title {
  font-weight: 600;
  font-size: 14px;
}

.artifact-actions {
  display: flex;
  gap: 4px;
}

.artifact-preview {
  display: flex;
  padding: 16px;
  gap: 16px;
}

.preview-content {
  flex: 1;
}

.preview-description {
  color: #4e5969;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 12px;
}

.preview-meta {
  display: flex;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #86909c;
}

.preview-thumbnail {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f7f8fa 0%, #e5e6eb 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.type-icon {
  font-size: 24px;
  color: #86909c;
}

.artifact-footer {
  padding: 8px 16px;
  background: #f7f8fa;
  border-top: 1px solid #e5e6eb;
}

.footer-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .artifact-preview {
    flex-direction: column;
    gap: 12px;
  }
  
  .preview-thumbnail {
    width: 100%;
    height: 40px;
  }
  
  .preview-meta {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
