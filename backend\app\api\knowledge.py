"""
Knowledge Base API endpoints - Enhanced with Intelligent Context
"""
from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any, Optional
from pydantic import BaseModel

# Temporarily import simplified context engine
# from app.services.context_engine.intelligent_context import get_context_engine, ContextRequest

router = APIRouter()

# Pydantic models for API
class IntelligentSearchRequest(BaseModel):
    query: str
    conversation_history: Optional[List[Dict[str, str]]] = None
    max_results: int = 10
    include_code: bool = True
    include_docs: bool = True
    frameworks: Optional[List[str]] = None
    languages: Optional[List[str]] = None

class SearchResultResponse(BaseModel):
    id: str
    content: str
    title: str
    score: float
    source_type: str
    metadata: Dict[str, Any]

class IntelligentSearchResponse(BaseModel):
    query: str
    contexts: List[SearchResultResponse]
    total_found: int
    processing_time_ms: int
    suggestions: List[str]
    metadata: Dict[str, Any]

@router.get("/collections")
async def get_collections():
    """Get all collections in the knowledge base"""
    return [
        {
            "id": "1",
            "name": "frontend-docs",
            "description": "Frontend development documentation",
            "category": "frontend",
            "subcategory": "general",
            "created_at": "2024-01-01T00:00:00Z",
            "document_count": 3,
            "code_example_count": 2
        },
        {
            "id": "2",
            "name": "backend-docs",
            "description": "Backend development documentation",
            "category": "backend",
            "subcategory": "general",
            "created_at": "2024-01-01T00:00:00Z",
            "document_count": 2,
            "code_example_count": 1
        }
    ]

@router.get("/collections/{collection_name}/stats")
async def get_collection_stats(collection_name: str):
    """Get statistics for a specific collection"""
    return {
        "name": collection_name,
        "document_count": 3,
        "code_example_count": 2,
        "total_items": 5,
        "created_at": "2024-01-01T00:00:00Z"
    }

@router.get("/health")
async def health_check():
    """Health check for knowledge base services"""
    return {
        "status": "healthy",
        "collections_count": 2,
        "timestamp": "2024-01-01T00:00:00Z"
    }

@router.post("/intelligent-search")
async def intelligent_search(request: IntelligentSearchRequest):
    """Perform intelligent context search"""
    try:
        # Mock intelligent search response for testing
        mock_results = [
            {
                "id": "doc_1",
                "content": "Vue.js is a progressive JavaScript framework for building user interfaces. It's designed to be incrementally adoptable.",
                "title": "Vue.js Introduction",
                "score": 0.95,
                "source_type": "document",
                "metadata": {
                    "framework": "vue",
                    "category": "tutorial",
                    "language": "javascript",
                    "tags": ["vue", "javascript", "frontend"]
                }
            },
            {
                "id": "code_1",
                "content": "const { ref, computed } = require('vue')\n\nexport default {\n  setup() {\n    const count = ref(0)\n    const doubleCount = computed(() => count.value * 2)\n    \n    return { count, doubleCount }\n  }\n}",
                "title": "Vue Composition API Example",
                "score": 0.88,
                "source_type": "code",
                "metadata": {
                    "framework": "vue",
                    "language": "javascript",
                    "category": "example",
                    "tags": ["vue", "composition-api", "reactive"]
                }
            }
        ]

        # Filter results based on request
        filtered_results = []
        for result in mock_results:
            # Apply framework filter
            if request.frameworks:
                if result["metadata"].get("framework") not in request.frameworks:
                    continue

            # Apply language filter
            if request.languages:
                if result["metadata"].get("language") not in request.languages:
                    continue

            # Apply source type filter
            if not request.include_code and result["source_type"] == "code":
                continue
            if not request.include_docs and result["source_type"] == "document":
                continue

            filtered_results.append(result)

        # Limit results
        final_results = filtered_results[:request.max_results]

        # Generate suggestions
        suggestions = [
            f"How to use {request.query} in Vue.js",
            f"{request.query} best practices",
            f"Advanced {request.query} techniques",
            f"{request.query} examples and tutorials"
        ]

        return IntelligentSearchResponse(
            query=request.query,
            contexts=final_results,
            total_found=len(mock_results),
            processing_time_ms=45,
            suggestions=suggestions[:3],
            metadata={
                "enhanced_query": request.query,
                "filters_applied": {
                    "frameworks": request.frameworks,
                    "languages": request.languages,
                    "include_code": request.include_code,
                    "include_docs": request.include_docs
                },
                "ranking_enabled": True
            }
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stats")
async def get_knowledge_base_stats():
    """Get overall knowledge base statistics"""
    collections = await get_collections()

    total_documents = sum(c.get('document_count', 0) for c in collections)
    total_code_examples = sum(c.get('code_example_count', 0) for c in collections)

    return {
        "total_collections": len(collections),
        "total_documents": total_documents,
        "total_code_examples": total_code_examples,
        "total_items": total_documents + total_code_examples,
        "collections": collections
    }




