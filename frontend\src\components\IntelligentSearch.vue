<template>
  <div class="intelligent-search">
    <div class="search-header">
      <h2>🧠 智能上下文搜索</h2>
      <p>基于AI的智能代码和文档检索系统</p>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form">
      <div class="search-input-group">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索代码示例、文档或最佳实践..."
          class="search-input"
          @keyup.enter="performSearch"
        />
        <button 
          @click="performSearch" 
          :disabled="!searchQuery.trim() || isLoading"
          class="search-button"
        >
          <span v-if="isLoading">🔄</span>
          <span v-else>🔍</span>
          搜索
        </button>
      </div>

      <!-- 高级选项 -->
      <div class="search-options" v-if="showAdvanced">
        <div class="option-group">
          <label>框架:</label>
          <div class="checkbox-group">
            <label v-for="framework in availableFrameworks" :key="framework">
              <input 
                type="checkbox" 
                :value="framework" 
                v-model="selectedFrameworks"
              />
              {{ framework }}
            </label>
          </div>
        </div>

        <div class="option-group">
          <label>语言:</label>
          <div class="checkbox-group">
            <label v-for="language in availableLanguages" :key="language">
              <input 
                type="checkbox" 
                :value="language" 
                v-model="selectedLanguages"
              />
              {{ language }}
            </label>
          </div>
        </div>

        <div class="option-group">
          <label>内容类型:</label>
          <div class="checkbox-group">
            <label>
              <input type="checkbox" v-model="includeCode" />
              代码示例
            </label>
            <label>
              <input type="checkbox" v-model="includeDocs" />
              文档
            </label>
          </div>
        </div>
      </div>

      <button 
        @click="showAdvanced = !showAdvanced" 
        class="toggle-advanced"
      >
        {{ showAdvanced ? '隐藏' : '显示' }}高级选项
      </button>
    </div>

    <!-- 搜索结果 -->
    <div v-if="searchResults" class="search-results">
      <div class="results-header">
        <h3>
          搜索结果 
          <span class="result-count">({{ searchResults.total_found }} 个结果，耗时 {{ searchResults.processing_time_ms }}ms)</span>
        </h3>
      </div>

      <!-- 结果列表 -->
      <div class="results-list">
        <div 
          v-for="result in searchResults.contexts" 
          :key="result.id"
          class="result-item"
          :class="{ 'code-result': result.source_type === 'code' }"
        >
          <div class="result-header">
            <h4 class="result-title">
              <span class="result-type-badge" :class="result.source_type">
                {{ result.source_type === 'code' ? '💻' : '📄' }}
                {{ result.source_type === 'code' ? '代码' : '文档' }}
              </span>
              {{ result.title }}
            </h4>
            <div class="result-score">
              相关度: {{ (result.score * 100).toFixed(1) }}%
            </div>
          </div>

          <div class="result-content">
            <pre v-if="result.source_type === 'code'" class="code-content">{{ result.content }}</pre>
            <div v-else class="doc-content">{{ result.content }}</div>
          </div>

          <div class="result-metadata">
            <span v-if="result.metadata.framework" class="metadata-tag framework">
              {{ result.metadata.framework }}
            </span>
            <span v-if="result.metadata.language" class="metadata-tag language">
              {{ result.metadata.language }}
            </span>
            <span v-if="result.metadata.category" class="metadata-tag category">
              {{ result.metadata.category }}
            </span>
            <span 
              v-for="tag in result.metadata.tags" 
              :key="tag" 
              class="metadata-tag tag"
            >
              {{ tag }}
            </span>
          </div>
        </div>
      </div>

      <!-- 建议查询 -->
      <div v-if="searchResults.suggestions.length > 0" class="suggestions">
        <h4>💡 相关建议:</h4>
        <div class="suggestion-list">
          <button 
            v-for="suggestion in searchResults.suggestions" 
            :key="suggestion"
            @click="searchQuery = suggestion; performSearch()"
            class="suggestion-button"
          >
            {{ suggestion }}
          </button>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="hasSearched && (!searchResults || searchResults.contexts.length === 0)" class="empty-state">
      <div class="empty-icon">🔍</div>
      <h3>未找到相关结果</h3>
      <p>尝试使用不同的关键词或调整搜索选项</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-state">
      <div class="loading-spinner">🔄</div>
      <p>正在智能分析和检索...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

// 响应式数据
const searchQuery = ref('')
const isLoading = ref(false)
const hasSearched = ref(false)
const showAdvanced = ref(false)
const searchResults = ref(null)

// 搜索选项
const selectedFrameworks = ref(['vue', 'fastapi'])
const selectedLanguages = ref(['javascript', 'python'])
const includeCode = ref(true)
const includeDocs = ref(true)

// 可用选项
const availableFrameworks = ['vue', 'react', 'angular', 'fastapi', 'django', 'express']
const availableLanguages = ['javascript', 'typescript', 'python', 'java', 'go']

// 搜索方法
const performSearch = async () => {
  if (!searchQuery.value.trim()) return

  isLoading.value = true
  hasSearched.value = true

  try {
    const response = await fetch('/api/knowledge/intelligent-search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query: searchQuery.value,
        max_results: 10,
        include_code: includeCode.value,
        include_docs: includeDocs.value,
        frameworks: selectedFrameworks.value.length > 0 ? selectedFrameworks.value : null,
        languages: selectedLanguages.value.length > 0 ? selectedLanguages.value : null
      })
    })

    if (response.ok) {
      searchResults.value = await response.json()
    } else {
      console.error('Search failed:', response.statusText)
      searchResults.value = null
    }
  } catch (error) {
    console.error('Search error:', error)
    searchResults.value = null
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.intelligent-search {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.search-header {
  text-align: center;
  margin-bottom: 30px;
}

.search-header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.search-header p {
  color: #7f8c8d;
  font-size: 16px;
}

.search-form {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.search-input-group {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.search-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.search-button {
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.search-button:hover:not(:disabled) {
  background: #2563eb;
}

.search-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.search-options {
  border-top: 1px solid #e5e7eb;
  padding-top: 20px;
  margin-top: 20px;
}

.option-group {
  margin-bottom: 15px;
}

.option-group label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  display: block;
}

.checkbox-group {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.checkbox-group label {
  font-weight: normal;
  display: flex;
  align-items: center;
  gap: 5px;
}

.toggle-advanced {
  background: none;
  border: 1px solid #d1d5db;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  color: #6b7280;
}

.results-header {
  margin-bottom: 20px;
}

.result-count {
  color: #6b7280;
  font-weight: normal;
  font-size: 14px;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.result-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #e5e7eb;
}

.result-item.code-result {
  border-left-color: #10b981;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.result-title {
  margin: 0;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 10px;
}

.result-type-badge {
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
}

.result-type-badge.code {
  background: #d1fae5;
  color: #065f46;
}

.result-type-badge.document {
  background: #dbeafe;
  color: #1e40af;
}

.result-score {
  background: #f9fafb;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  color: #6b7280;
}

.result-content {
  margin-bottom: 15px;
}

.code-content {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 14px;
  overflow-x: auto;
  white-space: pre-wrap;
}

.doc-content {
  color: #4b5563;
  line-height: 1.6;
}

.result-metadata {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.metadata-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.metadata-tag.framework {
  background: #fef3c7;
  color: #92400e;
}

.metadata-tag.language {
  background: #e0e7ff;
  color: #3730a3;
}

.metadata-tag.category {
  background: #f3e8ff;
  color: #6b21a8;
}

.metadata-tag.tag {
  background: #f0f9ff;
  color: #0c4a6e;
}

.suggestions {
  margin-top: 30px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
}

.suggestions h4 {
  margin: 0 0 15px 0;
  color: #374151;
}

.suggestion-list {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.suggestion-button {
  padding: 8px 16px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
}

.suggestion-button:hover {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.empty-state, .loading-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-icon, .loading-spinner {
  font-size: 48px;
  margin-bottom: 20px;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
