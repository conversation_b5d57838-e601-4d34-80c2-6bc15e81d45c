"""
LLM service for generating chat responses
"""
import json
import asyncio
from typing import List, AsyncGenerator
from app.schemas.chat import MessageBase

async def generate_chat_response(messages: List[MessageBase]) -> AsyncGenerator[str, None]:
    """
    Generate streaming chat response from LLM

    This is a mock implementation. In production, this would integrate
    with actual LLM APIs like OpenAI, Anthropic, or local models.
    """
    # Get the last user message
    user_message = ""
    for msg in reversed(messages):
        if msg.role == "user":
            user_message = msg.content.lower()
            break

    # Generate response based on user input
    if "html" in user_message or "网页" in user_message or "页面" in user_message:
        response = await generate_html_response()
    elif "vue" in user_message or "组件" in user_message:
        response = await generate_vue_response()
    elif "图表" in user_message or "chart" in user_message or "g2plot" in user_message:
        response = await generate_chart_response()
    elif "流程图" in user_message or "mermaid" in user_message:
        response = await generate_mermaid_response()
    else:
        response = await generate_default_response()

    # Simulate streaming by yielding chunks
    words = response.split()
    for i, word in enumerate(words):
        chunk = {
            "content": word + " ",
            "role": "assistant"
        }
        yield json.dumps(chunk)

        # Add some delay to simulate real streaming
        await asyncio.sleep(0.03)

    # Signal end of stream
    yield json.dumps({"content": "", "done": True})

async def generate_html_response() -> str:
    return """我来为您创建一个精美的 HTML 页面：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欢迎页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            backdrop-filter: blur(10px);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.5em;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 30px;
            font-size: 1.1em;
        }
        .button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .feature {
            padding: 20px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            transition: transform 0.3s ease;
        }
        .feature:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 欢迎来到我的网站</h1>
        <p>这是一个使用现代 HTML、CSS 技术创建的响应式页面，展示了渐变背景、毛玻璃效果和动画交互。</p>
        <button class="button" onclick="showMessage()">点击体验</button>

        <div class="features">
            <div class="feature">
                <h3>🎨 现代设计</h3>
                <p>渐变色彩</p>
            </div>
            <div class="feature">
                <h3>📱 响应式</h3>
                <p>适配各种设备</p>
            </div>
            <div class="feature">
                <h3>✨ 动画效果</h3>
                <p>流畅交互</p>
            </div>
        </div>
    </div>

    <script>
        function showMessage() {
            alert('🎊 欢迎使用 Artifacts Chat！这个页面完全由 AI 生成并实时渲染。');
        }

        // 添加一些动态效果
        document.addEventListener('DOMContentLoaded', function() {
            const features = document.querySelectorAll('.feature');
            features.forEach((feature, index) => {
                feature.style.animationDelay = `${index * 0.2}s`;
                feature.style.animation = 'fadeInUp 0.6s ease forwards';
            });
        });

        // CSS 动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
```

这个 HTML 页面包含了：
- 🎨 现代渐变背景和毛玻璃效果
- 📱 完全响应式设计
- ✨ 悬停动画和交互效果
- 🎯 网格布局和卡片设计
- 🚀 JavaScript 动态效果

您可以在右侧面板中看到完整的交互效果！"""

async def generate_vue_response() -> str:
    return """我来为您创建一个功能丰富的 Vue 组件：

```vue
<template>
  <div class="todo-app">
    <div class="header">
      <h1>📝 待办事项</h1>
      <p>使用 Vue 3 Composition API 构建</p>
    </div>

    <div class="add-todo">
      <input
        v-model="newTodo"
        @keyup.enter="addTodo"
        placeholder="添加新的待办事项..."
        class="todo-input"
      />
      <button @click="addTodo" :disabled="!newTodo.trim()" class="add-btn">
        ➕ 添加
      </button>
    </div>

    <div class="filters">
      <button
        v-for="filter in filters"
        :key="filter.key"
        @click="currentFilter = filter.key"
        :class="['filter-btn', { active: currentFilter === filter.key }]"
      >
        {{ filter.label }} ({{ filter.count }})
      </button>
    </div>

    <div class="todo-list">
      <div
        v-for="todo in filteredTodos"
        :key="todo.id"
        :class="['todo-item', { completed: todo.completed }]"
      >
        <input
          type="checkbox"
          v-model="todo.completed"
          class="todo-checkbox"
        />
        <span class="todo-text">{{ todo.text }}</span>
        <button @click="removeTodo(todo.id)" class="remove-btn">🗑️</button>
      </div>

      <div v-if="filteredTodos.length === 0" class="empty-state">
        <p>{{ emptyMessage }}</p>
      </div>
    </div>

    <div class="stats">
      <p>总计: {{ todos.length }} | 已完成: {{ completedCount }} | 待完成: {{ pendingCount }}</p>
      <button v-if="completedCount > 0" @click="clearCompleted" class="clear-btn">
        清除已完成
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const newTodo = ref('')
const currentFilter = ref('all')
const todos = ref([
  { id: 1, text: '学习 Vue 3', completed: false },
  { id: 2, text: '体验 Artifacts 功能', completed: true },
  { id: 3, text: '构建项目', completed: false }
])

const addTodo = () => {
  if (newTodo.value.trim()) {
    todos.value.push({
      id: Date.now(),
      text: newTodo.value.trim(),
      completed: false
    })
    newTodo.value = ''
  }
}

const removeTodo = (id) => {
  const index = todos.value.findIndex(todo => todo.id === id)
  if (index > -1) {
    todos.value.splice(index, 1)
  }
}

const clearCompleted = () => {
  todos.value = todos.value.filter(todo => !todo.completed)
}

const completedCount = computed(() =>
  todos.value.filter(todo => todo.completed).length
)

const pendingCount = computed(() =>
  todos.value.filter(todo => !todo.completed).length
)

const filteredTodos = computed(() => {
  switch (currentFilter.value) {
    case 'active':
      return todos.value.filter(todo => !todo.completed)
    case 'completed':
      return todos.value.filter(todo => todo.completed)
    default:
      return todos.value
  }
})

const filters = computed(() => [
  { key: 'all', label: '全部', count: todos.value.length },
  { key: 'active', label: '待完成', count: pendingCount.value },
  { key: 'completed', label: '已完成', count: completedCount.value }
])

const emptyMessage = computed(() => {
  switch (currentFilter.value) {
    case 'active':
      return '🎉 所有任务都已完成！'
    case 'completed':
      return '还没有完成的任务'
    default:
      return '还没有添加任何待办事项'
  }
})
</script>

<style scoped>
.todo-app {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Arial', sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: #2c3e50;
  margin-bottom: 5px;
}

.header p {
  color: #7f8c8d;
  font-size: 14px;
}

.add-todo {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.todo-input {
  flex: 1;
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.todo-input:focus {
  outline: none;
  border-color: #3498db;
}

.add-btn {
  padding: 12px 20px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s;
}

.add-btn:hover:not(:disabled) {
  background: #2980b9;
  transform: translateY(-2px);
}

.add-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.filters {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  justify-content: center;
}

.filter-btn {
  padding: 8px 16px;
  border: 2px solid #e0e0e0;
  background: white;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
}

.filter-btn.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.filter-btn:hover {
  border-color: #3498db;
}

.todo-list {
  min-height: 200px;
}

.todo-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  margin-bottom: 10px;
  transition: all 0.3s;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.todo-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.todo-item.completed {
  opacity: 0.7;
  background: #f8f9fa;
}

.todo-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.todo-text {
  flex: 1;
  font-size: 16px;
  transition: all 0.3s;
}

.todo-item.completed .todo-text {
  text-decoration: line-through;
  color: #7f8c8d;
}

.remove-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.remove-btn:hover {
  opacity: 1;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #7f8c8d;
}

.stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
  font-size: 14px;
  color: #7f8c8d;
}

.clear-btn {
  padding: 6px 12px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.3s;
}

.clear-btn:hover {
  background: #c0392b;
}
</style>
```

这个 Vue 组件展示了：
- ✅ Vue 3 Composition API 的使用
- 🔄 响应式数据绑定和计算属性
- 🎯 事件处理和条件渲染
- 🎨 现代化的 UI 设计
- 📱 完全响应式布局
- 🚀 流畅的动画效果

您可以在右侧面板中与这个待办事项应用进行完整交互！"""

async def generate_chart_response() -> str:
    import random

    # 生成随机数据
    chart_types = ["Line", "Column", "Pie", "Area", "Scatter"]
    chart_type = random.choice(chart_types)

    if chart_type == "Line":
        return generate_line_chart()
    elif chart_type == "Column":
        return generate_column_chart()
    elif chart_type == "Pie":
        return generate_pie_chart()
    elif chart_type == "Area":
        return generate_area_chart()
    else:
        return generate_scatter_chart()

def generate_line_chart() -> str:
    return """我来为您创建一个折线图：

```g2plot
{
  "type": "Line",
  "data": [
    {"month": "1月", "value": 3.2, "category": "销售额"},
    {"month": "2月", "value": 4.1, "category": "销售额"},
    {"month": "3月", "value": 3.8, "category": "销售额"},
    {"month": "4月", "value": 5.2, "category": "销售额"},
    {"month": "5月", "value": 4.9, "category": "销售额"},
    {"month": "6月", "value": 6.1, "category": "销售额"},
    {"month": "7月", "value": 5.8, "category": "销售额"},
    {"month": "8月", "value": 6.5, "category": "销售额"},
    {"month": "1月", "value": 2.1, "category": "利润"},
    {"month": "2月", "value": 2.8, "category": "利润"},
    {"month": "3月", "value": 2.5, "category": "利润"},
    {"month": "4月", "value": 3.6, "category": "利润"},
    {"month": "5月", "value": 3.2, "category": "利润"},
    {"month": "6月", "value": 4.1, "category": "利润"},
    {"month": "7月", "value": 3.9, "category": "利润"},
    {"month": "8月", "value": 4.3, "category": "利润"}
  ],
  "xField": "month",
  "yField": "value",
  "seriesField": "category",
  "smooth": true,
  "animation": {
    "appear": {
      "animation": "path-in",
      "duration": 1000
    }
  },
  "color": ["#1890ff", "#52c41a"],
  "point": {
    "size": 5,
    "shape": "diamond"
  },
  "tooltip": {
    "showMarkers": true
  },
  "legend": {
    "position": "top"
  },
  "interactions": [
    {
      "type": "marker-active"
    }
  ]
}
```

这个折线图展示了：
- 📈 多系列数据对比
- 🎨 平滑曲线效果
- ✨ 交互式数据点
- 🎯 专业的图例和提示框
- 📊 动画加载效果

您可以在右侧面板中与图表进行交互！"""

def generate_column_chart() -> str:
    return """我来为您创建一个柱状图：

```g2plot
{
  "type": "Column",
  "data": [
    {"name": "北京", "value": 38.2, "category": "销售额"},
    {"name": "上海", "value": 52.1, "category": "销售额"},
    {"name": "广州", "value": 41.8, "category": "销售额"},
    {"name": "深圳", "value": 47.3, "category": "销售额"},
    {"name": "杭州", "value": 35.6, "category": "销售额"},
    {"name": "北京", "value": 28.5, "category": "利润"},
    {"name": "上海", "value": 35.2, "category": "利润"},
    {"name": "广州", "value": 29.1, "category": "利润"},
    {"name": "深圳", "value": 32.8, "category": "利润"},
    {"name": "杭州", "value": 24.3, "category": "利润"}
  ],
  "xField": "name",
  "yField": "value",
  "seriesField": "category",
  "isGroup": true,
  "columnStyle": {
    "radius": [4, 4, 0, 0]
  },
  "color": ["#5B8FF9", "#5AD8A6"],
  "animation": {
    "appear": {
      "animation": "grow-in-y",
      "duration": 1000
    }
  },
  "label": {
    "position": "middle",
    "style": {
      "fill": "#FFFFFF",
      "opacity": 0.8
    }
  },
  "tooltip": {
    "shared": true,
    "showMarkers": false
  },
  "legend": {
    "position": "top-right"
  }
}
```

这个柱状图展示了：
- 📊 分组柱状图对比
- 🎨 圆角柱状样式
- ✨ 数据标签显示
- 🎯 共享提示框
- 📈 动画生长效果

您可以在右侧面板中查看详细数据！"""

def generate_pie_chart() -> str:
    return """我来为您创建一个饼图：

```g2plot
{
  "type": "Pie",
  "data": [
    {"type": "移动端", "value": 45.2},
    {"type": "桌面端", "value": 32.8},
    {"type": "平板端", "value": 15.6},
    {"type": "其他", "value": 6.4}
  ],
  "angleField": "value",
  "colorField": "type",
  "radius": 0.8,
  "innerRadius": 0.4,
  "color": ["#5B8FF9", "#5AD8A6", "#5D7092", "#F6BD16"],
  "label": {
    "type": "outer",
    "content": "{name} {percentage}"
  },
  "tooltip": {
    "formatter": {
      "title": "设备类型",
      "name": "占比"
    }
  },
  "legend": {
    "position": "bottom",
    "flipPage": false
  },
  "animation": {
    "appear": {
      "animation": "grow-in-x",
      "duration": 1000
    }
  },
  "interactions": [
    {
      "type": "element-active"
    },
    {
      "type": "pie-statistic-active"
    }
  ],
  "statistic": {
    "title": {
      "style": {
        "whiteSpace": "pre-wrap",
        "overflow": "hidden",
        "textOverflow": "ellipsis"
      },
      "content": "总访问量"
    },
    "content": {
      "style": {
        "whiteSpace": "pre-wrap",
        "overflow": "hidden",
        "textOverflow": "ellipsis"
      },
      "content": "100%"
    }
  }
}
```

这个饼图展示了：
- 🥧 环形饼图设计
- 🎨 外部标签显示
- ✨ 中心统计信息
- 🎯 交互式高亮
- 📊 百分比数据展示

您可以在右侧面板中点击扇形区域查看详情！"""

async def generate_mermaid_response() -> str:
    return """我来为您创建一个流程图：

```mermaid
graph TD
    A[开始] --> B{用户输入}
    B -->|包含代码| C[检测代码类型]
    B -->|普通文本| D[常规回复]

    C --> E{代码类型}
    E -->|HTML| F[渲染HTML页面]
    E -->|Vue| G[编译Vue组件]
    E -->|图表| H[生成数据图表]
    E -->|流程图| I[绘制Mermaid图]

    F --> J[显示在Artifact面板]
    G --> J
    H --> J
    I --> J

    J --> K[用户交互]
    K --> L{需要修改?}
    L -->|是| M[生成新版本]
    L -->|否| N[完成]

    M --> J
    D --> N

    style A fill:#e1f5fe
    style N fill:#c8e6c9
    style J fill:#fff3e0
    style C fill:#f3e5f5
    style E fill:#f3e5f5
```

这个流程图展示了：
- 🔄 Artifacts Chat 的工作流程
- 🎯 代码检测和分类逻辑
- 🎨 不同类型内容的处理方式
- ✨ 用户交互和迭代过程
- 📊 清晰的决策分支

您可以在右侧面板中看到完整的交互式流程图！"""

async def generate_default_response() -> str:
    return """您好！我是 Artifacts Chat 的 AI 助手。我可以帮您创建各种类型的内容：

## 🎨 我能为您创建什么？

### 1. **HTML 网页**
- 现代响应式设计
- 交互式用户界面
- 动画和特效

### 2. **Vue 组件**
- 使用 Vue 3 Composition API
- 响应式数据绑定
- 完整的组件功能

### 3. **数据图表**
- 使用 G2Plot 图表库
- 多种图表类型
- 交互式数据可视化

### 4. **流程图表**
- 使用 Mermaid 语法
- 流程图、时序图等
- 清晰的图形化表达

## 💡 使用建议

请告诉我您想要创建什么，例如：
- "创建一个登录页面"
- "制作一个计数器组件"
- "生成销售数据图表"
- "画一个系统架构图"

我会为您生成相应的代码，并在右侧面板中实时预览效果！

## ✨ 特色功能

- 🔄 **实时预览**：代码即时渲染
- 🎯 **智能检测**：自动识别代码类型
- 📱 **响应式设计**：适配各种设备
- 🎨 **现代UI**：美观的用户界面

现在就开始创建您的第一个 Artifact 吧！"""
