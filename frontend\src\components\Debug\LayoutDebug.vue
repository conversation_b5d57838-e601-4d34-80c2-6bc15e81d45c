<template>
  <div class="layout-debug">
    <div class="debug-info">
      <h3>布局调试信息</h3>
      <div class="info-grid">
        <div class="info-item">
          <label>窗口宽度:</label>
          <span>{{ windowWidth }}px</span>
        </div>
        <div class="info-item">
          <label>窗口高度:</label>
          <span>{{ windowHeight }}px</span>
        </div>
        <div class="info-item">
          <label>App元素宽度:</label>
          <span>{{ appWidth }}px</span>
        </div>
        <div class="info-item">
          <label>App元素高度:</label>
          <span>{{ appHeight }}px</span>
        </div>
        <div class="info-item">
          <label>布局占用比例:</label>
          <span>{{ widthRatio }}% × {{ heightRatio }}%</span>
        </div>
      </div>
      
      <div class="debug-actions">
        <button @click="refreshInfo" class="refresh-btn">刷新信息</button>
        <button @click="toggleFullscreen" class="fullscreen-btn">
          {{ isFullscreen ? '退出全屏' : '进入全屏' }}
        </button>
      </div>
      
      <div class="layout-status" :class="statusClass">
        <strong>状态:</strong> {{ statusMessage }}
      </div>
    </div>
    
    <div class="visual-debug">
      <div class="screen-outline">
        <div class="app-outline" :style="appOutlineStyle">
          <span>App区域</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'

const windowWidth = ref(0)
const windowHeight = ref(0)
const appWidth = ref(0)
const appHeight = ref(0)
const isFullscreen = ref(false)

const widthRatio = computed(() => {
  return windowWidth.value > 0 ? Math.round((appWidth.value / windowWidth.value) * 100) : 0
})

const heightRatio = computed(() => {
  return windowHeight.value > 0 ? Math.round((appHeight.value / windowHeight.value) * 100) : 0
})

const statusClass = computed(() => {
  const isFullWidth = widthRatio.value >= 98
  const isFullHeight = heightRatio.value >= 98
  
  if (isFullWidth && isFullHeight) return 'status-good'
  if (widthRatio.value >= 90 && heightRatio.value >= 90) return 'status-warning'
  return 'status-error'
})

const statusMessage = computed(() => {
  const isFullWidth = widthRatio.value >= 98
  const isFullHeight = heightRatio.value >= 98
  
  if (isFullWidth && isFullHeight) {
    return '✅ 布局正常，占满全屏'
  } else if (widthRatio.value >= 90 && heightRatio.value >= 90) {
    return '⚠️ 布局基本正常，但未完全占满屏幕'
  } else {
    return '❌ 布局异常，未占满屏幕空间'
  }
})

const appOutlineStyle = computed(() => {
  return {
    width: `${widthRatio.value}%`,
    height: `${heightRatio.value}%`
  }
})

const refreshInfo = () => {
  // 获取窗口尺寸
  windowWidth.value = window.innerWidth
  windowHeight.value = window.innerHeight
  
  // 获取App元素尺寸
  const appElement = document.getElementById('app')
  if (appElement) {
    const rect = appElement.getBoundingClientRect()
    appWidth.value = rect.width
    appHeight.value = rect.height
  }
  
  // 检查是否全屏
  isFullscreen.value = !!(
    document.fullscreenElement ||
    (document as any).webkitFullscreenElement ||
    (document as any).mozFullScreenElement ||
    (document as any).msFullscreenElement
  )
}

const toggleFullscreen = async () => {
  if (!isFullscreen.value) {
    try {
      await document.documentElement.requestFullscreen()
    } catch (err) {
      console.error('无法进入全屏模式:', err)
    }
  } else {
    try {
      await document.exitFullscreen()
    } catch (err) {
      console.error('无法退出全屏模式:', err)
    }
  }
  
  setTimeout(refreshInfo, 100)
}

onMounted(() => {
  refreshInfo()
  
  // 监听窗口大小变化
  window.addEventListener('resize', refreshInfo)
  
  // 监听全屏状态变化
  document.addEventListener('fullscreenchange', refreshInfo)
  
  // 定期刷新信息
  const interval = setInterval(refreshInfo, 1000)
  
  // 清理
  return () => {
    window.removeEventListener('resize', refreshInfo)
    document.removeEventListener('fullscreenchange', refreshInfo)
    clearInterval(interval)
  }
})
</script>

<style scoped>
.layout-debug {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 350px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 20px;
  border-radius: 8px;
  font-family: monospace;
  font-size: 12px;
  z-index: 9999;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.debug-info h3 {
  margin: 0 0 15px 0;
  color: #4CAF50;
  font-size: 14px;
}

.info-grid {
  display: grid;
  gap: 8px;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item label {
  color: #ccc;
}

.info-item span {
  color: #fff;
  font-weight: bold;
}

.debug-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.refresh-btn,
.fullscreen-btn {
  flex: 1;
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  background: #2196F3;
  color: white;
  cursor: pointer;
  font-size: 11px;
}

.refresh-btn:hover,
.fullscreen-btn:hover {
  background: #1976D2;
}

.layout-status {
  padding: 10px;
  border-radius: 4px;
  text-align: center;
  font-weight: bold;
}

.status-good {
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid #4CAF50;
  color: #4CAF50;
}

.status-warning {
  background: rgba(255, 193, 7, 0.2);
  border: 1px solid #FFC107;
  color: #FFC107;
}

.status-error {
  background: rgba(244, 67, 54, 0.2);
  border: 1px solid #F44336;
  color: #F44336;
}

.visual-debug {
  margin-top: 20px;
}

.screen-outline {
  width: 200px;
  height: 120px;
  border: 2px solid #666;
  position: relative;
  background: rgba(255, 255, 255, 0.1);
}

.app-outline {
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(76, 175, 80, 0.3);
  border: 1px solid #4CAF50;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #4CAF50;
  font-weight: bold;
}
</style>
