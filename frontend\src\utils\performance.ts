/**
 * 性能监控工具
 */

export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: Map<string, number> = new Map()

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  // 开始计时
  startTiming(label: string): void {
    this.metrics.set(label, performance.now())
  }

  // 结束计时并返回耗时
  endTiming(label: string): number {
    const startTime = this.metrics.get(label)
    if (!startTime) {
      console.warn(`No start time found for label: ${label}`)
      return 0
    }
    
    const duration = performance.now() - startTime
    this.metrics.delete(label)
    
    // 记录性能指标
    console.log(`[Performance] ${label}: ${duration.toFixed(2)}ms`)
    
    // 如果耗时过长，发出警告
    if (duration > 1000) {
      console.warn(`[Performance Warning] ${label} took ${duration.toFixed(2)}ms`)
    }
    
    return duration
  }

  // 监控组件渲染性能
  measureComponentRender(componentName: string, renderFn: () => void): void {
    this.startTiming(`${componentName}_render`)
    renderFn()
    this.endTiming(`${componentName}_render`)
  }

  // 监控 API 请求性能
  async measureApiCall<T>(label: string, apiCall: () => Promise<T>): Promise<T> {
    this.startTiming(label)
    try {
      const result = await apiCall()
      this.endTiming(label)
      return result
    } catch (error) {
      this.endTiming(label)
      throw error
    }
  }

  // 获取页面性能指标
  getPageMetrics(): Record<string, number> {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    
    return {
      // DNS 查询时间
      dnsLookup: navigation.domainLookupEnd - navigation.domainLookupStart,
      // TCP 连接时间
      tcpConnect: navigation.connectEnd - navigation.connectStart,
      // 请求响应时间
      request: navigation.responseEnd - navigation.requestStart,
      // DOM 解析时间
      domParse: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      // 页面加载完成时间
      pageLoad: navigation.loadEventEnd - navigation.loadEventStart,
      // 首次内容绘制
      firstContentfulPaint: this.getFirstContentfulPaint(),
      // 最大内容绘制
      largestContentfulPaint: this.getLargestContentfulPaint()
    }
  }

  private getFirstContentfulPaint(): number {
    const entries = performance.getEntriesByName('first-contentful-paint')
    return entries.length > 0 ? entries[0].startTime : 0
  }

  private getLargestContentfulPaint(): number {
    const entries = performance.getEntriesByType('largest-contentful-paint')
    return entries.length > 0 ? entries[entries.length - 1].startTime : 0
  }

  // 内存使用监控
  getMemoryUsage(): Record<string, number> | null {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit
      }
    }
    return null
  }

  // 监控长任务
  observeLongTasks(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          console.warn(`[Long Task] Duration: ${entry.duration}ms, Start: ${entry.startTime}ms`)
        }
      })
      
      try {
        observer.observe({ entryTypes: ['longtask'] })
      } catch (e) {
        console.log('Long task observation not supported')
      }
    }
  }

  // 生成性能报告
  generateReport(): string {
    const pageMetrics = this.getPageMetrics()
    const memoryUsage = this.getMemoryUsage()
    
    let report = '=== Performance Report ===\n'
    report += `DNS Lookup: ${pageMetrics.dnsLookup.toFixed(2)}ms\n`
    report += `TCP Connect: ${pageMetrics.tcpConnect.toFixed(2)}ms\n`
    report += `Request: ${pageMetrics.request.toFixed(2)}ms\n`
    report += `DOM Parse: ${pageMetrics.domParse.toFixed(2)}ms\n`
    report += `Page Load: ${pageMetrics.pageLoad.toFixed(2)}ms\n`
    report += `First Contentful Paint: ${pageMetrics.firstContentfulPaint.toFixed(2)}ms\n`
    report += `Largest Contentful Paint: ${pageMetrics.largestContentfulPaint.toFixed(2)}ms\n`
    
    if (memoryUsage) {
      report += `\n=== Memory Usage ===\n`
      report += `Used JS Heap: ${(memoryUsage.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB\n`
      report += `Total JS Heap: ${(memoryUsage.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB\n`
      report += `JS Heap Limit: ${(memoryUsage.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB\n`
    }
    
    return report
  }
}

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance()

// 自动开始监控长任务
performanceMonitor.observeLongTasks()

// 页面加载完成后输出性能报告
window.addEventListener('load', () => {
  setTimeout(() => {
    console.log(performanceMonitor.generateReport())
  }, 1000)
})
