"""
Claude API Adapter for LLM Integration

Provides unified interface for Anthropic Claude models with context injection.
"""
import asyncio
import json
from typing import List, Dict, Any, Optional, AsyncGenerator, Union
from dataclasses import dataclass
import httpx
from datetime import datetime

from app.core.context_config import context_settings
from app.services.context_engine.intelligent_context import get_context_engine, ContextRequest
from app.services.llm.openai_adapter import ChatMessage, ChatRequest, ChatResponse
import structlog

logger = structlog.get_logger(__name__)

class ClaudeAdapter:
    """Claude API adapter with intelligent context injection"""
    
    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None):
        self.api_key = api_key or context_settings.CLAUDE_API_KEY
        self.base_url = base_url or context_settings.CLAUDE_BASE_URL or "https://api.anthropic.com"
        self.timeout = context_settings.LLM_TIMEOUT or 60
        
        # HTTP client configuration
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.timeout),
            headers={
                "x-api-key": self.api_key,
                "Content-Type": "application/json",
                "anthropic-version": "2023-06-01"
            }
        )
        
        logger.info("Initialized Claude adapter", base_url=self.base_url)
    
    async def chat_completion(self, request: ChatRequest) -> Union[ChatResponse, AsyncGenerator]:
        """Generate chat completion with optional context enhancement"""
        try:
            # Enhance messages with context if enabled
            enhanced_messages = request.messages
            context_used = False
            context_sources = []
            
            if request.enable_context and len(request.messages) > 0:
                enhanced_messages, context_info = await self._enhance_with_context(
                    request.messages, request.user_id, request.session_id
                )
                context_used = context_info.get('context_used', False)
                context_sources = context_info.get('sources', [])
            
            # Convert to Claude format
            claude_messages, system_prompt = self._convert_to_claude_format(enhanced_messages)
            
            # Prepare Claude API request
            api_request = {
                "model": self._map_model_name(request.model),
                "messages": claude_messages,
                "max_tokens": request.max_tokens or 4096,
                "temperature": request.temperature,
                "stream": request.stream
            }
            
            if system_prompt:
                api_request["system"] = system_prompt
            
            # Make API call
            if request.stream:
                return self._stream_completion(api_request, context_used, context_sources)
            else:
                return await self._complete_completion(api_request, context_used, context_sources)
                
        except Exception as e:
            logger.error("Claude chat completion failed", error=str(e))
            raise
    
    def _map_model_name(self, model: str) -> str:
        """Map model names - deprecated, use simple_llm_service instead"""
        # This adapter is deprecated in favor of OpenAI-compatible interface
        return model
    
    def _convert_to_claude_format(self, messages: List[ChatMessage]) -> tuple[List[Dict], Optional[str]]:
        """Convert OpenAI format messages to Claude format"""
        claude_messages = []
        system_prompt = None
        
        for msg in messages:
            if msg.role == "system":
                # Claude uses separate system parameter
                if system_prompt:
                    system_prompt += f"\n\n{msg.content}"
                else:
                    system_prompt = msg.content
            else:
                claude_messages.append({
                    "role": msg.role,
                    "content": msg.content
                })
        
        return claude_messages, system_prompt
    
    async def _enhance_with_context(
        self, 
        messages: List[ChatMessage], 
        user_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> tuple[List[ChatMessage], Dict[str, Any]]:
        """Enhance messages with intelligent context (same as OpenAI adapter)"""
        try:
            # Get the latest user message for context search
            user_messages = [msg for msg in messages if msg.role == "user"]
            if not user_messages:
                return messages, {"context_used": False}
            
            latest_query = user_messages[-1].content
            
            # Get conversation history for context
            conversation_history = []
            for msg in messages[-10:]:  # Last 10 messages for context
                conversation_history.append({
                    "role": msg.role,
                    "content": msg.content
                })
            
            # Request intelligent context
            context_engine = await get_context_engine()
            context_request = ContextRequest(
                query=latest_query,
                conversation_history=conversation_history,
                max_results=5,
                include_code=True,
                include_docs=True
            )
            
            context_response = await context_engine.get_context(context_request)
            
            # Build context if results found
            if context_response.contexts:
                context_content = self._build_context_content(context_response)
                
                # Insert context as system message or enhance existing system message
                enhanced_messages = self._inject_context(messages, context_content)
                
                context_info = {
                    "context_used": True,
                    "sources": [ctx.id for ctx in context_response.contexts],
                    "total_results": context_response.total_found,
                    "processing_time": context_response.processing_time_ms
                }
                
                logger.info(
                    "Enhanced Claude messages with context",
                    query_length=len(latest_query),
                    context_results=len(context_response.contexts),
                    processing_time=context_response.processing_time_ms
                )
                
                return enhanced_messages, context_info
            else:
                return messages, {"context_used": False}
                
        except Exception as e:
            logger.error("Claude context enhancement failed", error=str(e))
            return messages, {"context_used": False, "error": str(e)}
    
    def _build_context_content(self, context_response) -> str:
        """Build context content from search results (optimized for Claude)"""
        context_parts = [
            "# 相关技术文档和代码示例\n",
            "以下是与用户查询相关的技术文档和代码示例。请仔细阅读这些内容，并基于这些信息提供准确、实用的回答：\n"
        ]
        
        for i, context in enumerate(context_response.contexts, 1):
            context_parts.append(f"\n## 参考资料 {i}: {context.title}")
            context_parts.append(f"**类型**: {context.source_type}")
            context_parts.append(f"**相关度**: {context.score:.2f}")
            
            # Add metadata
            metadata = context.metadata
            if metadata.get('framework'):
                context_parts.append(f"**框架**: {metadata['framework']}")
            if metadata.get('language'):
                context_parts.append(f"**语言**: {metadata['language']}")
            if metadata.get('category'):
                context_parts.append(f"**分类**: {metadata['category']}")
            
            # Add content
            if context.source_type == 'code':
                context_parts.append(f"\n```{metadata.get('language', '')}")
                context_parts.append(context.content)
                context_parts.append("```")
            else:
                context_parts.append(f"\n{context.content}")
            
            context_parts.append("\n---")
        
        context_parts.append(
            "\n## 回答指导原则\n"
            "1. 优先使用上述参考资料中的信息和代码示例\n"
            "2. 确保回答准确、实用且符合最佳实践\n"
            "3. 如果有代码示例，请详细解释代码的工作原理\n"
            "4. 提供完整、可运行的代码示例\n"
            "5. 如果参考资料不足以回答问题，请明确说明并提供一般性建议"
        )
        
        return "\n".join(context_parts)
    
    def _inject_context(self, messages: List[ChatMessage], context_content: str) -> List[ChatMessage]:
        """Inject context into message list"""
        enhanced_messages = []
        
        # Check if there's already a system message
        has_system_message = any(msg.role == "system" for msg in messages)
        
        if has_system_message:
            # Enhance existing system message
            for msg in messages:
                if msg.role == "system":
                    enhanced_content = f"{msg.content}\n\n{context_content}"
                    enhanced_messages.append(ChatMessage(
                        role="system",
                        content=enhanced_content
                    ))
                else:
                    enhanced_messages.append(msg)
        else:
            # Add new system message with context
            enhanced_messages.append(ChatMessage(
                role="system",
                content=context_content
            ))
            enhanced_messages.extend(messages)
        
        return enhanced_messages
    
    async def _complete_completion(
        self, 
        api_request: Dict[str, Any], 
        context_used: bool, 
        context_sources: List[str]
    ) -> ChatResponse:
        """Handle non-streaming completion"""
        try:
            response = await self.client.post(
                f"{self.base_url}/v1/messages",
                json=api_request
            )
            response.raise_for_status()
            
            data = response.json()
            
            # Convert Claude response to OpenAI format
            choices = [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": data["content"][0]["text"] if data.get("content") else ""
                },
                "finish_reason": self._map_stop_reason(data.get("stop_reason"))
            }]
            
            usage = {
                "prompt_tokens": data.get("usage", {}).get("input_tokens", 0),
                "completion_tokens": data.get("usage", {}).get("output_tokens", 0),
                "total_tokens": data.get("usage", {}).get("input_tokens", 0) + data.get("usage", {}).get("output_tokens", 0)
            }
            
            return ChatResponse(
                id=data.get("id", f"claude-{datetime.now().timestamp()}"),
                model=data.get("model", "claude-3-sonnet"),
                choices=choices,
                usage=usage,
                created=int(datetime.now().timestamp()),
                context_used=context_used,
                context_sources=context_sources
            )
            
        except httpx.HTTPStatusError as e:
            logger.error("Claude API error", status_code=e.response.status_code, response=e.response.text)
            raise
        except Exception as e:
            logger.error("Claude completion request failed", error=str(e))
            raise
    
    async def _stream_completion(
        self, 
        api_request: Dict[str, Any], 
        context_used: bool, 
        context_sources: List[str]
    ) -> AsyncGenerator:
        """Handle streaming completion"""
        try:
            async with self.client.stream(
                "POST",
                f"{self.base_url}/v1/messages",
                json=api_request
            ) as response:
                response.raise_for_status()
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]  # Remove "data: " prefix
                        
                        if data_str.strip() == "[DONE]":
                            break
                        
                        try:
                            data = json.loads(data_str)
                            
                            # Convert Claude streaming format to OpenAI format
                            if data.get("type") == "content_block_delta":
                                openai_chunk = {
                                    "id": f"claude-{datetime.now().timestamp()}",
                                    "object": "chat.completion.chunk",
                                    "created": int(datetime.now().timestamp()),
                                    "model": "claude-3-sonnet",
                                    "choices": [{
                                        "index": 0,
                                        "delta": {
                                            "content": data.get("delta", {}).get("text", "")
                                        },
                                        "finish_reason": None
                                    }]
                                }
                                
                                # Add context information to first chunk
                                if not hasattr(self, '_context_info_sent'):
                                    openai_chunk["context_used"] = context_used
                                    openai_chunk["context_sources"] = context_sources
                                    self._context_info_sent = True
                                
                                yield openai_chunk
                            
                            elif data.get("type") == "message_stop":
                                # Send final chunk
                                final_chunk = {
                                    "id": f"claude-{datetime.now().timestamp()}",
                                    "object": "chat.completion.chunk",
                                    "created": int(datetime.now().timestamp()),
                                    "model": "claude-3-sonnet",
                                    "choices": [{
                                        "index": 0,
                                        "delta": {},
                                        "finish_reason": "stop"
                                    }]
                                }
                                yield final_chunk
                                break
                            
                        except json.JSONDecodeError:
                            continue
                            
        except httpx.HTTPStatusError as e:
            logger.error("Claude streaming API error", status_code=e.response.status_code)
            raise
        except Exception as e:
            logger.error("Claude streaming completion failed", error=str(e))
            raise
    
    def _map_stop_reason(self, claude_reason: str) -> str:
        """Map Claude stop reasons to OpenAI format"""
        mapping = {
            "end_turn": "stop",
            "max_tokens": "length",
            "stop_sequence": "stop"
        }
        return mapping.get(claude_reason, "stop")
    
    async def list_models(self) -> List[Dict[str, Any]]:
        """List available Claude models - deprecated"""
        # This adapter is deprecated in favor of OpenAI-compatible interface
        return []
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

# Singleton instance
_claude_adapter = None

async def get_claude_adapter() -> ClaudeAdapter:
    """Get singleton Claude adapter instance"""
    global _claude_adapter
    
    if _claude_adapter is None:
        _claude_adapter = ClaudeAdapter()
    
    return _claude_adapter
