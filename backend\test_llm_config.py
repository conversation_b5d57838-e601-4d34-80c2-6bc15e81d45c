#!/usr/bin/env python3
"""
Test script for LLM configuration
"""
import sys
import os
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def test_config_loading():
    """Test if configuration loads correctly"""
    print("🔧 Testing configuration loading...")
    
    try:
        from app.core.config import settings, llm_settings
        
        print(f"✅ Settings loaded successfully")
        print(f"   API_PREFIX: {settings.API_PREFIX}")
        print(f"   DATABASE_URL: {settings.DATABASE_URL}")
        
        print(f"✅ LLM Settings loaded successfully")
        print(f"   LLM_MODEL: {llm_settings.LLM_MODEL}")
        print(f"   LLM_API_URL: {llm_settings.LLM_API_URL}")
        print(f"   LLM_TEMPERATURE: {llm_settings.LLM_TEMPERATURE}")
        print(f"   LLM_TIMEOUT: {llm_settings.LLM_TIMEOUT}")
        
        # Check if API key is set (don't print the actual key)
        if llm_settings.LLM_API_KEY:
            print(f"   LLM_API_KEY: {'*' * len(llm_settings.LLM_API_KEY)} (set)")
        else:
            print(f"   LLM_API_KEY: (not set)")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration loading failed: {str(e)}")
        return False

def test_llm_service_import():
    """Test if LLM service can be imported"""
    print("\n📦 Testing LLM service import...")
    
    try:
        from app.services.llm.simple_llm_service import SimpleLLMService, ChatMessage, ChatRequest
        from app.services.llm import get_llm_service
        
        print("✅ LLM service imports successful")
        print(f"   SimpleLLMService: {SimpleLLMService}")
        print(f"   ChatMessage: {ChatMessage}")
        print(f"   ChatRequest: {ChatRequest}")
        print(f"   get_llm_service: {get_llm_service}")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM service import failed: {str(e)}")
        return False

def test_api_import():
    """Test if API endpoints can be imported"""
    print("\n🌐 Testing API import...")
    
    try:
        from app.api.enhanced_chat import router
        
        print("✅ API import successful")
        print(f"   Router: {router}")
        
        # Check routes
        routes = [route.path for route in router.routes]
        print(f"   Available routes: {routes}")
        
        return True
        
    except Exception as e:
        print(f"❌ API import failed: {str(e)}")
        return False

def test_pydantic_models():
    """Test if Pydantic models work correctly"""
    print("\n📋 Testing Pydantic models...")
    
    try:
        from app.services.llm.simple_llm_service import ChatMessage, ChatRequest
        
        # Test ChatMessage
        message = ChatMessage(role="user", content="Hello, world!")
        print(f"✅ ChatMessage created: {message}")
        
        # Test ChatRequest
        request = ChatRequest(
            messages=[message],
            model="gpt-3.5-turbo",
            temperature=0.7,
            stream=False
        )
        print(f"✅ ChatRequest created: {request}")
        
        return True
        
    except Exception as e:
        print(f"❌ Pydantic models test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing LLM Configuration Refactor")
    print("=" * 50)
    
    tests = [
        test_config_loading,
        test_llm_service_import,
        test_api_import,
        test_pydantic_models
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Configuration refactor successful.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    exit(main())
