#!/usr/bin/env python3
"""
开源文档处理示例

演示如何使用开源文档处理器处理各种格式的文档。
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from app.services.context_engine.open_source_document_processor import open_source_processor
import tempfile
import json

def create_sample_files():
    """创建示例文件用于测试"""
    temp_dir = Path(tempfile.mkdtemp())
    
    # 创建Markdown文件
    md_content = """---
title: "示例Markdown文档"
author: "测试用户"
tags: ["示例", "测试", "markdown"]
---

# 示例文档

这是一个**示例**Markdown文档，用于测试文档处理功能。

## 功能特性

- 支持Markdown语法
- 支持代码块
- 支持表格

```python
def hello_world():
    print("Hello, World!")
```

## 表格示例

| 功能 | 状态 | 说明 |
|------|------|------|
| Markdown | ✅ | 完全支持 |
| PDF | ✅ | 基础支持 |
| Word | ✅ | 完全支持 |
"""
    
    md_file = temp_dir / "example.md"
    with open(md_file, 'w', encoding='utf-8') as f:
        f.write(md_content)
    
    # 创建纯文本文件
    txt_content = """这是一个纯文本文件示例。

包含多个段落和一些中文内容。

可以用于测试基础的文本处理功能。
"""
    
    txt_file = temp_dir / "example.txt"
    with open(txt_file, 'w', encoding='utf-8') as f:
        f.write(txt_content)
    
    # 创建JSON文件
    json_content = {
        "name": "示例配置",
        "version": "1.0.0",
        "features": ["文档处理", "向量化", "搜索"],
        "settings": {
            "chunk_size": 1000,
            "overlap": 200
        }
    }
    
    json_file = temp_dir / "example.json"
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(json_content, f, ensure_ascii=False, indent=2)
    
    # 创建HTML文件
    html_content = """<!DOCTYPE html>
<html>
<head>
    <title>示例HTML文档</title>
</head>
<body>
    <h1>HTML文档示例</h1>
    <p>这是一个<strong>HTML</strong>文档示例。</p>
    <ul>
        <li>支持HTML标签</li>
        <li>自动提取文本内容</li>
        <li>保留结构信息</li>
    </ul>
</body>
</html>"""
    
    html_file = temp_dir / "example.html"
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return temp_dir, [md_file, txt_file, json_file, html_file]

def process_document_example(file_path: Path):
    """处理单个文档的示例"""
    print(f"\n📄 处理文件: {file_path.name}")
    print("-" * 50)
    
    try:
        # 处理文档
        doc = open_source_processor.process_file(file_path)
        
        # 显示基本信息
        print(f"📋 标题: {doc.title}")
        print(f"📁 文件类型: {doc.file_type}")
        print(f"📊 内容长度: {len(doc.content)} 字符")
        print(f"🧩 分块数量: {len(doc.chunks)}")
        print(f"🔑 关键词: {', '.join(doc.keywords[:5])}")
        
        # 显示摘要
        print(f"\n📝 摘要:")
        print(doc.summary)
        
        # 显示元数据
        print(f"\n🏷️ 元数据:")
        for key, value in doc.metadata.items():
            if isinstance(value, (str, int, float, bool)):
                print(f"  {key}: {value}")
            elif isinstance(value, list) and len(value) < 10:
                print(f"  {key}: {value}")
            else:
                print(f"  {key}: <复杂数据>")
        
        # 显示前两个分块
        print(f"\n📚 分块示例 (前2个):")
        for i, chunk in enumerate(doc.chunks[:2]):
            print(f"\n  分块 {i+1}:")
            print(f"    类型: {chunk.chunk_type}")
            print(f"    长度: {len(chunk.content)} 字符")
            print(f"    内容预览: {chunk.content[:100]}...")
        
        return doc
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return None

def batch_processing_example(files: list):
    """批量处理示例"""
    print(f"\n🔄 批量处理 {len(files)} 个文件")
    print("=" * 60)
    
    results = []
    
    for file_path in files:
        doc = process_document_example(file_path)
        if doc:
            results.append(doc)
    
    # 统计信息
    print(f"\n📊 批量处理统计:")
    print(f"  成功处理: {len(results)} 个文件")
    print(f"  失败处理: {len(files) - len(results)} 个文件")
    
    if results:
        total_chunks = sum(len(doc.chunks) for doc in results)
        avg_chunks = total_chunks / len(results)
        print(f"  总分块数: {total_chunks}")
        print(f"  平均分块数: {avg_chunks:.1f}")
        
        # 文件类型统计
        file_types = {}
        for doc in results:
            file_types[doc.file_type] = file_types.get(doc.file_type, 0) + 1
        
        print(f"  文件类型分布:")
        for file_type, count in file_types.items():
            print(f"    {file_type}: {count} 个")
    
    return results

def supported_formats_example():
    """显示支持的格式"""
    print("📋 支持的文件格式:")
    print("-" * 30)
    
    formats = open_source_processor.supported_types
    
    for ext, processor_func in formats.items():
        func_name = processor_func.__name__.replace('_process_', '').replace('_', ' ').title()
        print(f"  {ext:<8} - {func_name}")
    
    print(f"\n总计支持 {len(formats)} 种格式")

def performance_test_example():
    """性能测试示例"""
    print("\n⚡ 性能测试")
    print("-" * 30)
    
    import time
    
    # 创建一个较大的测试文件
    temp_dir = Path(tempfile.mkdtemp())
    large_content = "这是一个性能测试文档。\n" * 1000  # 约25KB
    
    large_file = temp_dir / "large_test.txt"
    with open(large_file, 'w', encoding='utf-8') as f:
        f.write(large_content)
    
    # 测试处理时间
    start_time = time.time()
    doc = open_source_processor.process_file(large_file)
    end_time = time.time()
    
    processing_time = end_time - start_time
    
    print(f"📄 文件大小: {len(large_content)} 字符")
    print(f"⏱️ 处理时间: {processing_time:.3f} 秒")
    print(f"🧩 生成分块: {len(doc.chunks)} 个")
    print(f"🚀 处理速度: {len(large_content) / processing_time:.0f} 字符/秒")
    
    # 清理
    large_file.unlink()
    temp_dir.rmdir()

def main():
    """主函数"""
    print("🚀 开源文档处理器示例")
    print("=" * 60)
    
    # 显示支持的格式
    supported_formats_example()
    
    # 创建示例文件
    print(f"\n📁 创建示例文件...")
    temp_dir, sample_files = create_sample_files()
    
    try:
        # 单个文档处理示例
        print(f"\n🔍 单个文档处理示例:")
        process_document_example(sample_files[0])  # 处理Markdown文件
        
        # 批量处理示例
        batch_processing_example(sample_files)
        
        # 性能测试
        performance_test_example()
        
        print(f"\n✅ 示例运行完成!")
        print(f"💡 提示: 你可以修改此脚本来测试自己的文档")
        
    finally:
        # 清理临时文件
        for file in sample_files:
            if file.exists():
                file.unlink()
        if temp_dir.exists():
            temp_dir.rmdir()

if __name__ == "__main__":
    main()
