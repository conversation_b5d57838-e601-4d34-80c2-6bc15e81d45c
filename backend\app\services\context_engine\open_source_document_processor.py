"""
开源文档处理器

使用完全开源和免费的库实现文档处理功能，替代Unstructured.io。
支持多种文档格式：PDF、Word、Excel、PowerPoint、Markdown、HTML、纯文本等。
"""
import re
import hashlib
import json
import mimetypes
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import yaml
import markdown
from bs4 import BeautifulSoup
import logging

# 可选的开源文档处理库
try:
    import PyPDF2
    HAS_PDF_SUPPORT = True
except ImportError:
    HAS_PDF_SUPPORT = False

try:
    from docx import Document
    HAS_DOCX_SUPPORT = True
except ImportError:
    HAS_DOCX_SUPPORT = False

try:
    import openpyxl
    HAS_EXCEL_SUPPORT = True
except ImportError:
    HAS_EXCEL_SUPPORT = False

try:
    from pptx import Presentation
    HAS_PPTX_SUPPORT = True
except ImportError:
    HAS_PPTX_SUPPORT = False

logger = logging.getLogger(__name__)

@dataclass
class DocumentChunk:
    """文档分块"""
    id: str
    content: str
    title: str
    chunk_index: int
    start_position: int
    end_position: int
    chunk_type: str  # text, code, table, list, header
    metadata: Dict[str, Any]
    content_hash: str

@dataclass
class ProcessedDocument:
    """处理后的文档"""
    id: str
    title: str
    content: str
    metadata: Dict[str, Any]
    chunks: List[DocumentChunk]
    summary: str
    keywords: List[str]
    file_path: str
    file_hash: str
    file_type: str

class OpenSourceDocumentProcessor:
    """开源文档处理器主类"""
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        
        # 支持的文件类型
        self.supported_types = {
            '.md': self._process_markdown,
            '.txt': self._process_text,
            '.html': self._process_html,
            '.htm': self._process_html,
            '.json': self._process_json,
            '.yaml': self._process_yaml,
            '.yml': self._process_yaml,
        }
        
        # 添加可选支持的类型
        if HAS_PDF_SUPPORT:
            self.supported_types['.pdf'] = self._process_pdf
        if HAS_DOCX_SUPPORT:
            self.supported_types['.docx'] = self._process_docx
        if HAS_EXCEL_SUPPORT:
            self.supported_types['.xlsx'] = self._process_excel
            self.supported_types['.xls'] = self._process_excel
        if HAS_PPTX_SUPPORT:
            self.supported_types['.pptx'] = self._process_pptx
    
    def process_file(self, file_path: Path) -> ProcessedDocument:
        """处理文件"""
        try:
            file_ext = file_path.suffix.lower()
            
            if file_ext not in self.supported_types:
                raise ValueError(f"不支持的文件类型: {file_ext}")
            
            # 读取文件内容
            with open(file_path, 'rb') as f:
                file_content = f.read()
            
            file_hash = hashlib.md5(file_content).hexdigest()
            
            # 处理文档
            processor = self.supported_types[file_ext]
            content, metadata = processor(file_path)
            
            # 生成文档ID
            doc_id = hashlib.md5(str(file_path).encode()).hexdigest()
            
            # 提取标题
            title = metadata.get('title', file_path.stem)
            
            # 分块处理
            chunks = self._chunk_content(content, title)
            
            # 生成摘要和关键词
            summary = self._generate_summary(content, metadata)
            keywords = self._extract_keywords(content, metadata)
            
            return ProcessedDocument(
                id=doc_id,
                title=title,
                content=content,
                metadata=metadata,
                chunks=chunks,
                summary=summary,
                keywords=keywords,
                file_path=str(file_path),
                file_hash=file_hash,
                file_type=file_ext
            )
            
        except Exception as e:
            logger.error(f"处理文件失败 {file_path}: {e}")
            raise
    
    def _process_markdown(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """处理Markdown文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        metadata = {}
        
        # 提取YAML前置元数据
        if content.startswith('---\n'):
            try:
                end_marker = content.find('\n---\n', 4)
                if end_marker != -1:
                    yaml_content = content[4:end_marker]
                    metadata = yaml.safe_load(yaml_content)
                    content = content[end_marker + 5:].strip()
            except yaml.YAMLError as e:
                logger.warning(f"YAML解析失败: {e}")
        
        # 转换为HTML然后提取纯文本
        html = markdown.markdown(content)
        soup = BeautifulSoup(html, 'html.parser')
        text_content = soup.get_text()
        
        metadata.update({
            'file_type': 'markdown',
            'has_yaml_frontmatter': bool(metadata),
            'markdown_html': html
        })
        
        return text_content, metadata
    
    def _process_text(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """处理纯文本文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        metadata = {
            'file_type': 'text',
            'encoding': 'utf-8',
            'line_count': len(content.split('\n'))
        }
        
        return content, metadata
    
    def _process_html(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """处理HTML文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        
        # 提取标题
        title = soup.find('title')
        title_text = title.get_text() if title else file_path.stem
        
        # 提取纯文本
        text_content = soup.get_text()
        
        metadata = {
            'file_type': 'html',
            'title': title_text,
            'has_scripts': bool(soup.find('script')),
            'has_styles': bool(soup.find('style')),
            'links_count': len(soup.find_all('a'))
        }
        
        return text_content, metadata
    
    def _process_json(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """处理JSON文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 将JSON转换为可读文本
        content = json.dumps(data, indent=2, ensure_ascii=False)
        
        metadata = {
            'file_type': 'json',
            'json_keys': list(data.keys()) if isinstance(data, dict) else [],
            'json_type': type(data).__name__
        }
        
        return content, metadata
    
    def _process_yaml(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """处理YAML文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        try:
            data = yaml.safe_load(content)
            # 将YAML转换为可读文本
            readable_content = yaml.dump(data, default_flow_style=False, allow_unicode=True)
        except yaml.YAMLError:
            readable_content = content
            data = {}
        
        metadata = {
            'file_type': 'yaml',
            'yaml_keys': list(data.keys()) if isinstance(data, dict) else [],
            'yaml_type': type(data).__name__
        }
        
        return readable_content, metadata
    
    def _process_pdf(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """处理PDF文件（需要PyPDF2）"""
        if not HAS_PDF_SUPPORT:
            raise ImportError("需要安装PyPDF2来处理PDF文件: pip install PyPDF2")
        
        with open(file_path, 'rb') as f:
            reader = PyPDF2.PdfReader(f)
            
            content = ""
            for page in reader.pages:
                content += page.extract_text() + "\n"
        
        metadata = {
            'file_type': 'pdf',
            'page_count': len(reader.pages),
            'pdf_info': reader.metadata if hasattr(reader, 'metadata') else {}
        }
        
        return content, metadata
    
    def _process_docx(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """处理Word文档（需要python-docx）"""
        if not HAS_DOCX_SUPPORT:
            raise ImportError("需要安装python-docx来处理Word文档: pip install python-docx")
        
        doc = Document(file_path)
        
        content = ""
        for paragraph in doc.paragraphs:
            content += paragraph.text + "\n"
        
        # 处理表格
        table_content = ""
        for table in doc.tables:
            for row in table.rows:
                row_text = "\t".join(cell.text for cell in row.cells)
                table_content += row_text + "\n"
        
        if table_content:
            content += "\n表格内容:\n" + table_content
        
        metadata = {
            'file_type': 'docx',
            'paragraph_count': len(doc.paragraphs),
            'table_count': len(doc.tables),
            'has_tables': len(doc.tables) > 0
        }
        
        return content, metadata
    
    def _process_excel(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """处理Excel文件（需要openpyxl）"""
        if not HAS_EXCEL_SUPPORT:
            raise ImportError("需要安装openpyxl来处理Excel文件: pip install openpyxl")
        
        workbook = openpyxl.load_workbook(file_path)
        
        content = ""
        sheet_info = {}
        
        for sheet_name in workbook.sheetnames:
            sheet = workbook[sheet_name]
            content += f"\n工作表: {sheet_name}\n"
            
            rows_data = []
            for row in sheet.iter_rows(values_only=True):
                if any(cell is not None for cell in row):
                    row_text = "\t".join(str(cell) if cell is not None else "" for cell in row)
                    content += row_text + "\n"
                    rows_data.append(row)
            
            sheet_info[sheet_name] = {
                'max_row': sheet.max_row,
                'max_column': sheet.max_column,
                'data_rows': len(rows_data)
            }
        
        metadata = {
            'file_type': 'excel',
            'sheet_count': len(workbook.sheetnames),
            'sheet_names': workbook.sheetnames,
            'sheet_info': sheet_info
        }
        
        return content, metadata
    
    def _process_pptx(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """处理PowerPoint文件（需要python-pptx）"""
        if not HAS_PPTX_SUPPORT:
            raise ImportError("需要安装python-pptx来处理PowerPoint文件: pip install python-pptx")
        
        presentation = Presentation(file_path)
        
        content = ""
        slide_count = 0
        
        for slide in presentation.slides:
            slide_count += 1
            content += f"\n幻灯片 {slide_count}:\n"
            
            for shape in slide.shapes:
                if hasattr(shape, "text"):
                    content += shape.text + "\n"
        
        metadata = {
            'file_type': 'pptx',
            'slide_count': slide_count,
            'layout_count': len(presentation.slide_layouts)
        }
        
        return content, metadata
    
    def _chunk_content(self, content: str, title: str) -> List[DocumentChunk]:
        """分块处理内容"""
        chunks = []
        
        # 按段落分割
        paragraphs = content.split('\n\n')
        
        current_chunk = ""
        current_start = 0
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # 如果添加这个段落会超过限制
            if len(current_chunk) + len(paragraph) > self.chunk_size and current_chunk:
                # 创建当前分块
                chunk = self._create_chunk(
                    content=current_chunk.strip(),
                    title=title,
                    chunk_index=len(chunks),
                    start_position=current_start,
                    end_position=current_start + len(current_chunk)
                )
                chunks.append(chunk)
                
                # 开始新分块（包含重叠）
                overlap_content = self._get_overlap_content(current_chunk)
                current_chunk = overlap_content + paragraph
                current_start = current_start + len(current_chunk) - len(overlap_content)
            else:
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
        
        # 处理最后一个分块
        if current_chunk:
            chunk = self._create_chunk(
                content=current_chunk.strip(),
                title=title,
                chunk_index=len(chunks),
                start_position=current_start,
                end_position=current_start + len(current_chunk)
            )
            chunks.append(chunk)
        
        return chunks
    
    def _create_chunk(self, content: str, title: str, chunk_index: int, 
                     start_position: int, end_position: int) -> DocumentChunk:
        """创建文档分块"""
        chunk_id = hashlib.md5(f"{title}_{chunk_index}_{content[:100]}".encode()).hexdigest()
        content_hash = hashlib.md5(content.encode()).hexdigest()
        
        # 检测分块类型
        chunk_type = self._detect_chunk_type(content)
        
        return DocumentChunk(
            id=chunk_id,
            content=content,
            title=title,
            chunk_index=chunk_index,
            start_position=start_position,
            end_position=end_position,
            chunk_type=chunk_type,
            metadata={
                "length": len(content),
                "word_count": len(content.split()),
                "has_code": "```" in content or "def " in content or "function " in content,
                "has_links": "[" in content and "](" in content,
                "has_numbers": bool(re.search(r'\d+', content))
            },
            content_hash=content_hash
        )
    
    def _detect_chunk_type(self, content: str) -> str:
        """检测分块类型"""
        content_lower = content.lower()
        
        if content.startswith('#') or content_lower.startswith('title') or content_lower.startswith('heading'):
            return 'header'
        elif '```' in content or 'def ' in content or 'function ' in content:
            return 'code'
        elif '|' in content and content.count('|') > 4:
            return 'table'
        elif content.startswith(('- ', '* ', '1. ', '2. ')):
            return 'list'
        else:
            return 'text'
    
    def _get_overlap_content(self, content: str) -> str:
        """获取重叠内容"""
        if len(content) <= self.chunk_overlap:
            return content
        
        # 从末尾取重叠长度的内容
        overlap = content[-self.chunk_overlap:]
        
        # 尝试在句子边界截断
        sentences = overlap.split('.')
        if len(sentences) > 1:
            return '.'.join(sentences[1:]) + '.'
        
        return overlap
    
    def _generate_summary(self, content: str, metadata: Dict[str, Any]) -> str:
        """生成文档摘要"""
        # 优先使用元数据中的描述
        if 'description' in metadata:
            return metadata['description']
        
        # 提取前几句作为摘要
        sentences = re.split(r'[.!?]+', content)
        summary_sentences = []
        total_length = 0
        
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence and total_length + len(sentence) < 200:
                summary_sentences.append(sentence)
                total_length += len(sentence)
            else:
                break
        
        return '. '.join(summary_sentences) + '.' if summary_sentences else "无摘要"
    
    def _extract_keywords(self, content: str, metadata: Dict[str, Any]) -> List[str]:
        """提取关键词"""
        keywords = []
        
        # 从元数据中获取标签
        if 'tags' in metadata:
            keywords.extend(metadata['tags'])
        
        # 简单的关键词提取（基于词频）
        words = re.findall(r'\b[a-zA-Z\u4e00-\u9fff]{3,}\b', content.lower())
        
        # 停用词列表
        stop_words = {
            'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 
            'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 
            'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 
            'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use', 'this', 'that',
            '的', '是', '在', '有', '和', '了', '不', '与', '也', '就', '要', '可以'
        }
        
        word_freq = {}
        for word in words:
            if word not in stop_words:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # 取频率最高的词作为关键词
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        keywords.extend([word for word, freq in sorted_words[:10] if freq > 1])
        
        return list(set(keywords))  # 去重

# 全局实例
open_source_processor = OpenSourceDocumentProcessor()
