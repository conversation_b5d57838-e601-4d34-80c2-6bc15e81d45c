# Core FastAPI dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
sqlalchemy==2.0.23
alembic==1.12.1
pydantic==2.5.0
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-dotenv==1.0.0
httpx==0.25.2
aiofiles==23.2.1

# Context Engine - RAG Framework
llama-index==0.9.15
llama-index-vector-stores-qdrant==0.1.3
llama-index-embeddings-huggingface==0.1.4

# Embedding Models
sentence-transformers==2.2.2
torch==2.1.2
transformers==4.37.2

# Vector Database
qdrant-client==1.7.1
chromadb==0.4.18

# Document Processing
unstructured[all-docs]==0.11.6
python-magic==0.4.27
pypdf==3.17.1
python-docx==1.1.0
python-pptx==0.6.23

# Code Processing
tree-sitter==0.25.0
tree-sitter-python==0.23.6
tree-sitter-javascript==0.23.1
tree-sitter-typescript==0.23.2
pygments==2.17.2

# Caching and Storage
redis==5.0.1
asyncpg==0.29.0
psycopg2-binary==2.9.9

# Utilities
tiktoken==0.5.2
numpy==1.24.4
pandas==2.1.4
scikit-learn==1.3.2

# Monitoring and Logging
prometheus-client==0.19.0
structlog==23.2.0

# Development
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
mypy==1.7.1
