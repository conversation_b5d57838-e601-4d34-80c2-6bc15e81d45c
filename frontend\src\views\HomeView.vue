<template>
  <div class="home">
    <MainLayout />
    <!-- 临时调试组件 -->
    <LayoutDebug v-if="showDebug" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import MainLayout from '@/components/Layout/MainLayout.vue'
import LayoutDebug from '@/components/Debug/LayoutDebug.vue'

// 开发环境显示调试信息
const showDebug = ref(import.meta.env.SHOW_DEBUG_INFO)
</script>

<style scoped>
.home {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
}
</style>
