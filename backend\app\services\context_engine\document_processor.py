"""
Document Processor for Context Engine

Handles document parsing, chunking, and preprocessing for various file types.
"""
import os
import hashlib
import mimetypes
from pathlib import Path
from typing import List, Dict, Any, Optional, Union, Tuple
import asyncio
import aiofiles

# Document processing libraries
from unstructured.partition.auto import partition
from unstructured.chunking.title import chunk_by_title
from unstructured.cleaners.core import clean_extra_whitespace, clean_non_ascii_chars

# Code processing
import tree_sitter_python as tspython
import tree_sitter_javascript as tsjavascript
import tree_sitter_typescript as tstypescript
from tree_sitter import Language, Parser
from pygments import highlight
from pygments.lexers import get_lexer_by_name
from pygments.formatters import TextFormatter

from app.core.context_config import context_settings, SUPPORTED_LANGUAGES
import structlog

logger = structlog.get_logger(__name__)

class DocumentProcessor:
    """Service for processing various document types"""
    
    def __init__(self):
        self.chunk_size = context_settings.CHUNK_SIZE
        self.chunk_overlap = context_settings.CHUNK_OVERLAP
        self.max_file_size = context_settings.MAX_FILE_SIZE
        self.supported_types = context_settings.SUPPORTED_FILE_TYPES
        
        # Initialize code parsers
        self._parsers = {}
        self._init_code_parsers()
        
        logger.info("Initialized DocumentProcessor", chunk_size=self.chunk_size)
    
    def _init_code_parsers(self):
        """Initialize tree-sitter parsers for code analysis"""
        try:
            # Python parser
            PY_LANGUAGE = Language(tspython.language(), "python")
            py_parser = Parser()
            py_parser.set_language(PY_LANGUAGE)
            self._parsers['python'] = py_parser
            
            # JavaScript parser
            JS_LANGUAGE = Language(tsjavascript.language(), "javascript")
            js_parser = Parser()
            js_parser.set_language(JS_LANGUAGE)
            self._parsers['javascript'] = js_parser
            
            # TypeScript parser
            TS_LANGUAGE = Language(tstypescript.language(), "typescript")
            ts_parser = Parser()
            ts_parser.set_language(TS_LANGUAGE)
            self._parsers['typescript'] = ts_parser
            
            logger.info("Initialized code parsers", languages=list(self._parsers.keys()))
            
        except Exception as e:
            logger.warning("Failed to initialize some code parsers", error=str(e))
    
    async def process_file(self, file_path: str) -> Dict[str, Any]:
        """Process a single file and return document metadata"""
        try:
            file_path = Path(file_path)
            
            # Validate file
            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            
            file_size = file_path.stat().st_size
            if file_size > self.max_file_size:
                raise ValueError(f"File too large: {file_size} bytes")
            
            # Determine file type
            file_type = self._get_file_type(file_path)
            if file_type not in self.supported_types:
                raise ValueError(f"Unsupported file type: {file_type}")
            
            # Calculate file hash
            file_hash = await self._calculate_file_hash(file_path)
            
            # Process based on file type
            if file_type in ['py', 'js', 'ts', 'vue', 'jsx', 'tsx']:
                content, chunks = await self._process_code_file(file_path, file_type)
            else:
                content, chunks = await self._process_document_file(file_path, file_type)
            
            return {
                'file_path': str(file_path),
                'file_type': file_type,
                'file_size': file_size,
                'file_hash': file_hash,
                'content': content,
                'chunks': chunks,
                'chunk_count': len(chunks)
            }
            
        except Exception as e:
            logger.error("Failed to process file", file_path=str(file_path), error=str(e))
            raise
    
    async def _process_document_file(self, file_path: Path, file_type: str) -> Tuple[str, List[Dict[str, Any]]]:
        """Process document files (PDF, DOCX, TXT, MD, HTML)"""
        try:
            # Use unstructured.io for document parsing
            elements = await asyncio.to_thread(
                partition,
                filename=str(file_path),
                strategy="auto",
                include_page_breaks=True
            )
            
            # Clean and extract text
            full_content = ""
            element_data = []
            
            for element in elements:
                # Clean text
                text = clean_extra_whitespace(element.text)
                text = clean_non_ascii_chars(text)
                
                if text.strip():
                    full_content += text + "\n\n"
                    element_data.append({
                        'type': element.category,
                        'text': text,
                        'metadata': element.metadata.to_dict() if hasattr(element, 'metadata') else {}
                    })
            
            # Create chunks
            chunks = await self._create_text_chunks(full_content, element_data)
            
            logger.info("Processed document file", 
                       file_path=str(file_path), 
                       elements=len(elements), 
                       chunks=len(chunks))
            
            return full_content.strip(), chunks
            
        except Exception as e:
            logger.error("Failed to process document file", file_path=str(file_path), error=str(e))
            raise
    
    async def _process_code_file(self, file_path: Path, file_type: str) -> Tuple[str, List[Dict[str, Any]]]:
        """Process code files with syntax analysis"""
        try:
            # Read file content
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                content = await f.read()
            
            # Determine language
            language = self._get_language_from_extension(file_type)
            
            # Parse code structure if parser available
            code_structure = None
            if language in self._parsers:
                code_structure = await self._parse_code_structure(content, language)
            
            # Create code chunks
            chunks = await self._create_code_chunks(content, language, code_structure)
            
            logger.info("Processed code file", 
                       file_path=str(file_path), 
                       language=language, 
                       chunks=len(chunks))
            
            return content, chunks
            
        except Exception as e:
            logger.error("Failed to process code file", file_path=str(file_path), error=str(e))
            raise
    
    async def _create_text_chunks(self, content: str, elements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create chunks from text content"""
        chunks = []
        
        # Simple chunking by character count with overlap
        words = content.split()
        current_chunk = []
        current_size = 0
        
        for word in words:
            word_size = len(word) + 1  # +1 for space
            
            if current_size + word_size > self.chunk_size and current_chunk:
                # Create chunk
                chunk_text = ' '.join(current_chunk)
                chunks.append({
                    'content': chunk_text,
                    'chunk_index': len(chunks),
                    'start_char': len(' '.join(chunks)) if chunks else 0,
                    'end_char': len(' '.join(chunks)) + len(chunk_text) if chunks else len(chunk_text),
                    'token_count': len(current_chunk),
                    'metadata': {'type': 'text_chunk'}
                })
                
                # Start new chunk with overlap
                overlap_words = current_chunk[-self.chunk_overlap:] if len(current_chunk) > self.chunk_overlap else current_chunk
                current_chunk = overlap_words + [word]
                current_size = sum(len(w) + 1 for w in current_chunk)
            else:
                current_chunk.append(word)
                current_size += word_size
        
        # Add final chunk
        if current_chunk:
            chunk_text = ' '.join(current_chunk)
            chunks.append({
                'content': chunk_text,
                'chunk_index': len(chunks),
                'start_char': len(' '.join([c['content'] for c in chunks])) if chunks else 0,
                'end_char': len(' '.join([c['content'] for c in chunks])) + len(chunk_text) if chunks else len(chunk_text),
                'token_count': len(current_chunk),
                'metadata': {'type': 'text_chunk'}
            })
        
        return chunks
    
    async def _create_code_chunks(
        self, 
        content: str, 
        language: str, 
        structure: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Create chunks from code content"""
        chunks = []
        lines = content.split('\n')
        
        # Try to chunk by functions/classes if structure is available
        if structure and 'functions' in structure:
            for func in structure['functions']:
                start_line = func.get('start_line', 0)
                end_line = func.get('end_line', len(lines))
                
                func_content = '\n'.join(lines[start_line:end_line + 1])
                
                chunks.append({
                    'content': func_content,
                    'chunk_index': len(chunks),
                    'start_line': start_line,
                    'end_line': end_line,
                    'token_count': len(func_content.split()),
                    'metadata': {
                        'type': 'function',
                        'language': language,
                        'function_name': func.get('name', 'unknown'),
                        'docstring': func.get('docstring', '')
                    }
                })
        else:
            # Fallback to simple line-based chunking
            chunk_lines = []
            for i, line in enumerate(lines):
                chunk_lines.append(line)
                
                if len('\n'.join(chunk_lines)) > self.chunk_size:
                    chunk_content = '\n'.join(chunk_lines)
                    chunks.append({
                        'content': chunk_content,
                        'chunk_index': len(chunks),
                        'start_line': i - len(chunk_lines) + 1,
                        'end_line': i,
                        'token_count': len(chunk_content.split()),
                        'metadata': {
                            'type': 'code_block',
                            'language': language
                        }
                    })
                    
                    # Start new chunk with overlap
                    overlap_size = min(self.chunk_overlap, len(chunk_lines))
                    chunk_lines = chunk_lines[-overlap_size:]
            
            # Add final chunk
            if chunk_lines:
                chunk_content = '\n'.join(chunk_lines)
                chunks.append({
                    'content': chunk_content,
                    'chunk_index': len(chunks),
                    'start_line': len(lines) - len(chunk_lines),
                    'end_line': len(lines) - 1,
                    'token_count': len(chunk_content.split()),
                    'metadata': {
                        'type': 'code_block',
                        'language': language
                    }
                })
        
        return chunks
    
    async def _parse_code_structure(self, content: str, language: str) -> Dict[str, Any]:
        """Parse code structure using tree-sitter"""
        try:
            parser = self._parsers.get(language)
            if not parser:
                return {}
            
            tree = await asyncio.to_thread(parser.parse, content.encode('utf-8'))
            root_node = tree.root_node
            
            structure = {
                'functions': [],
                'classes': [],
                'imports': []
            }
            
            # Extract functions and classes (simplified)
            await self._extract_code_elements(root_node, content, structure)
            
            return structure
            
        except Exception as e:
            logger.warning("Failed to parse code structure", language=language, error=str(e))
            return {}
    
    async def _extract_code_elements(self, node, content: str, structure: Dict[str, Any]):
        """Extract code elements from AST node"""
        # This is a simplified implementation
        # In a full implementation, you'd handle different node types properly
        
        if node.type == 'function_definition':
            name_node = node.child_by_field_name('name')
            if name_node:
                structure['functions'].append({
                    'name': content[name_node.start_byte:name_node.end_byte],
                    'start_line': node.start_point[0],
                    'end_line': node.end_point[0]
                })
        
        # Recursively process child nodes
        for child in node.children:
            await self._extract_code_elements(child, content, structure)
    
    def _get_file_type(self, file_path: Path) -> str:
        """Determine file type from extension"""
        return file_path.suffix.lstrip('.').lower()
    
    def _get_language_from_extension(self, file_type: str) -> str:
        """Map file extension to programming language"""
        mapping = {
            'py': 'python',
            'js': 'javascript',
            'jsx': 'javascript',
            'ts': 'typescript',
            'tsx': 'typescript',
            'vue': 'vue'
        }
        return mapping.get(file_type, file_type)
    
    async def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate MD5 hash of file"""
        hash_md5 = hashlib.md5()
        async with aiofiles.open(file_path, 'rb') as f:
            async for chunk in self._read_chunks(f):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    async def _read_chunks(self, file_obj, chunk_size: int = 8192):
        """Read file in chunks"""
        while True:
            chunk = await file_obj.read(chunk_size)
            if not chunk:
                break
            yield chunk
    
    def get_processor_info(self) -> Dict[str, Any]:
        """Get processor configuration info"""
        return {
            'chunk_size': self.chunk_size,
            'chunk_overlap': self.chunk_overlap,
            'max_file_size': self.max_file_size,
            'supported_types': self.supported_types,
            'available_parsers': list(self._parsers.keys())
        }
