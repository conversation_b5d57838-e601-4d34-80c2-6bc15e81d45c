Uninstalled 10 packages in 38ms
Installed 10 packages in 8ms
INFO:     Will watch for changes in these directories: ['/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/backend']
INFO:     Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
INFO:     Started reloader process [54055] using WatchFiles
INFO:     Started server process [54057]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     127.0.0.1:50134 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:50184 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:50342 - "POST /api/chat/send HTTP/1.1" 200 OK
INFO:     127.0.0.1:50511 - "POST /api/chat/send HTTP/1.1" 200 OK
INFO:     127.0.0.1:50992 - "POST /api/chat/send HTTP/1.1" 200 OK
INFO:     127.0.0.1:54648 - "POST /api/chat/send HTTP/1.1" 200 OK
INFO:     127.0.0.1:54747 - "POST /api/chat/send HTTP/1.1" 200 OK
INFO:     127.0.0.1:58539 - "POST /api/chat/send HTTP/1.1" 200 OK
INFO:     127.0.0.1:61758 - "POST /api/chat/send HTTP/1.1" 200 OK
