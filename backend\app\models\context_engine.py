"""
Database models for Context Engine
"""
from sqlalchemy import Column, String, Text, Integer, DateTime, Boolean, JSON, ForeignKey, Enum
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
import uuid

from app.db.database import Base

class DocumentStatus(str, enum.Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class DocumentType(str, enum.Enum):
    PDF = "pdf"
    DOCX = "docx"
    TXT = "txt"
    MD = "md"
    HTML = "html"
    CODE = "code"

class EmbeddingModelType(str, enum.Enum):
    MINI_LM = "all-MiniLM-L6-v2"
    BGE_LARGE_ZH = "bge-large-zh-v1.5"
    MPNET_BASE = "all-mpnet-base-v2"
    MULTILINGUAL = "paraphrase-multilingual-MiniLM-L12-v2"

class Collection(Base):
    """Knowledge base collections"""
    __tablename__ = "collections"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), unique=True, nullable=False, index=True)
    description = Column(Text)
    category = Column(String(100), index=True)
    subcategory = Column(String(100), index=True)
    embedding_model = Column(Enum(EmbeddingModelType), default=EmbeddingModelType.MINI_LM)
    vector_dimension = Column(Integer, default=384)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    metadata = Column(JSON, default=dict)
    
    # Relationships
    documents = relationship("Document", back_populates="collection", cascade="all, delete-orphan")
    code_examples = relationship("CodeExample", back_populates="collection", cascade="all, delete-orphan")

class Document(Base):
    """Documents in the knowledge base"""
    __tablename__ = "documents"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    collection_id = Column(UUID(as_uuid=True), ForeignKey("collections.id"), nullable=False)
    title = Column(String(500), nullable=False, index=True)
    content = Column(Text, nullable=False)
    file_path = Column(String(1000))
    file_size = Column(Integer)
    file_hash = Column(String(64), index=True)
    document_type = Column(Enum(DocumentType), nullable=False, index=True)
    status = Column(Enum(DocumentStatus), default=DocumentStatus.PENDING, index=True)
    language = Column(String(10), default="en", index=True)
    tags = Column(ARRAY(String), default=list)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    processed_at = Column(DateTime(timezone=True))
    metadata = Column(JSON, default=dict)
    
    # Relationships
    collection = relationship("Collection", back_populates="documents")
    chunks = relationship("DocumentChunk", back_populates="document", cascade="all, delete-orphan")

class DocumentChunk(Base):
    """Document chunks for vector storage"""
    __tablename__ = "document_chunks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    chunk_index = Column(Integer, nullable=False)
    content = Column(Text, nullable=False)
    content_hash = Column(String(64), index=True)
    token_count = Column(Integer)
    vector_id = Column(String(255), index=True)  # Reference to vector in Qdrant
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    metadata = Column(JSON, default=dict)
    
    # Relationships
    document = relationship("Document", back_populates="chunks")

class CodeExample(Base):
    """Code examples in the knowledge base"""
    __tablename__ = "code_examples"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    collection_id = Column(UUID(as_uuid=True), ForeignKey("collections.id"), nullable=False)
    title = Column(String(500), nullable=False, index=True)
    description = Column(Text)
    code = Column(Text, nullable=False)
    language = Column(String(50), nullable=False, index=True)
    framework = Column(String(100), index=True)
    category = Column(String(100), index=True)
    tags = Column(ARRAY(String), default=list)
    dependencies = Column(ARRAY(String), default=list)
    file_path = Column(String(1000))
    vector_id = Column(String(255), index=True)  # Reference to vector in Qdrant
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    metadata = Column(JSON, default=dict)
    
    # Relationships
    collection = relationship("Collection", back_populates="code_examples")

class QueryLog(Base):
    """Query logs for analytics and optimization"""
    __tablename__ = "query_logs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), index=True)
    session_id = Column(UUID(as_uuid=True), index=True)
    query_text = Column(Text, nullable=False)
    query_type = Column(String(50), index=True)
    collection_ids = Column(ARRAY(UUID), default=list)
    results_count = Column(Integer)
    response_time_ms = Column(Integer)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    metadata = Column(JSON, default=dict)

class SystemMetric(Base):
    """System metrics for monitoring"""
    __tablename__ = "system_metrics"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    metric_name = Column(String(100), nullable=False, index=True)
    metric_value = Column(String(255), nullable=False)
    labels = Column(JSON, default=dict)
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), index=True)
