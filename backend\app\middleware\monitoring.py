"""
API监控中间件

提供API请求监控、性能跟踪和指标收集功能。
"""
import time
import uuid
from typing import Callable
from fastapi import Request, Response
from fastapi.responses import JSONResponse
import logging

from app.core.metrics import record_counter, record_timing, record_gauge

logger = logging.getLogger(__name__)

class MonitoringMiddleware:
    """API监控中间件"""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        # 生成请求ID
        request_id = str(uuid.uuid4())
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 解析请求信息
        request = Request(scope, receive)
        method = request.method
        path = request.url.path
        
        # 添加请求ID到headers
        scope["headers"] = list(scope.get("headers", []))
        scope["headers"].append((b"x-request-id", request_id.encode()))
        
        # 记录请求开始
        logger.info(f"Request started: {method} {path}", extra={
            "request_id": request_id,
            "method": method,
            "path": path,
            "user_agent": request.headers.get("user-agent", ""),
            "remote_addr": request.client.host if request.client else ""
        })
        
        # 记录请求计数
        record_counter("http_requests_total", tags={
            "method": method,
            "endpoint": self._normalize_path(path)
        })
        
        status_code = 200
        response_size = 0
        
        async def send_wrapper(message):
            nonlocal status_code, response_size
            
            if message["type"] == "http.response.start":
                status_code = message["status"]
            elif message["type"] == "http.response.body":
                if "body" in message:
                    response_size += len(message["body"])
            
            await send(message)
        
        try:
            await self.app(scope, receive, send_wrapper)
        except Exception as e:
            status_code = 500
            logger.error(f"Request failed: {method} {path}", extra={
                "request_id": request_id,
                "error": str(e)
            })
            raise
        finally:
            # 计算请求耗时
            duration_ms = (time.time() - start_time) * 1000
            
            # 记录请求完成
            logger.info(f"Request completed: {method} {path}", extra={
                "request_id": request_id,
                "status_code": status_code,
                "duration_ms": duration_ms,
                "response_size": response_size
            })
            
            # 记录性能指标
            tags = {
                "method": method,
                "endpoint": self._normalize_path(path),
                "status_code": str(status_code)
            }
            
            record_timing("http_request_duration", duration_ms, tags)
            record_gauge("http_response_size_bytes", response_size, tags)
            
            # 记录错误计数
            if status_code >= 400:
                record_counter("http_errors_total", tags=tags)
            
            # 记录慢请求
            if duration_ms > 1000:  # 超过1秒的请求
                record_counter("http_slow_requests_total", tags=tags)
                logger.warning(f"Slow request detected: {method} {path} took {duration_ms:.2f}ms")
    
    def _normalize_path(self, path: str) -> str:
        """标准化路径，用于指标标签"""
        # 移除查询参数
        if "?" in path:
            path = path.split("?")[0]
        
        # 替换路径参数为占位符
        import re
        # 替换UUID
        path = re.sub(r'/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', '/{id}', path)
        # 替换数字ID
        path = re.sub(r'/\d+', '/{id}', path)
        
        return path

async def add_security_headers(request: Request, call_next):
    """添加安全头中间件"""
    response = await call_next(request)
    
    # 添加安全头
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
    
    return response

async def add_cors_headers(request: Request, call_next):
    """添加CORS头中间件"""
    response = await call_next(request)
    
    # 允许的源
    allowed_origins = [
        "http://localhost:3000",
        "http://localhost:5173",
        "http://localhost:8080"
    ]
    
    origin = request.headers.get("origin")
    if origin in allowed_origins:
        response.headers["Access-Control-Allow-Origin"] = origin
        response.headers["Access-Control-Allow-Credentials"] = "true"
        response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
        response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With"
    
    return response

async def rate_limiting_middleware(request: Request, call_next):
    """简单的限流中间件"""
    from collections import defaultdict
    import asyncio
    
    # 简单的内存限流器（生产环境应使用Redis）
    if not hasattr(rate_limiting_middleware, "requests"):
        rate_limiting_middleware.requests = defaultdict(list)
    
    client_ip = request.client.host if request.client else "unknown"
    current_time = time.time()
    
    # 清理过期记录
    rate_limiting_middleware.requests[client_ip] = [
        req_time for req_time in rate_limiting_middleware.requests[client_ip]
        if current_time - req_time < 60  # 1分钟窗口
    ]
    
    # 检查请求频率
    if len(rate_limiting_middleware.requests[client_ip]) >= 100:  # 每分钟100次请求
        record_counter("http_rate_limited_total", tags={"client_ip": client_ip})
        return JSONResponse(
            status_code=429,
            content={"error": "Rate limit exceeded", "retry_after": 60}
        )
    
    # 记录请求时间
    rate_limiting_middleware.requests[client_ip].append(current_time)
    
    response = await call_next(request)
    return response

async def error_handling_middleware(request: Request, call_next):
    """全局错误处理中间件"""
    try:
        response = await call_next(request)
        return response
    except Exception as e:
        # 记录错误
        logger.error(f"Unhandled error in {request.method} {request.url.path}: {str(e)}", 
                    exc_info=True)
        
        # 记录错误指标
        record_counter("http_unhandled_errors_total", tags={
            "method": request.method,
            "path": request.url.path,
            "error_type": type(e).__name__
        })
        
        # 返回通用错误响应
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal server error",
                "message": "An unexpected error occurred",
                "request_id": request.headers.get("x-request-id", "unknown")
            }
        )

class HealthCheckMiddleware:
    """健康检查中间件"""
    
    def __init__(self):
        self.start_time = time.time()
        self.request_count = 0
        self.error_count = 0
    
    async def __call__(self, request: Request, call_next):
        self.request_count += 1
        
        try:
            response = await call_next(request)
            
            # 记录健康指标
            record_gauge("app_uptime_seconds", time.time() - self.start_time)
            record_gauge("app_requests_total", self.request_count)
            record_gauge("app_errors_total", self.error_count)
            
            return response
        except Exception as e:
            self.error_count += 1
            raise

# 健康检查中间件实例
health_check_middleware = HealthCheckMiddleware()

async def request_logging_middleware(request: Request, call_next):
    """请求日志中间件"""
    start_time = time.time()
    
    # 记录请求信息
    logger.info(f"Incoming request: {request.method} {request.url}")
    
    response = await call_next(request)
    
    # 记录响应信息
    duration = time.time() - start_time
    logger.info(f"Request completed: {request.method} {request.url} - "
               f"Status: {response.status_code} - Duration: {duration:.3f}s")
    
    return response

def setup_middleware(app):
    """设置所有中间件"""
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.middleware.gzip import GZipMiddleware
    
    # 添加GZIP压缩
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[
            "http://localhost:3000",
            "http://localhost:5173", 
            "http://localhost:8080"
        ],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 添加自定义中间件
    app.middleware("http")(add_security_headers)
    app.middleware("http")(rate_limiting_middleware)
    app.middleware("http")(error_handling_middleware)
    app.middleware("http")(request_logging_middleware)
    app.middleware("http")(health_check_middleware)
    
    # 添加监控中间件
    app.add_middleware(MonitoringMiddleware)
    
    logger.info("All middleware setup completed")
