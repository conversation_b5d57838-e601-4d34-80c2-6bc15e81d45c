@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM Artifacts Chat 本地开发环境启动脚本
REM 自动启动前后端开发服务器

set PROJECT_NAME=Artifacts Chat
set BACKEND_DIR=backend
set FRONTEND_DIR=frontend

echo 启动 %PROJECT_NAME% 本地开发环境
echo ==================================================

REM 检查基础环境
:check_requirements
echo 检查环境要求...

REM 检查 Python
python --version >nul 2>&1
if errorlevel 1 (
    echo Python 未安装
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo Python 版本: %PYTHON_VERSION%

REM 检查 Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo Node.js 未安装
    exit /b 1
)

for /f %%i in ('node --version') do set NODE_VERSION=%%i
echo Node.js 版本: %NODE_VERSION%

REM 设置后端环境
:setup_backend
echo 设置后端环境...

cd %BACKEND_DIR%

REM 检查是否有 uv
uv --version >nul 2>&1
if not errorlevel 1 (
    echo 使用 uv 管理 Python 依赖...
    
    REM 检查是否已有虚拟环境
    if not exist ".venv" (
        echo 创建 uv 虚拟环境...
        uv venv
    )
    
    REM 激活虚拟环境
    call .venv\Scripts\activate.bat
    
    REM 安装依赖
    echo 使用 uv 安装 Python 依赖...
    uv pip install -r requirements.txt --quiet
    
) else (
    echo uv 未找到，使用传统 venv...
    
    REM 创建虚拟环境
    if not exist "venv" (
        echo 创建 Python 虚拟环境...
        python -m venv venv
    )
    
    REM 激活虚拟环境
    call venv\Scripts\activate.bat
    
    REM 升级 pip
    python -m pip install --upgrade pip --quiet
    
    REM 安装依赖
    echo 安装 Python 依赖...
    pip install -r requirements.txt --quiet
)

REM 验证安装
python -c "import fastapi; print('FastAPI 安装成功')" 2>nul
if errorlevel 1 (
    echo FastAPI 安装失败
    exit /b 1
)

REM 检查环境变量文件
if not exist ".env" (
    echo 创建 .env 文件...
    if exist ".env.example" (
        copy .env.example .env >nul
        echo 请编辑 backend\.env 文件配置 API 密钥
    ) else (
        echo # 基础配置 > .env
        echo DEBUG=true >> .env
        echo LOG_LEVEL=INFO >> .env
        echo. >> .env
        echo # 数据库配置 (开发环境使用SQLite) >> .env
        echo DATABASE_URL=sqlite:///./artifacts_chat.db >> .env
        echo. >> .env
        echo # LLM API配置 (至少配置一个) >> .env
        echo OPENAI_API_KEY=sk-your-openai-api-key-here >> .env
        echo # CLAUDE_API_KEY=sk-ant-your-claude-api-key-here >> .env
        echo. >> .env
        echo # 安全配置 >> .env
        echo SECRET_KEY=your-development-secret-key-change-in-production >> .env
    )
)

REM 初始化数据库
echo 初始化数据库...
python -c "from app.database import engine; from app.models import Base; Base.metadata.create_all(bind=engine); print('数据库初始化成功')" 2>nul || echo 数据库初始化跳过

echo 后端环境准备完成
cd ..

REM 设置前端环境
:setup_frontend
echo 设置前端环境...

cd %FRONTEND_DIR%

REM 检查包管理器
set PKG_MANAGER=npm
pnpm --version >nul 2>&1
if not errorlevel 1 set PKG_MANAGER=pnpm
yarn --version >nul 2>&1
if not errorlevel 1 set PKG_MANAGER=yarn

echo 使用包管理器: %PKG_MANAGER%

REM 安装依赖
if not exist "node_modules" (
    echo 安装前端依赖...
    %PKG_MANAGER% install
) else (
    echo 前端依赖已安装
)

REM 检查环境变量文件
if not exist ".env.local" (
    echo 创建前端环境配置...
    if exist ".env.example" (
        copy .env.example .env.local >nul
    ) else (
        echo # API基础URL > .env.local
        echo VITE_API_BASE_URL=http://localhost:8080 >> .env.local
        echo. >> .env.local
        echo # 开发模式配置 >> .env.local
        echo VITE_DEV_MODE=true >> .env.local
        echo VITE_ENABLE_MOCK=false >> .env.local
    )
)

echo 前端环境准备完成
cd ..

REM 启动开发服务器
:start_dev_servers
echo 启动开发服务器...

REM 启动后端服务器
echo 启动后端服务器...
cd %BACKEND_DIR%

REM 激活对应的虚拟环境
if exist ".venv" (
    call .venv\Scripts\activate.bat
) else (
    call venv\Scripts\activate.bat
)

REM 获取进程ID并保存
for /f "tokens=2" %%i in ('wmic process call create "uvicorn app.main:app --reload --host 0.0.0.0 --port 8080" ^| find "ProcessId"') do (
    echo %%i > ..\backend.pid
)

cd ..

REM 等待后端启动
echo 等待后端服务启动...
timeout /t 5 /nobreak >nul

REM 验证后端服务
curl -s http://localhost:8080/health >nul 2>&1
if not errorlevel 1 (
    echo 后端服务启动成功
) else (
    echo 后端服务可能需要更多时间启动
)

REM 启动前端服务器
echo 启动前端服务器...
cd %FRONTEND_DIR%

REM 获取进程ID并保存
for /f "tokens=2" %%i in ('wmic process call create "%PKG_MANAGER% dev" ^| find "ProcessId"') do (
    echo %%i > ..\frontend.pid
)

cd ..

REM 等待前端启动
echo 等待前端服务启动...
timeout /t 8 /nobreak >nul

REM 显示状态信息
:show_status
echo.
echo 开发环境启动完成！
echo ==================================================
echo 前端应用: http://localhost:5173
echo 后端 API: http://localhost:8080
echo API 文档: http://localhost:8080/docs
echo.
echo 服务状态:

REM 检查后端状态
curl -s http://localhost:8080/health >nul 2>&1
if not errorlevel 1 (
    echo 后端服务: 运行中
) else (
    echo 后端服务: 未响应
)

REM 检查前端状态
curl -s http://localhost:5173 >nul 2>&1
if not errorlevel 1 (
    echo 前端服务: 运行中
) else (
    echo 前端服务: 启动中...
)

echo.
echo 使用提示:
echo • 查看后端日志: type backend.log
echo • 查看前端日志: type frontend.log
echo • 停止所有服务: scripts\stop-dev-local.bat
echo.
echo 开发工具:
echo • 后端代码修改会自动重载
echo • 前端代码修改会自动热更新
echo • 使用 Ctrl+C 停止此脚本（服务继续运行）

echo.
echo 按任意键退出监控（服务继续运行）...
pause >nul

