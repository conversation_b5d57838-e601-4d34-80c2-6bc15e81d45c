version: '3.8'

services:
  # Qdrant Vector Database (OrbStack optimized)
  qdrant:
    image: qdrant/qdrant:v1.6.1
    container_name: qdrant-dev
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
      - QDRANT__LOG_LEVEL=INFO
      - QDRANT__SERVICE__HOST=0.0.0.0
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:6333/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Chroma Vector Database (Alternative for development)
  chroma:
    image: chromadb/chroma:0.4.18
    container_name: chroma-dev
    ports:
      - "8000:8000"
    volumes:
      - chroma_data:/chroma/chroma
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
    restart: unless-stopped
    profiles: ["chroma"]  # Optional service

  # Redis Cache (OrbStack optimized)
  redis:
    image: redis:7-alpine
    container_name: redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru --bind 0.0.0.0
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: postgres-dev
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    environment:
      - POSTGRES_DB=context_engine
      - POSTGRES_USER=context_user
      - POSTGRES_PASSWORD=dev_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U context_user -d context_engine"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Context Engine Backend (Development)
  context-engine:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: context-engine-dev
    ports:
      - "8080:8080"
    volumes:
      - ./backend:/app
      - ./knowledge:/app/knowledge
      - model_cache:/app/models
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=****************************************************/context_engine
      - VECTOR_DB_TYPE=qdrant
      - VECTOR_DB_URL=http://qdrant:6333
      - REDIS_URL=redis://redis:6379
      - EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
      - LOG_LEVEL=DEBUG
    depends_on:
      qdrant:
        condition: service_healthy
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    restart: unless-stopped
    profiles: ["backend"]  # Optional service for development

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:v2.47.2
    container_name: prometheus-dev
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    profiles: ["monitoring"]

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:10.2.0
    container_name: grafana-dev
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    restart: unless-stopped
    profiles: ["monitoring"]

volumes:
  qdrant_data:
    driver: local
  chroma_data:
    driver: local
  redis_data:
    driver: local
  postgres_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  model_cache:
    driver: local

networks:
  default:
    name: context-engine-dev
    driver: bridge
