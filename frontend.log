
> artifacts-chat-frontend@0.0.0 dev /Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend
> vite


  VITE v7.0.4  ready in 310 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
  ➜  Vue DevTools: Open http://localhost:5173/__devtools__/ as a separate window
  ➜  Vue DevTools: Press Option(⌥)+Shift(⇧)+D in App to toggle the Vue DevTools
10:06:58 AM [vite] (client) hmr update /src/components/Chat/ChatInterface.vue
10:40:33 AM [vite] (client) Pre-transform error: Invalid end tag.
  Plugin: vite-plugin-vue-inspector
  File: /Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/src/components/Chat/ChatInterface.vue
10:40:33 AM [vite] Internal server error: Invalid end tag.
  Plugin: vite-plugin-vue-inspector
  File: /Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/src/components/Chat/ChatInterface.vue
      at createCompilerError (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-core@3.5.17/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:1364:17)
      at emitError (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-core@3.5.17/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2949:5)
      at Object.onclosetag (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-core@3.5.17/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2318:9)
      at Tokenizer.stateInClosingTagName (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-core@3.5.17/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:785:16)
      at Tokenizer.parse (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-core@3.5.17/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:1143:16)
      at Object.baseParse (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-core@3.5.17/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2988:13)
      at parse (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-dom@3.5.17/node_modules/@vue/compiler-dom/dist/compiler-dom.cjs.js:907:23)
      at result (file:///Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/vite-plugin-vue-inspector@5.3.2_vite@7.0.4_@types+node@22.16.4_/node_modules/vite-plugin-vue-inspector/dist/index.mjs:29:21)
      at new Promise (<anonymous>)
      at compileSFCTemplate (file:///Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/vite-plugin-vue-inspector@5.3.2_vite@7.0.4_@types+node@22.16.4_/node_modules/vite-plugin-vue-inspector/dist/index.mjs:26:24)
10:51:57 AM [vite] (client) Pre-transform error: Invalid end tag.
  Plugin: vite-plugin-vue-inspector
  File: /Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/src/components/Chat/ChatInterface.vue
10:51:57 AM [vite] Internal server error: Invalid end tag.
  Plugin: vite-plugin-vue-inspector
  File: /Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/src/components/Chat/ChatInterface.vue
      at createCompilerError (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-core@3.5.17/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:1364:17)
      at emitError (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-core@3.5.17/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2949:5)
      at Object.onclosetag (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-core@3.5.17/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2318:9)
      at Tokenizer.stateInClosingTagName (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-core@3.5.17/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:785:16)
      at Tokenizer.parse (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-core@3.5.17/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:1143:16)
      at Object.baseParse (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-core@3.5.17/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2988:13)
      at parse (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-dom@3.5.17/node_modules/@vue/compiler-dom/dist/compiler-dom.cjs.js:907:23)
      at result (file:///Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/vite-plugin-vue-inspector@5.3.2_vite@7.0.4_@types+node@22.16.4_/node_modules/vite-plugin-vue-inspector/dist/index.mjs:29:21)
      at new Promise (<anonymous>)
      at compileSFCTemplate (file:///Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/vite-plugin-vue-inspector@5.3.2_vite@7.0.4_@types+node@22.16.4_/node_modules/vite-plugin-vue-inspector/dist/index.mjs:26:24)
10:54:43 AM [vite] Internal server error: Invalid end tag.
  Plugin: vite-plugin-vue-inspector
  File: /Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/src/components/Chat/ChatInterface.vue
      at createCompilerError (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-core@3.5.17/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:1364:17)
      at emitError (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-core@3.5.17/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2949:5)
      at Object.onclosetag (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-core@3.5.17/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2318:9)
      at Tokenizer.stateInClosingTagName (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-core@3.5.17/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:785:16)
      at Tokenizer.parse (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-core@3.5.17/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:1143:16)
      at Object.baseParse (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-core@3.5.17/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js:2988:13)
      at parse (/Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/@vue+compiler-dom@3.5.17/node_modules/@vue/compiler-dom/dist/compiler-dom.cjs.js:907:23)
      at result (file:///Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/vite-plugin-vue-inspector@5.3.2_vite@7.0.4_@types+node@22.16.4_/node_modules/vite-plugin-vue-inspector/dist/index.mjs:29:21)
      at new Promise (<anonymous>)
      at compileSFCTemplate (file:///Users/<USER>/school/speedstudio-artifacts/artifacts-chat/frontend/node_modules/.pnpm/vite-plugin-vue-inspector@5.3.2_vite@7.0.4_@types+node@22.16.4_/node_modules/vite-plugin-vue-inspector/dist/index.mjs:26:24)
10:56:41 AM [vite] (client) hmr update /src/components/Chat/ChatInterface.vue, /src/components/Chat/ChatInterface.vue?vue&type=style&index=0&scoped=510fd436&lang.css
