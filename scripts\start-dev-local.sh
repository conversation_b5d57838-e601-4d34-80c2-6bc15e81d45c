#!/bin/bash

# Artifacts Chat 本地开发环境启动脚本
# 自动启动前后端开发服务器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

PROJECT_NAME="Artifacts Chat"
BACKEND_DIR="backend"
FRONTEND_DIR="frontend"

echo -e "${BLUE}🚀 启动 ${PROJECT_NAME} 本地开发环境${NC}"
echo "=================================================="

# 检查基础环境
check_requirements() {
    echo -e "${YELLOW}📋 检查环境要求...${NC}"
    
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ Python 3 未安装${NC}"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
    if [[ $(echo "$python_version >= 3.11" | bc -l) -eq 0 ]]; then
        echo -e "${YELLOW}⚠️  Python 版本: $python_version (推荐 3.11+)${NC}"
    else
        echo -e "${GREEN}✅ Python 版本: $python_version${NC}"
    fi
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装${NC}"
        exit 1
    fi
    
    node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [[ $node_version -lt 18 ]]; then
        echo -e "${YELLOW}⚠️  Node.js 版本: v$node_version (推荐 18+)${NC}"
    else
        echo -e "${GREEN}✅ Node.js 版本: $(node --version)${NC}"
    fi
}

# 设置后端环境
setup_backend() {
    echo -e "${YELLOW}🐍 设置后端环境...${NC}"
    
    cd $BACKEND_DIR
    
    # 检查是否有 uv
    if command -v uv &> /dev/null; then
        echo "使用 uv 管理 Python 依赖..."
        
        # 检查是否已有虚拟环境
        if [ ! -d ".venv" ]; then
            echo "创建 uv 虚拟环境..."
            uv venv
        fi
        
        # 激活虚拟环境
        source .venv/bin/activate
        
        # 安装依赖
        echo "使用 uv 安装 Python 依赖..."
        uv pip install -r requirements.txt --quiet
        
    else
        echo "uv 未找到，使用传统 venv..."
        
        # 创建虚拟环境
        if [ ! -d "venv" ]; then
            echo "创建 Python 虚拟环境..."
            python3 -m venv venv
        fi
        
        # 激活虚拟环境
        source venv/bin/activate
        
        # 升级 pip
        pip install --upgrade pip --quiet
        
        # 安装依赖
        echo "安装 Python 依赖..."
        pip install -r requirements.txt --quiet
    fi
    
    # 验证安装
    python -c "import fastapi; print('FastAPI 安装成功')" 2>/dev/null || {
        echo -e "${RED}❌ FastAPI 安装失败${NC}"
        exit 1
    }
    
    # 检查环境变量文件
    if [ ! -f ".env" ]; then
        echo -e "${YELLOW}📝 创建 .env 文件...${NC}"
        if [ -f ".env.example" ]; then
            cp .env.example .env
            echo -e "${YELLOW}⚠️  请编辑 backend/.env 文件配置 API 密钥${NC}"
        else
            cat > .env << 'EOF'
# 基础配置
DEBUG=true
LOG_LEVEL=INFO

# 数据库配置 (开发环境使用SQLite)
DATABASE_URL=sqlite:///./artifacts_chat.db

# LLM API配置 (至少配置一个)
OPENAI_API_KEY=sk-your-openai-api-key-here
# CLAUDE_API_KEY=sk-ant-your-claude-api-key-here

# 安全配置
SECRET_KEY=your-development-secret-key-change-in-production
EOF
        fi
    fi
    
    # 初始化数据库
    echo "初始化数据库..."
    python -c "
from app.database import engine
from app.models import Base
Base.metadata.create_all(bind=engine)
print('数据库初始化成功')
" 2>/dev/null || echo -e "${YELLOW}⚠️  数据库初始化跳过${NC}"
    
    echo -e "${GREEN}✅ 后端环境准备完成${NC}"
    cd ..
}

# 设置前端环境
setup_frontend() {
    echo -e "${YELLOW}📦 设置前端环境...${NC}"
    
    cd $FRONTEND_DIR
    
    # 检查包管理器
    if command -v pnpm &> /dev/null; then
        PKG_MANAGER="pnpm"
    elif command -v yarn &> /dev/null; then
        PKG_MANAGER="yarn"
    else
        PKG_MANAGER="npm"
    fi
    
    echo "使用包管理器: $PKG_MANAGER"
    
    # 安装依赖
    if [ ! -d "node_modules" ]; then
        echo "安装前端依赖..."
        $PKG_MANAGER install
    else
        echo -e "${GREEN}✅ 前端依赖已安装${NC}"
    fi
    
    # 检查环境变量文件
    if [ ! -f ".env.local" ]; then
        echo -e "${YELLOW}📝 创建前端环境配置...${NC}"
        if [ -f ".env.example" ]; then
            cp .env.example .env.local
        else
            cat > .env.local << 'EOF'
# API基础URL
VITE_API_BASE_URL=http://localhost:8080

# 开发模式配置
VITE_DEV_MODE=true
VITE_ENABLE_MOCK=false
EOF
        fi
    fi
    
    echo -e "${GREEN}✅ 前端环境准备完成${NC}"
    cd ..
}

# 启动开发服务器
start_dev_servers() {
    echo -e "${YELLOW}🚀 启动开发服务器...${NC}"
    
    # 启动后端服务器
    echo "启动后端服务器..."
    cd $BACKEND_DIR
    
    # 激活对应的虚拟环境
    if [ -d ".venv" ]; then
        source .venv/bin/activate
    else
        source venv/bin/activate
    fi
    
    nohup uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8080 > ../backend.log 2>&1 &
    BACKEND_PID=$!
    cd ..
    
    # 等待后端启动
    echo "等待后端服务启动..."
    sleep 5
    
    # 验证后端服务
    if curl -s http://localhost:8080/health > /dev/null; then
        echo -e "${GREEN}✅ 后端服务启动成功${NC}"
    else
        echo -e "${YELLOW}⚠️  后端服务可能需要更多时间启动${NC}"
    fi
    
    # 启动前端服务器
    echo "启动前端服务器..."
    cd $FRONTEND_DIR
    
    # 确定启动命令
    if command -v pnpm &> /dev/null; then
        nohup pnpm dev > ../frontend.log 2>&1 &
    elif command -v yarn &> /dev/null; then
        nohup yarn dev > ../frontend.log 2>&1 &
    else
        nohup npm run dev > ../frontend.log 2>&1 &
    fi
    
    FRONTEND_PID=$!
    cd ..
    
    # 保存进程 ID
    echo $BACKEND_PID > .backend.pid
    echo $FRONTEND_PID > .frontend.pid
    
    # 等待前端启动
    echo "等待前端服务启动..."
    sleep 8
}

# 显示状态信息
show_status() {
    echo ""
    echo -e "${GREEN}🎉 开发环境启动完成！${NC}"
    echo "=================================================="
    echo -e "${BLUE}📱 前端应用:${NC} http://localhost:5173"
    echo -e "${BLUE}🔧 后端 API:${NC} http://localhost:8080"
    echo -e "${BLUE}📚 API 文档:${NC} http://localhost:8080/docs"
    echo ""
    echo -e "${YELLOW}📋 服务状态:${NC}"
    
    # 检查后端状态
    if curl -s http://localhost:8080/health > /dev/null; then
        echo -e "${GREEN}✅ 后端服务: 运行中${NC}"
    else
        echo -e "${RED}❌ 后端服务: 未响应${NC}"
    fi
    
    # 检查前端状态
    if curl -s http://localhost:5173 > /dev/null; then
        echo -e "${GREEN}✅ 前端服务: 运行中${NC}"
    else
        echo -e "${YELLOW}⏳ 前端服务: 启动中...${NC}"
    fi
    
    echo ""
    echo -e "${YELLOW}💡 使用提示:${NC}"
    echo "• 查看后端日志: tail -f backend.log"
    echo "• 查看前端日志: tail -f frontend.log"
    echo "• 停止所有服务: ./scripts/stop-dev.sh"
    echo "• 重启服务: ./scripts/restart-dev.sh"
    echo ""
    echo -e "${YELLOW}🔧 开发工具:${NC}"
    echo "• 后端代码修改会自动重载"
    echo "• 前端代码修改会自动热更新"
    echo "• 使用 Ctrl+C 停止此脚本（服务继续运行）"
}

# 清理函数
cleanup() {
    echo -e "\n${YELLOW}🧹 清理中...${NC}"
    echo -e "${GREEN}✅ 服务将继续在后台运行${NC}"
    echo -e "${YELLOW}使用 ./scripts/stop-dev.sh 停止所有服务${NC}"
}

# 信号处理
trap cleanup EXIT INT TERM

# 主函数
main() {
    check_requirements
    setup_backend
    setup_frontend
    start_dev_servers
    show_status
    
    # 保持脚本运行以显示日志
    echo -e "${YELLOW}按 Ctrl+C 退出监控（服务继续运行）${NC}"
    echo "=================================================="
    
    # 实时显示日志
    tail -f backend.log frontend.log 2>/dev/null || {
        echo "等待日志文件生成..."
        sleep 2
        tail -f backend.log frontend.log 2>/dev/null || true
    }
}

# 运行主函数
main
