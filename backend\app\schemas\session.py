"""
Session-related Pydantic schemas
"""
from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class SessionBase(BaseModel):
    """Base session schema"""
    title: str = "New Chat"

class SessionCreate(SessionBase):
    """Schema for creating a session"""
    user_id: Optional[int] = None

class SessionResponse(SessionBase):
    """Schema for session response"""
    id: int
    user_id: Optional[int]
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True
