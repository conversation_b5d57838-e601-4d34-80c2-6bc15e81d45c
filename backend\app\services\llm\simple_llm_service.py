"""
Simple LLM Service - Using OpenAI SDK for OpenAI Compatible APIs
"""
import asyncio
import json
from typing import List, Dict, Any, Optional, AsyncGenerator
from pydantic import BaseModel
import logging
from openai import AsyncOpenAI
from app.core.config import llm_settings

logger = logging.getLogger(__name__)

class ChatMessage(BaseModel):
    role: str
    content: str
    name: Optional[str] = None

class ChatRequest(BaseModel):
    messages: List[ChatMessage]
    model: Optional[str] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    stream: bool = False

class SimpleLLMService:
    """Simple LLM service using OpenAI SDK for OpenAI compatible APIs"""

    def __init__(self):
        self.client = AsyncOpenAI(
            api_key=llm_settings.LLM_API_KEY,
            base_url=llm_settings.LLM_API_URL,
            timeout=llm_settings.LLM_TIMEOUT
        )
    
    async def chat_completion(self, request: ChatRequest) -> Dict[str, Any]:
        """Generate chat completion using OpenAI SDK"""
        try:
            # Check if API key is valid (not empty and not placeholder)
            if not llm_settings.LLM_API_KEY or llm_settings.LLM_API_KEY in ["your-api-key-here", ""]:
                raise Exception("LLM_API_KEY is not configured. Please set a valid API key in the .env file.")

            # Prepare messages for OpenAI SDK
            messages = [
                {"role": msg.role, "content": msg.content}
                for msg in request.messages
            ]

            logger.info(f"Making LLM request to {llm_settings.LLM_API_URL}")

            if request.stream:
                return self._stream_completion(request)
            else:
                # Use OpenAI SDK for non-streaming completion
                response = await self.client.chat.completions.create(
                    model=request.model or llm_settings.LLM_MODEL,
                    messages=messages,
                    temperature=request.temperature or llm_settings.LLM_TEMPERATURE,
                    max_tokens=request.max_tokens or llm_settings.LLM_MAX_TOKENS,
                    stream=False
                )

                # Convert OpenAI response to our expected format
                return {
                    "id": response.id,
                    "object": response.object,
                    "created": response.created,
                    "model": response.model,
                    "choices": [
                        {
                            "index": choice.index,
                            "message": {
                                "role": choice.message.role,
                                "content": choice.message.content
                            },
                            "finish_reason": choice.finish_reason
                        }
                        for choice in response.choices
                    ],
                    "usage": {
                        "prompt_tokens": response.usage.prompt_tokens if response.usage else 0,
                        "completion_tokens": response.usage.completion_tokens if response.usage else 0,
                        "total_tokens": response.usage.total_tokens if response.usage else 0
                    }
                }

        except Exception as e:
            logger.error(f"LLM request failed: {str(e)}")
            raise Exception(f"LLM request failed: {str(e)}")
    
    async def _stream_completion(self, request: ChatRequest) -> AsyncGenerator[str, None]:
        """Stream completion response using OpenAI SDK"""
        try:
            # Prepare messages for OpenAI SDK
            messages = [
                {"role": msg.role, "content": msg.content}
                for msg in request.messages
            ]

            # Use OpenAI SDK for streaming completion
            stream = await self.client.chat.completions.create(
                model=request.model or llm_settings.LLM_MODEL,
                messages=messages,
                temperature=request.temperature or llm_settings.LLM_TEMPERATURE,
                max_tokens=request.max_tokens or llm_settings.LLM_MAX_TOKENS,
                stream=True
            )

            async for chunk in stream:
                # Convert OpenAI chunk to our expected format
                chunk_data = {
                    "id": chunk.id,
                    "object": chunk.object,
                    "created": chunk.created,
                    "model": chunk.model,
                    "choices": []
                }

                for choice in chunk.choices:
                    choice_data = {
                        "index": choice.index,
                        "delta": {},
                        "finish_reason": choice.finish_reason
                    }

                    if choice.delta.content:
                        choice_data["delta"]["content"] = choice.delta.content
                    if choice.delta.role:
                        choice_data["delta"]["role"] = choice.delta.role

                    chunk_data["choices"].append(choice_data)

                yield f"data: {json.dumps(chunk_data)}\n\n"

            yield "data: [DONE]\n\n"

        except Exception as e:
            logger.error(f"LLM streaming failed: {str(e)}")
            error_chunk = {
                "error": {
                    "message": str(e),
                    "type": "internal_error"
                }
            }
            yield f"data: {json.dumps(error_chunk)}\n\n"
    
    async def list_models(self) -> List[Dict[str, Any]]:
        """List available models using OpenAI SDK"""
        try:
            models = await self.client.models.list()
            return [
                {
                    "id": model.id,
                    "object": model.object,
                    "owned_by": model.owned_by,
                    "display_name": model.id
                }
                for model in models.data
            ]

        except Exception as e:
            logger.warning(f"Failed to fetch models: {str(e)}")
            # Return default model info
            return [{
                "id": llm_settings.LLM_MODEL,
                "object": "model",
                "owned_by": "configured",
                "display_name": llm_settings.LLM_MODEL
            }]
    
    async def health_check(self) -> Dict[str, Any]:
        """Check LLM service health"""
        try:
            models = await self.list_models()
            return {
                "status": "healthy",
                "model": llm_settings.LLM_MODEL,
                "api_url": llm_settings.LLM_API_URL,
                "models_available": len(models)
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "model": llm_settings.LLM_MODEL,
                "api_url": llm_settings.LLM_API_URL
            }
    
    async def close(self):
        """Close the OpenAI client"""
        await self.client.close()

# Global service instance
_llm_service = None

async def get_llm_service() -> SimpleLLMService:
    """Get or create LLM service instance"""
    global _llm_service
    if _llm_service is None:
        _llm_service = SimpleLLMService()
    return _llm_service

async def close_llm_service():
    """Close LLM service"""
    global _llm_service
    if _llm_service:
        await _llm_service.close()
        _llm_service = None
