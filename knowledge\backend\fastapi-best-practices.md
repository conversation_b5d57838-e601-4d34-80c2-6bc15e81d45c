# FastAPI 最佳实践指南

## 概述

FastAPI 是一个现代、快速的 Python Web 框架，用于构建 API。本指南介绍 FastAPI 开发的最佳实践。

## 项目结构

### 推荐的目录结构

```
app/
├── __init__.py
├── main.py              # FastAPI 应用入口
├── core/                # 核心配置
│   ├── __init__.py
│   ├── config.py        # 配置设置
│   ├── security.py      # 安全相关
│   └── database.py      # 数据库配置
├── api/                 # API 路由
│   ├── __init__.py
│   ├── deps.py          # 依赖项
│   └── v1/              # API 版本
│       ├── __init__.py
│       ├── users.py
│       └── items.py
├── models/              # 数据库模型
│   ├── __init__.py
│   ├── user.py
│   └── item.py
├── schemas/             # Pydantic 模式
│   ├── __init__.py
│   ├── user.py
│   └── item.py
├── services/            # 业务逻辑
│   ├── __init__.py
│   ├── user_service.py
│   └── item_service.py
└── utils/               # 工具函数
    ├── __init__.py
    └── helpers.py
```

## 配置管理

### 使用 Pydantic Settings

```python
# core/config.py
from pydantic import BaseSettings, validator
from typing import List, Optional

class Settings(BaseSettings):
    # 应用配置
    APP_NAME: str = "FastAPI App"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # 数据库配置
    DATABASE_URL: str
    
    # 安全配置
    SECRET_KEY: str
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # CORS 配置
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000"]
    
    # Redis 配置
    REDIS_URL: Optional[str] = None
    
    class Config:
        env_file = ".env"
        case_sensitive = True
    
    @validator("ALLOWED_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

settings = Settings()
```

## 依赖注入

### 数据库依赖

```python
# api/deps.py
from sqlalchemy.orm import Session
from fastapi import Depends, HTTPException, status
from core.database import SessionLocal
from core.security import get_current_user
from models.user import User

def get_db() -> Session:
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """获取当前活跃用户"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user

def get_current_superuser(
    current_user: User = Depends(get_current_user)
) -> User:
    """获取当前超级用户"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Not enough permissions"
        )
    return current_user
```

## 路由设计

### RESTful API 设计

```python
# api/v1/users.py
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from api.deps import get_db, get_current_active_user
from schemas.user import User, UserCreate, UserUpdate
from services.user_service import UserService

router = APIRouter()

@router.get("/", response_model=List[User])
async def read_users(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取用户列表"""
    user_service = UserService(db)
    users = user_service.get_users(skip=skip, limit=limit)
    return users

@router.post("/", response_model=User, status_code=status.HTTP_201_CREATED)
async def create_user(
    user_in: UserCreate,
    db: Session = Depends(get_db)
):
    """创建新用户"""
    user_service = UserService(db)
    
    # 检查用户是否已存在
    if user_service.get_by_email(user_in.email):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    user = user_service.create(user_in)
    return user

@router.get("/{user_id}", response_model=User)
async def read_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取特定用户"""
    user_service = UserService(db)
    user = user_service.get(user_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return user

@router.put("/{user_id}", response_model=User)
async def update_user(
    user_id: int,
    user_in: UserUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """更新用户信息"""
    user_service = UserService(db)
    user = user_service.get(user_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # 权限检查
    if user.id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    user = user_service.update(user, user_in)
    return user

@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_superuser)
):
    """删除用户"""
    user_service = UserService(db)
    user = user_service.get(user_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    user_service.delete(user_id)
```

## 数据验证

### Pydantic 模式

```python
# schemas/user.py
from pydantic import BaseModel, EmailStr, validator
from typing import Optional
from datetime import datetime

class UserBase(BaseModel):
    email: EmailStr
    full_name: Optional[str] = None
    is_active: bool = True

class UserCreate(UserBase):
    password: str
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters')
        return v

class UserUpdate(UserBase):
    password: Optional[str] = None
    
    @validator('password')
    def validate_password(cls, v):
        if v is not None and len(v) < 8:
            raise ValueError('Password must be at least 8 characters')
        return v

class UserInDBBase(UserBase):
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class User(UserInDBBase):
    pass

class UserInDB(UserInDBBase):
    hashed_password: str
```

## 错误处理

### 自定义异常处理

```python
# core/exceptions.py
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

class CustomHTTPException(HTTPException):
    def __init__(self, status_code: int, detail: str, error_code: str = None):
        super().__init__(status_code=status_code, detail=detail)
        self.error_code = error_code

async def custom_http_exception_handler(request: Request, exc: CustomHTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": {
                "code": exc.error_code,
                "message": exc.detail,
                "timestamp": "2024-01-01T00:00:00Z"
            }
        }
    )

async def validation_exception_handler(request: Request, exc: RequestValidationError):
    return JSONResponse(
        status_code=422,
        content={
            "error": {
                "code": "VALIDATION_ERROR",
                "message": "Validation failed",
                "details": exc.errors()
            }
        }
    )

# 在 main.py 中注册
app.add_exception_handler(CustomHTTPException, custom_http_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
```

## 中间件

### 请求日志中间件

```python
# core/middleware.py
import time
import logging
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)

class LoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        
        # 记录请求信息
        logger.info(
            f"Request: {request.method} {request.url.path}",
            extra={
                "method": request.method,
                "url": str(request.url),
                "client_ip": request.client.host
            }
        )
        
        response = await call_next(request)
        
        # 记录响应信息
        process_time = time.time() - start_time
        logger.info(
            f"Response: {response.status_code} - {process_time:.4f}s",
            extra={
                "status_code": response.status_code,
                "process_time": process_time
            }
        )
        
        response.headers["X-Process-Time"] = str(process_time)
        return response

# 在 main.py 中添加
app.add_middleware(LoggingMiddleware)
```

## 测试

### 单元测试

```python
# tests/test_users.py
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from main import app
from core.database import get_db, Base
from core.config import settings

# 测试数据库
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture
def client():
    Base.metadata.create_all(bind=engine)
    with TestClient(app) as c:
        yield c
    Base.metadata.drop_all(bind=engine)

def test_create_user(client):
    response = client.post(
        "/api/v1/users/",
        json={
            "email": "<EMAIL>",
            "full_name": "Test User",
            "password": "testpassword123"
        }
    )
    assert response.status_code == 201
    data = response.json()
    assert data["email"] == "<EMAIL>"
    assert "id" in data

def test_read_users(client):
    response = client.get("/api/v1/users/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
```

## 性能优化

### 数据库查询优化

```python
# services/user_service.py
from sqlalchemy.orm import Session, joinedload
from models.user import User
from schemas.user import UserCreate, UserUpdate

class UserService:
    def __init__(self, db: Session):
        self.db = db
    
    def get_users_with_posts(self, skip: int = 0, limit: int = 100):
        """获取用户及其文章（避免 N+1 查询）"""
        return (
            self.db.query(User)
            .options(joinedload(User.posts))
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_email(self, email: str):
        """通过邮箱查询用户（使用索引）"""
        return self.db.query(User).filter(User.email == email).first()
```

### 缓存

```python
# core/cache.py
import redis
import json
from typing import Optional, Any
from core.config import settings

redis_client = redis.from_url(settings.REDIS_URL) if settings.REDIS_URL else None

def cache_key(prefix: str, *args) -> str:
    """生成缓存键"""
    return f"{prefix}:{':'.join(map(str, args))}"

def get_cache(key: str) -> Optional[Any]:
    """获取缓存"""
    if not redis_client:
        return None
    
    try:
        data = redis_client.get(key)
        return json.loads(data) if data else None
    except Exception:
        return None

def set_cache(key: str, value: Any, expire: int = 3600):
    """设置缓存"""
    if not redis_client:
        return
    
    try:
        redis_client.setex(key, expire, json.dumps(value))
    except Exception:
        pass

# 使用装饰器
from functools import wraps

def cached(prefix: str, expire: int = 3600):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            key = cache_key(prefix, *args, *kwargs.values())
            
            # 尝试从缓存获取
            cached_result = get_cache(key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            set_cache(key, result, expire)
            return result
        
        return wrapper
    return decorator
```

这个指南涵盖了 FastAPI 开发的核心最佳实践，包括项目结构、配置管理、路由设计、数据验证、错误处理、测试和性能优化。
