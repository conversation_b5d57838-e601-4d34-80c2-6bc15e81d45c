"""
Configuration settings for the Artifacts Chat application
"""
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings
from pydantic import validator
from typing import List, Optional
import os

class LLMSettings(BaseSettings):
    """LLM configuration settings - OpenAI compatible only"""
    # LLM API settings (OpenAI compatible)
    LLM_API_KEY: str = os.getenv("LLM_API_KEY", "")
    LLM_API_URL: str = os.getenv("LLM_API_URL", "https://api.openai.com/v1")
    LLM_MODEL: str = os.getenv("LLM_MODEL", "gpt-3.5-turbo")

    # LLM request settings
    LLM_TEMPERATURE: float = float(os.getenv("LLM_TEMPERATURE", "0.7"))
    LLM_MAX_TOKENS: Optional[int] = int(os.getenv("LLM_MAX_TOKENS", "4096")) if os.getenv("LLM_MAX_TOKENS") else None
    LLM_TIMEOUT: int = int(os.getenv("LLM_TIMEOUT", "60"))

    @validator("LLM_API_URL")
    def validate_api_url(cls, v):
        if not v.startswith(("http://", "https://")):
            raise ValueError("LLM_API_URL must start with http:// or https://")
        return v.rstrip("/")

    @validator("LLM_API_KEY")
    def validate_api_key(cls, v):
        # Allow empty API key for testing/development
        return v or ""

    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"  # Ignore extra fields in .env

class Settings(BaseSettings):
    """Application settings"""
    # API settings
    API_PREFIX: str = "/api"

    # CORS settings
    ALLOWED_ORIGINS: List[str] = ["http://localhost:5173"]  # Vue dev server default port

    # Database settings
    DATABASE_URL: str = "sqlite:///./artifacts_chat.db"

    # Security settings
    SECRET_KEY: str = os.getenv("SECRET_KEY", "supersecretkey")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7 days

    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"  # Ignore extra fields in .env

# Create settings instances
settings = Settings()
llm_settings = LLMSettings()
