#!/usr/bin/env node

/**
 * 前端构建测试脚本
 * 用于验证前端代码是否可以正常构建
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🔧 开始测试前端构建...');

// 运行类型检查
console.log('📝 运行TypeScript类型检查...');
const typeCheck = spawn('npm', ['run', 'type-check'], {
  cwd: __dirname,
  stdio: 'inherit'
});

typeCheck.on('close', (code) => {
  if (code === 0) {
    console.log('✅ TypeScript类型检查通过');
    
    // 运行构建
    console.log('🏗️ 运行构建...');
    const build = spawn('npm', ['run', 'build-only'], {
      cwd: __dirname,
      stdio: 'inherit'
    });
    
    build.on('close', (buildCode) => {
      if (buildCode === 0) {
        console.log('✅ 构建成功完成！');
        console.log('🎉 前端代码没有问题，可以正常运行');
      } else {
        console.log('❌ 构建失败');
        process.exit(1);
      }
    });
    
  } else {
    console.log('❌ TypeScript类型检查失败');
    process.exit(1);
  }
});
