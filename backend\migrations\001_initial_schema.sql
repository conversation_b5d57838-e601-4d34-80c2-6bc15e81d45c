-- Initial database schema for Artifacts Chat
-- Created: 2025-07-15

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Chat sessions table
CREATE TABLE IF NOT EXISTS chat_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    title VARCHAR(200) DEFAULT 'New Chat',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- Messages table
CREATE TABLE IF NOT EXISTS messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id INTEGER NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant')),
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES chat_sessions (id) ON DELETE CASCADE
);

-- Artifacts table
CREATE TABLE IF NOT EXISTS artifacts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    message_id INTEGER NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('html', 'vue', 'g2plot', 'mermaid', 'svg', 'javascript')),
    code TEXT NOT NULL,
    title VARCHAR(200) DEFAULT 'Artifact',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (message_id) REFERENCES messages (id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_chat_sessions_user_id ON chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_messages_session_id ON messages(session_id);
CREATE INDEX IF NOT EXISTS idx_artifacts_message_id ON artifacts(message_id);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_updated_at ON chat_sessions(updated_at DESC);

-- Insert sample data for development
INSERT OR IGNORE INTO users (id, username, email, hashed_password) VALUES 
(1, 'demo_user', '<EMAIL>', 'hashed_password_placeholder');

INSERT OR IGNORE INTO chat_sessions (id, user_id, title) VALUES 
(1, 1, '欢迎使用 Artifacts Chat');

INSERT OR IGNORE INTO messages (session_id, role, content) VALUES 
(1, 'assistant', '欢迎使用 Artifacts Chat！我可以帮您生成各种类型的代码，包括 HTML、Vue 组件、G2Plot 图表等，并在右侧面板中实时预览。

请尝试问我一些问题，比如：
- 创建一个简单的 HTML 页面
- 生成一个 Vue 组件
- 制作一个数据可视化图表

让我们开始吧！');
