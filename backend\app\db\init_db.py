"""
Database initialization script
"""
from sqlalchemy import create_engine
from app.db.database import Base
from app.models.user import User
from app.models.chat import ChatSession, Message, Artifact
from app.core.config import settings

def create_tables():
    """Create all database tables"""
    engine = create_engine(settings.DATABASE_URL, connect_args={"check_same_thread": False})
    Base.metadata.create_all(bind=engine)
    print("Database tables created successfully!")

if __name__ == "__main__":
    create_tables()
