"""
Database service for CRUD operations
"""
from sqlalchemy.orm import Session
from sqlalchemy import desc
from typing import List, Optional
from app.models.user import User
from app.models.chat import ChatSession, Message, Artifact
from app.schemas.chat import MessageCreate
from app.schemas.session import Session<PERSON>reate
from app.schemas.artifact import ArtifactCreate

class DatabaseService:
    def __init__(self, db: Session):
        self.db = db

    # User operations
    def get_user(self, user_id: int) -> Optional[User]:
        return self.db.query(User).filter(User.id == user_id).first()

    def get_user_by_email(self, email: str) -> Optional[User]:
        return self.db.query(User).filter(User.email == email).first()

    def create_user(self, username: str, email: str, hashed_password: str) -> User:
        user = User(username=username, email=email, hashed_password=hashed_password)
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        return user

    # Chat session operations
    def get_sessions(self, user_id: Optional[int] = None) -> List[ChatSession]:
        query = self.db.query(ChatSession)
        if user_id:
            query = query.filter(ChatSession.user_id == user_id)
        return query.order_by(desc(ChatSession.updated_at)).all()

    def get_session(self, session_id: int) -> Optional[ChatSession]:
        return self.db.query(ChatSession).filter(ChatSession.id == session_id).first()

    def create_session(self, session_data: SessionCreate) -> ChatSession:
        session = ChatSession(
            user_id=session_data.user_id,
            title=session_data.title
        )
        self.db.add(session)
        self.db.commit()
        self.db.refresh(session)
        return session

    def update_session_title(self, session_id: int, title: str) -> Optional[ChatSession]:
        session = self.get_session(session_id)
        if session:
            session.title = title
            self.db.commit()
            self.db.refresh(session)
        return session

    def delete_session(self, session_id: int) -> bool:
        session = self.get_session(session_id)
        if session:
            self.db.delete(session)
            self.db.commit()
            return True
        return False

    # Message operations
    def get_messages(self, session_id: int) -> List[Message]:
        return self.db.query(Message).filter(
            Message.session_id == session_id
        ).order_by(Message.created_at).all()

    def create_message(self, message_data: MessageCreate) -> Message:
        message = Message(
            session_id=message_data.session_id,
            role=message_data.role,
            content=message_data.content
        )
        self.db.add(message)
        self.db.commit()
        self.db.refresh(message)
        return message

    def get_message(self, message_id: int) -> Optional[Message]:
        return self.db.query(Message).filter(Message.id == message_id).first()

    # Artifact operations
    def get_artifacts(self, message_id: Optional[int] = None) -> List[Artifact]:
        query = self.db.query(Artifact)
        if message_id:
            query = query.filter(Artifact.message_id == message_id)
        return query.order_by(desc(Artifact.created_at)).all()

    def get_artifact(self, artifact_id: int) -> Optional[Artifact]:
        return self.db.query(Artifact).filter(Artifact.id == artifact_id).first()

    def create_artifact(self, artifact_data: ArtifactCreate) -> Artifact:
        artifact = Artifact(
            message_id=artifact_data.message_id,
            type=artifact_data.type,
            code=artifact_data.code,
            title=artifact_data.title
        )
        self.db.add(artifact)
        self.db.commit()
        self.db.refresh(artifact)
        return artifact

    def update_artifact(self, artifact_id: int, **kwargs) -> Optional[Artifact]:
        artifact = self.get_artifact(artifact_id)
        if artifact:
            for key, value in kwargs.items():
                if hasattr(artifact, key):
                    setattr(artifact, key, value)
            self.db.commit()
            self.db.refresh(artifact)
        return artifact

    def delete_artifact(self, artifact_id: int) -> bool:
        artifact = self.get_artifact(artifact_id)
        if artifact:
            self.db.delete(artifact)
            self.db.commit()
            return True
        return False

    # Utility methods
    def get_session_with_messages(self, session_id: int) -> Optional[ChatSession]:
        return self.db.query(ChatSession).filter(
            ChatSession.id == session_id
        ).first()

    def get_recent_artifacts(self, limit: int = 10) -> List[Artifact]:
        return self.db.query(Artifact).order_by(
            desc(Artifact.created_at)
        ).limit(limit).all()

    def search_sessions(self, query: str, user_id: Optional[int] = None) -> List[ChatSession]:
        db_query = self.db.query(ChatSession).filter(
            ChatSession.title.contains(query)
        )
        if user_id:
            db_query = db_query.filter(ChatSession.user_id == user_id)
        return db_query.order_by(desc(ChatSession.updated_at)).all()

def get_database_service(db: Session) -> DatabaseService:
    """Dependency to get database service"""
    return DatabaseService(db)
