<template>
  <div class="artifact-panel">
    <!-- 面板头部 -->
    <div class="panel-header">
      <div class="panel-title">
        <h3>{{ currentArtifact?.title || 'Artifact' }}</h3>
        <a-tag :color="getTypeColor(currentArtifact?.type)">
          {{ currentArtifact?.type?.toUpperCase() }}
        </a-tag>
      </div>
      <div class="panel-actions">
        <a-button type="text" @click="closePanel">
          <template #icon>
            <icon-close />
          </template>
        </a-button>
      </div>
    </div>

    <!-- 标签页 -->
    <div class="panel-content">
      <a-tabs v-model:active-key="activeTab" class="artifact-tabs">
        <!-- 预览标签页 -->
        <a-tab-pane key="preview" title="预览">
          <div class="preview-container">
            <iframe
              ref="rendererRef"
              src="/renderer.html"
              class="artifact-renderer"
              sandbox="allow-scripts allow-same-origin"
              @load="onRendererLoad"
            ></iframe>
            <div v-if="renderError" class="render-error">
              <a-alert
                type="error"
                :title="renderError"
                show-icon
              />
            </div>
          </div>
        </a-tab-pane>

        <!-- 代码标签页 -->
        <a-tab-pane key="code" title="代码">
          <div class="code-container">
            <div class="code-header">
              <span class="code-language">{{ currentArtifact?.type }}</span>
              <a-button type="text" size="mini" @click="copyCode">
                <template #icon>
                  <icon-copy />
                </template>
                复制代码
              </a-button>
            </div>
            <div class="code-editor">
              <pre class="code-content"><code :class="`language-${currentArtifact?.type}`" v-html="highlightedCode"></code></pre>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- Artifact 历史 -->
    <div v-if="artifactHistory.length > 1" class="artifact-history">
      <div class="history-header">
        <span>历史版本</span>
      </div>
      <div class="history-list">
        <div
          v-for="artifact in artifactHistory"
          :key="artifact.id"
          class="history-item"
          :class="{ 'active': artifact.id === currentArtifact?.id }"
          @click="switchToArtifact(artifact.id)"
        >
          <div class="history-title">{{ artifact.title }}</div>
          <div class="history-type">{{ artifact.type }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { IconClose, IconCopy } from '@arco-design/web-vue/es/icon'
import { Message } from '@arco-design/web-vue'
import hljs from 'highlight.js'
import { useLayoutStore } from '@/stores/layout'
import { ArtifactSandbox } from '@/utils/security'
import { performanceMonitor } from '@/utils/performance'

const layoutStore = useLayoutStore()

// 状态
const activeTab = ref('preview')
const rendererRef = ref<HTMLIFrameElement>()
const renderError = ref('')
const rendererReady = ref(false)

// 计算属性
const currentArtifact = computed(() => layoutStore.currentArtifact)
const artifactHistory = computed(() => layoutStore.artifactHistory)

const highlightedCode = computed(() => {
  if (!currentArtifact.value) return ''
  
  const language = currentArtifact.value.type
  if (hljs.getLanguage(language)) {
    return hljs.highlight(currentArtifact.value.code, { language }).value
  }
  return hljs.highlightAuto(currentArtifact.value.code).value
})

// 方法
const closePanel = () => {
  layoutStore.hideArtifact()
}

const copyCode = async () => {
  if (!currentArtifact.value) return
  
  try {
    await navigator.clipboard.writeText(currentArtifact.value.code)
    Message.success('代码已复制到剪贴板')
  } catch (error) {
    Message.error('复制失败')
  }
}

const switchToArtifact = (artifactId: string) => {
  layoutStore.switchToArtifact(artifactId)
}

const getTypeColor = (type?: string) => {
  const colors: Record<string, string> = {
    html: 'orange',
    vue: 'green',
    g2plot: 'blue',
    mermaid: 'purple',
    svg: 'cyan',
    javascript: 'gold'
  }
  return colors[type || ''] || 'gray'
}

// 渲染器相关方法
const onRendererLoad = () => {
  rendererReady.value = true
  renderArtifact()
}

const renderArtifact = () => {
  if (!rendererRef.value || !currentArtifact.value || !rendererReady.value) return

  // 性能监控
  performanceMonitor.startTiming(`artifact_render_${currentArtifact.value.type}`)

  // 安全验证
  const validation = ArtifactSandbox.validateArtifactCode(
    currentArtifact.value.code,
    currentArtifact.value.type
  )

  if (!validation.safe) {
    renderError.value = `安全检查失败: ${validation.issues.join(', ')}`
    performanceMonitor.endTiming(`artifact_render_${currentArtifact.value.type}`)
    return
  }

  const message = {
    type: 'render',
    artifactType: currentArtifact.value.type,
    code: validation.sanitizedCode || currentArtifact.value.code,
    id: currentArtifact.value.id
  }

  rendererRef.value.contentWindow?.postMessage(message, '*')
}

// 监听来自渲染器的消息
const handleRendererMessage = (event: MessageEvent) => {
  if (event.origin !== window.location.origin) return

  const { type, success, errorMessage, artifactId } = event.data

  if (type === 'rendererReady') {
    rendererReady.value = true
    renderArtifact()
  } else if (type === 'renderStatus') {
    if (!success && errorMessage) {
      renderError.value = errorMessage
      console.error('Artifact 渲染失败:', errorMessage)
    } else {
      renderError.value = ''
      console.log('Artifact 渲染成功:', artifactId)
    }
  }
}

// 监听 artifact 变化
watch(currentArtifact, () => {
  renderError.value = ''
  nextTick(() => {
    renderArtifact()
  })
}, { immediate: true })

// 生命周期
window.addEventListener('message', handleRendererMessage)
</script>

<style scoped>
.artifact-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e6eb;
  background: #fafafa;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.panel-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1d2129;
}

.panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.artifact-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.artifact-tabs :deep(.arco-tabs-content) {
  flex: 1;
  overflow: hidden;
}

.artifact-tabs :deep(.arco-tabs-pane) {
  height: 100%;
  overflow: hidden;
}

.preview-container {
  height: 100%;
  position: relative;
}

.artifact-renderer {
  width: 100%;
  height: 100%;
  border: none;
  background: #fff;
}

.render-error {
  position: absolute;
  top: 16px;
  left: 16px;
  right: 16px;
  z-index: 10;
}

.code-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.code-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f7f8fa;
  border-bottom: 1px solid #e5e6eb;
}

.code-language {
  font-size: 12px;
  font-weight: 500;
  color: #4e5969;
  text-transform: uppercase;
}

.code-editor {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.code-content {
  flex: 1;
  margin: 0;
  padding: 16px;
  background: #f7f8fa;
  overflow: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.artifact-history {
  border-top: 1px solid #e5e6eb;
  background: #fafafa;
}

.history-header {
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #4e5969;
  border-bottom: 1px solid #e5e6eb;
}

.history-list {
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f2f3f5;
}

.history-item:hover {
  background: #f2f3f5;
}

.history-item.active {
  background: #e8f3ff;
}

.history-title {
  font-size: 13px;
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 2px;
}

.history-type {
  font-size: 11px;
  color: #86909c;
  text-transform: uppercase;
}
</style>
