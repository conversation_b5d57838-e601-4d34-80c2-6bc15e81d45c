"""
Intelligent Prompt Builder for Context Engine

Builds optimized prompts with intelligent context injection.
"""
import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json

from app.services.context_engine.retrieval_engine import SearchResult
import logging

logger = logging.getLogger(__name__)

class QueryType(str, Enum):
    """Types of user queries"""
    DEBUGGING = "debugging"
    LEARNING = "learning"
    IMPLEMENTATION = "implementation"
    BEST_PRACTICES = "best_practices"
    COMPARISON = "comparison"
    GENERAL = "general"

class ContextType(str, Enum):
    """Types of context content"""
    CODE_EXAMPLE = "code_example"
    DOCUMENTATION = "documentation"
    TUTORIAL = "tutorial"
    API_REFERENCE = "api_reference"
    TROUBLESHOOTING = "troubleshooting"

@dataclass
class PromptTemplate:
    """Prompt template structure"""
    name: str
    query_types: List[QueryType]
    system_prompt: str
    context_format: str
    max_context_length: int = 8000
    priority_weights: Dict[str, float] = None

class QueryAnalyzer:
    """Analyze user queries to determine type and intent"""
    
    def __init__(self):
        # Query type patterns
        self.patterns = {
            QueryType.DEBUGGING: [
                r'\b(error|bug|issue|problem|fix|debug|troubleshoot|not working|broken)\b',
                r'\b(exception|traceback|stack trace|crash|fail)\b',
                r'\b(why.*not|what.*wrong|how.*fix)\b'
            ],
            QueryType.LEARNING: [
                r'\b(learn|understand|explain|what is|how does|tutorial)\b',
                r'\b(beginner|start|introduction|basics|fundamentals)\b',
                r'\b(teach me|show me|guide|walkthrough)\b'
            ],
            QueryType.IMPLEMENTATION: [
                r'\b(how to|implement|create|build|make|develop)\b',
                r'\b(code|example|sample|demo|snippet)\b',
                r'\b(step by step|instructions|procedure)\b'
            ],
            QueryType.BEST_PRACTICES: [
                r'\b(best practice|recommended|should|convention|standard)\b',
                r'\b(optimize|performance|efficient|clean code)\b',
                r'\b(pattern|architecture|design|structure)\b'
            ],
            QueryType.COMPARISON: [
                r'\b(vs|versus|compare|difference|better|alternative)\b',
                r'\b(which|choose|select|decide|option)\b',
                r'\b(pros and cons|advantages|disadvantages)\b'
            ]
        }
    
    def analyze_query(self, query: str) -> Tuple[QueryType, float]:
        """Analyze query and return type with confidence score"""
        query_lower = query.lower()
        scores = {}
        
        for query_type, patterns in self.patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, query_lower))
                score += matches
            
            if score > 0:
                scores[query_type] = score / len(patterns)
        
        if not scores:
            return QueryType.GENERAL, 0.5
        
        # Return type with highest score
        best_type = max(scores.items(), key=lambda x: x[1])
        return best_type[0], min(best_type[1], 1.0)

class ContextCompressor:
    """Compress context to fit within token limits"""
    
    def __init__(self, max_tokens: int = 8000):
        self.max_tokens = max_tokens
        # Rough estimation: 1 token ≈ 4 characters
        self.chars_per_token = 4
    
    def compress_contexts(
        self, 
        contexts: List[SearchResult], 
        query_type: QueryType
    ) -> List[SearchResult]:
        """Compress contexts based on query type and token limits"""
        if not contexts:
            return contexts
        
        # Calculate current size
        total_chars = sum(len(ctx.content) for ctx in contexts)
        max_chars = self.max_tokens * self.chars_per_token
        
        if total_chars <= max_chars:
            return contexts
        
        # Prioritize contexts based on query type
        prioritized_contexts = self._prioritize_contexts(contexts, query_type)
        
        # Select contexts within limit
        selected_contexts = []
        current_chars = 0
        
        for context in prioritized_contexts:
            context_chars = len(context.content)
            
            if current_chars + context_chars <= max_chars:
                selected_contexts.append(context)
                current_chars += context_chars
            else:
                # Try to include a truncated version
                remaining_chars = max_chars - current_chars
                if remaining_chars > 200:  # Minimum useful size
                    truncated_context = self._truncate_context(context, remaining_chars)
                    selected_contexts.append(truncated_context)
                break
        
        logger.info(
            "Compressed contexts",
            original_count=len(contexts),
            selected_count=len(selected_contexts),
            original_chars=total_chars,
            compressed_chars=current_chars
        )
        
        return selected_contexts
    
    def _prioritize_contexts(
        self, 
        contexts: List[SearchResult], 
        query_type: QueryType
    ) -> List[SearchResult]:
        """Prioritize contexts based on query type"""
        
        # Define priority weights for different query types
        priority_weights = {
            QueryType.DEBUGGING: {
                'troubleshooting': 1.0,
                'code_example': 0.9,
                'documentation': 0.7,
                'tutorial': 0.5
            },
            QueryType.LEARNING: {
                'tutorial': 1.0,
                'documentation': 0.9,
                'code_example': 0.8,
                'troubleshooting': 0.3
            },
            QueryType.IMPLEMENTATION: {
                'code_example': 1.0,
                'tutorial': 0.8,
                'documentation': 0.7,
                'troubleshooting': 0.4
            },
            QueryType.BEST_PRACTICES: {
                'documentation': 1.0,
                'tutorial': 0.8,
                'code_example': 0.7,
                'troubleshooting': 0.3
            },
            QueryType.COMPARISON: {
                'documentation': 1.0,
                'tutorial': 0.7,
                'code_example': 0.6,
                'troubleshooting': 0.2
            },
            QueryType.GENERAL: {
                'documentation': 0.8,
                'code_example': 0.8,
                'tutorial': 0.7,
                'troubleshooting': 0.5
            }
        }
        
        weights = priority_weights.get(query_type, priority_weights[QueryType.GENERAL])
        
        # Calculate priority scores
        for context in contexts:
            base_score = context.score
            
            # Determine context type from metadata
            context_type = self._determine_context_type(context)
            type_weight = weights.get(context_type, 0.5)
            
            # Adjust score based on type priority
            context.score = base_score * type_weight
        
        # Sort by adjusted score
        return sorted(contexts, key=lambda x: x.score, reverse=True)
    
    def _determine_context_type(self, context: SearchResult) -> str:
        """Determine context type from metadata"""
        metadata = context.metadata
        
        # Check explicit category
        category = metadata.get('category', '').lower()
        if 'troubleshoot' in category or 'debug' in category:
            return 'troubleshooting'
        elif 'tutorial' in category or 'guide' in category:
            return 'tutorial'
        elif 'api' in category or 'reference' in category:
            return 'documentation'
        elif context.source_type == 'code':
            return 'code_example'
        else:
            return 'documentation'
    
    def _truncate_context(self, context: SearchResult, max_chars: int) -> SearchResult:
        """Truncate context content to fit within character limit"""
        if len(context.content) <= max_chars:
            return context
        
        # Try to truncate at sentence boundaries
        sentences = context.content.split('. ')
        truncated_content = ""
        
        for sentence in sentences:
            if len(truncated_content) + len(sentence) + 2 <= max_chars - 20:  # Leave room for "..."
                truncated_content += sentence + ". "
            else:
                break
        
        if not truncated_content:
            # Fallback to character truncation
            truncated_content = context.content[:max_chars-3]
        
        truncated_content += "..."
        
        # Create new context with truncated content
        truncated_context = SearchResult(
            id=context.id,
            content=truncated_content,
            title=context.title,
            score=context.score * 0.8,  # Reduce score for truncated content
            source_type=context.source_type,
            metadata={**context.metadata, 'truncated': True}
        )
        
        return truncated_context

class IntelligentPromptBuilder:
    """Build intelligent prompts with optimized context injection"""
    
    def __init__(self):
        self.query_analyzer = QueryAnalyzer()
        self.context_compressor = ContextCompressor()
        
        # Load prompt templates
        self.templates = self._load_templates()
        
        logger.info("Initialized IntelligentPromptBuilder")
    
    def _load_templates(self) -> Dict[QueryType, PromptTemplate]:
        """Load prompt templates for different query types"""
        return {
            QueryType.DEBUGGING: PromptTemplate(
                name="debugging",
                query_types=[QueryType.DEBUGGING],
                system_prompt="""你是一个专业的技术问题解决专家。用户遇到了技术问题，需要你的帮助来调试和解决。

请基于提供的技术文档和代码示例，帮助用户：
1. 分析问题的可能原因
2. 提供具体的解决方案
3. 给出可执行的代码示例
4. 解释解决方案的原理

回答要求：
- 直接针对问题，提供实用的解决方案
- 优先使用提供的代码示例和文档
- 如果有多种解决方案，请说明各自的优缺点
- 提供完整、可运行的代码示例""",
                context_format="## 🔧 相关技术资料\n\n{contexts}\n\n---\n\n",
                max_context_length=6000
            ),
            
            QueryType.LEARNING: PromptTemplate(
                name="learning",
                query_types=[QueryType.LEARNING],
                system_prompt="""你是一个耐心的技术导师。用户想要学习新的技术概念，需要你提供清晰、易懂的解释。

请基于提供的技术文档和示例，帮助用户：
1. 理解核心概念和原理
2. 提供循序渐进的学习路径
3. 给出实践性的代码示例
4. 解释最佳实践和注意事项

回答要求：
- 从基础概念开始，循序渐进
- 使用简单易懂的语言解释复杂概念
- 提供丰富的代码示例和实践练习
- 突出重点和关键知识点""",
                context_format="## 📚 学习资料\n\n{contexts}\n\n---\n\n",
                max_context_length=8000
            ),
            
            QueryType.IMPLEMENTATION: PromptTemplate(
                name="implementation",
                query_types=[QueryType.IMPLEMENTATION],
                system_prompt="""你是一个实战经验丰富的开发专家。用户需要实现特定的功能，需要你提供具体的实现方案。

请基于提供的技术文档和代码示例，帮助用户：
1. 设计实现方案和架构
2. 提供完整的代码实现
3. 解释关键实现细节
4. 提供测试和验证方法

回答要求：
- 提供完整、可运行的代码实现
- 详细解释每个步骤的作用
- 考虑边界情况和错误处理
- 提供代码注释和使用说明""",
                context_format="## 💻 实现参考\n\n{contexts}\n\n---\n\n",
                max_context_length=7000
            ),
            
            QueryType.BEST_PRACTICES: PromptTemplate(
                name="best_practices",
                query_types=[QueryType.BEST_PRACTICES],
                system_prompt="""你是一个资深的技术架构师。用户想了解最佳实践和规范，需要你提供专业的建议。

请基于提供的技术文档和示例，帮助用户：
1. 了解行业标准和最佳实践
2. 理解设计原则和架构模式
3. 避免常见的陷阱和反模式
4. 提供可维护和可扩展的解决方案

回答要求：
- 基于行业标准和最佳实践
- 解释设计原则背后的原因
- 提供正确和错误的对比示例
- 考虑长期维护和团队协作""",
                context_format="## 🏆 最佳实践指南\n\n{contexts}\n\n---\n\n",
                max_context_length=8000
            ),
            
            QueryType.COMPARISON: PromptTemplate(
                name="comparison",
                query_types=[QueryType.COMPARISON],
                system_prompt="""你是一个技术选型专家。用户需要在不同的技术方案中做出选择，需要你提供客观的对比分析。

请基于提供的技术文档和示例，帮助用户：
1. 对比不同方案的优缺点
2. 分析适用场景和限制条件
3. 提供选择建议和决策依据
4. 考虑性能、维护性、学习成本等因素

回答要求：
- 客观公正地对比各种方案
- 详细分析优缺点和适用场景
- 提供具体的选择建议
- 考虑实际项目需求和约束""",
                context_format="## ⚖️ 技术对比资料\n\n{contexts}\n\n---\n\n",
                max_context_length=8000
            ),
            
            QueryType.GENERAL: PromptTemplate(
                name="general",
                query_types=[QueryType.GENERAL],
                system_prompt="""你是一个全栈技术专家，具有丰富的开发经验。请基于提供的技术文档和代码示例，为用户提供准确、实用的技术建议。

回答要求：
- 基于提供的参考资料回答问题
- 提供具体的代码示例和实现方案
- 解释技术原理和最佳实践
- 确保答案准确、完整、易于理解""",
                context_format="## 📖 相关技术资料\n\n{contexts}\n\n---\n\n",
                max_context_length=7000
            )
        }
    
    async def build_prompt(
        self,
        user_query: str,
        contexts: List[SearchResult],
        conversation_history: Optional[List[Dict[str, str]]] = None
    ) -> Tuple[str, Dict[str, Any]]:
        """Build optimized prompt with intelligent context injection"""
        try:
            # Analyze query type
            query_type, confidence = self.query_analyzer.analyze_query(user_query)
            
            # Get appropriate template
            template = self.templates.get(query_type, self.templates[QueryType.GENERAL])
            
            # Compress contexts
            compressed_contexts = self.context_compressor.compress_contexts(contexts, query_type)
            
            # Format contexts
            context_content = self._format_contexts(compressed_contexts, template)
            
            # Build system prompt
            system_prompt = template.system_prompt
            if context_content:
                system_prompt += "\n\n" + template.context_format.format(contexts=context_content)
            
            # Add conversation context if available
            if conversation_history:
                conversation_context = self._format_conversation_history(conversation_history)
                if conversation_context:
                    system_prompt += f"\n\n## 💬 对话历史\n\n{conversation_context}\n\n---\n\n"
            
            # Build metadata
            metadata = {
                "query_type": query_type.value,
                "confidence": confidence,
                "template_used": template.name,
                "contexts_count": len(compressed_contexts),
                "original_contexts_count": len(contexts),
                "prompt_length": len(system_prompt)
            }
            
            logger.info(
                "Built intelligent prompt",
                query_type=query_type.value,
                confidence=confidence,
                contexts_used=len(compressed_contexts),
                prompt_length=len(system_prompt)
            )
            
            return system_prompt, metadata
            
        except Exception as e:
            logger.error("Failed to build prompt", error=str(e))
            # Fallback to simple prompt
            fallback_prompt = self._build_fallback_prompt(user_query, contexts)
            return fallback_prompt, {"error": str(e), "fallback": True}
    
    def _format_contexts(self, contexts: List[SearchResult], template: PromptTemplate) -> str:
        """Format contexts according to template"""
        if not contexts:
            return ""
        
        formatted_contexts = []
        
        for i, context in enumerate(contexts, 1):
            context_text = f"### 📄 资料 {i}: {context.title}\n"
            context_text += f"**类型**: {context.source_type} | **相关度**: {context.score:.2f}\n"
            
            # Add metadata
            metadata = context.metadata
            if metadata.get('framework'):
                context_text += f"**框架**: {metadata['framework']} | "
            if metadata.get('language'):
                context_text += f"**语言**: {metadata['language']} | "
            if metadata.get('category'):
                context_text += f"**分类**: {metadata['category']}"
            
            context_text += "\n\n"
            
            # Add content
            if context.source_type == 'code':
                language = metadata.get('language', '')
                context_text += f"```{language}\n{context.content}\n```"
            else:
                context_text += context.content
            
            context_text += "\n\n---\n"
            formatted_contexts.append(context_text)
        
        return "\n".join(formatted_contexts)
    
    def _format_conversation_history(self, history: List[Dict[str, str]]) -> str:
        """Format conversation history"""
        if not history or len(history) < 2:
            return ""
        
        # Take last few exchanges
        recent_history = history[-6:]  # Last 3 exchanges
        
        formatted_history = []
        for msg in recent_history:
            role = msg.get('role', 'unknown')
            content = msg.get('content', '')
            
            if role == 'user':
                formatted_history.append(f"**用户**: {content}")
            elif role == 'assistant':
                # Truncate long assistant responses
                if len(content) > 200:
                    content = content[:200] + "..."
                formatted_history.append(f"**助手**: {content}")
        
        return "\n\n".join(formatted_history)
    
    def _build_fallback_prompt(self, user_query: str, contexts: List[SearchResult]) -> str:
        """Build fallback prompt when main builder fails"""
        prompt_parts = [
            "你是一个技术专家，请基于以下资料回答用户问题：\n"
        ]
        
        if contexts:
            prompt_parts.append("## 参考资料\n")
            for i, context in enumerate(contexts[:3], 1):  # Limit to 3 contexts
                prompt_parts.append(f"### 资料 {i}: {context.title}\n")
                prompt_parts.append(f"{context.content[:500]}...\n")  # Truncate content
        
        prompt_parts.append("\n请基于以上资料提供准确、实用的回答。")
        
        return "\n".join(prompt_parts)
