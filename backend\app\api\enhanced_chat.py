"""
Enhanced Chat API endpoints with Intelligent Context
"""
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import List, Dict, Any, Optional, AsyncGenerator
import json
import asyncio
import logging

from app.services.llm.simple_llm_service import get_llm_service, ChatMessage, ChatRequest
from app.core.config import llm_settings

logger = logging.getLogger(__name__)
router = APIRouter()

class EnhancedChatRequest(BaseModel):
    messages: List[ChatMessage]
    model: Optional[str] = None  # Will use configured model if not specified
    temperature: Optional[float] = None  # Will use configured temperature if not specified
    max_tokens: Optional[int] = None
    stream: bool = False
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    enable_context: bool = True

class ModelListResponse(BaseModel):
    object: str = "list"
    data: List[Dict[str, Any]]

@router.post("/completions")
async def enhanced_chat_completions(request: EnhancedChatRequest):
    """Enhanced chat completions with intelligent context"""
    try:
        llm_service = await get_llm_service()

        # Create LLM request
        llm_request = ChatRequest(
            messages=request.messages,
            model=request.model,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            stream=request.stream
        )

        # TODO: Add context enhancement here if needed
        # For now, just pass through to LLM service

        if request.stream:
            return StreamingResponse(
                llm_service._stream_completion({
                    "model": request.model or llm_settings.LLM_MODEL,
                    "messages": [{"role": msg.role, "content": msg.content} for msg in request.messages],
                    "temperature": request.temperature or llm_settings.LLM_TEMPERATURE,
                    "max_tokens": request.max_tokens or llm_settings.LLM_MAX_TOKENS,
                    "stream": True
                }),
                media_type="text/plain"
            )
        else:
            response = await llm_service.chat_completion(llm_request)
            return response

    except Exception as e:
        logger.error(f"Enhanced chat completion failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

async def stream_llm_response(response_generator: AsyncGenerator) -> AsyncGenerator[str, None]:
    """Stream LLM response in OpenAI format"""
    try:
        async for chunk in response_generator:
            yield f"data: {json.dumps(chunk)}\n\n"
        
        yield "data: [DONE]\n\n"
        
    except Exception as e:
        logger.error(f"Streaming response failed: {str(e)}")
        error_chunk = {
            "error": {
                "message": str(e),
                "type": "internal_error"
            }
        }
        yield f"data: {json.dumps(error_chunk)}\n\n"

@router.get("/models", response_model=ModelListResponse)
async def list_models():
    """List available models from configured LLM service"""
    try:
        llm_service = await get_llm_service()
        models = await llm_service.list_models()
        return ModelListResponse(data=models)
    except Exception as e:
        logger.error(f"Failed to list models: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/health")
async def health_check():
    """Health check for LLM services"""
    try:
        llm_service = await get_llm_service()
        health_status = await llm_service.health_check()

        if health_status["status"] == "unhealthy":
            raise HTTPException(status_code=503, detail=health_status)

        return health_status
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=503, detail=str(e))

@router.post("/context-search")
async def context_search(request: Dict[str, Any]):
    """Search context for a query (placeholder for future implementation)"""
    try:
        query = request.get("query", "")

        # TODO: Implement actual context search
        return {
            "query": query,
            "contexts": [],
            "total_found": 0,
            "processing_time_ms": 0,
            "suggestions": [],
            "metadata": {"implemented": False, "message": "Context search not yet implemented"}
        }

    except Exception as e:
        logger.error(f"Context search failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/optimize-prompt")
async def optimize_prompt(request: Dict[str, Any]):
    """Optimize prompt (placeholder for future implementation)"""
    try:
        query = request.get("query", "")

        # TODO: Implement actual prompt optimization with context
        return {
            "query": query,
            "optimized_prompt": f"请回答关于'{query}'的问题。",
            "metadata": {"implemented": False, "message": "Prompt optimization not yet implemented"},
            "contexts_used": 0
        }

    except Exception as e:
        logger.error(f"Prompt optimization failed: {str(e)}")
        return {
            "query": request.get("query", ""),
            "optimized_prompt": f"请回答关于'{request.get('query', '')}'的问题。",
            "metadata": {"error": str(e), "fallback": True},
            "contexts_used": 0
        }


