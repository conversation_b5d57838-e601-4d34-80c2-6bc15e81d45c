# 🧠 智能上下文引擎技术文档

## 概述

智能上下文引擎是 Artifacts Chat 的核心组件，为AI对话提供精准的技术文档和代码示例支持。通过混合检索算法、智能排序和动态提示词优化，显著提升AI回答的准确性和实用性。

## 🏗️ 系统架构

### 核心组件

```mermaid
graph TB
    A[用户查询] --> B[查询分析器]
    B --> C[智能检索引擎]
    C --> D[上下文排序器]
    D --> E[提示词构建器]
    E --> F[LLM API路由器]
    F --> G[AI回答]
    
    H[知识库管理器] --> C
    I[向量存储] --> C
    J[文档处理器] --> H
    K[嵌入服务] --> I
```

### 数据流

1. **查询输入** → 用户输入技术问题
2. **意图识别** → 分析查询类型（调试、学习、实现等）
3. **混合检索** → 向量搜索 + 关键词匹配
4. **智能排序** → 8维度质量评估和排序
5. **上下文压缩** → 适配token限制的智能压缩
6. **提示词构建** → 基于查询类型的专业模板
7. **LLM调用** → 路由到最适合的模型
8. **结果返回** → 增强的AI回答

## 🔍 智能检索引擎

### 混合检索算法

```python
# 向量搜索 (70%) + 关键词搜索 (30%)
final_score = vector_score * 0.7 + keyword_score * 0.3

# BM25 关键词评分
bm25_score = idf * (tf * (k1 + 1)) / (tf + k1 * (1 - b + b * (doc_length / avg_doc_length)))

# 向量相似度
vector_score = cosine_similarity(query_embedding, doc_embedding)
```

### 多维度排序

| 排序因子 | 权重 | 说明 |
|---------|------|------|
| 语义相似度 | 30% | 向量空间中的相似度 |
| 关键词相关性 | 20% | BM25算法评分 |
| 标题相关性 | 15% | 标题与查询的匹配度 |
| 内容质量 | 15% | 结构、示例、解释完整性 |
| 时效性 | 5% | 内容创建/更新时间 |
| 流行度 | 5% | 使用频率和评分 |
| 完整性 | 5% | 元数据和标签完整性 |
| 代码质量 | 5% | 代码规范和最佳实践 |

### 上下文压缩策略

```python
# 基于查询类型的优先级权重
priority_weights = {
    'debugging': {
        'troubleshooting': 1.0,
        'code_example': 0.9,
        'documentation': 0.7
    },
    'learning': {
        'tutorial': 1.0,
        'documentation': 0.9,
        'code_example': 0.8
    },
    'implementation': {
        'code_example': 1.0,
        'tutorial': 0.8,
        'documentation': 0.7
    }
}
```

## 🎯 智能提示词工程

### 查询类型识别

| 查询类型 | 关键词模式 | 示例 |
|---------|-----------|------|
| 调试 (debugging) | error, bug, 问题, 调试 | "Vue组件报错怎么办？" |
| 学习 (learning) | 学习, 了解, 什么是 | "什么是Vue组件？" |
| 实现 (implementation) | 如何, 创建, 实现 | "如何创建Vue组件？" |
| 最佳实践 (best_practices) | 最佳实践, 推荐 | "Vue组件最佳实践" |
| 对比 (comparison) | 对比, 区别, 选择 | "Vue vs React组件" |

### 专业化提示词模板

#### 调试模板
```
你是一个专业的技术问题解决专家。用户遇到了技术问题，需要你的帮助来调试和解决。

请基于提供的技术文档和代码示例，帮助用户：
1. 分析问题的可能原因
2. 提供具体的解决方案
3. 给出可执行的代码示例
4. 解释解决方案的原理

回答要求：
- 直接针对问题，提供实用的解决方案
- 优先使用提供的代码示例和文档
- 如果有多种解决方案，请说明各自的优缺点
- 提供完整、可运行的代码示例
```

#### 学习模板
```
你是一个耐心的技术导师。用户想要学习新的技术概念，需要你提供清晰、易懂的解释。

请基于提供的技术文档和示例，帮助用户：
1. 理解核心概念和原理
2. 提供循序渐进的学习路径
3. 给出实践性的代码示例
4. 解释最佳实践和注意事项

回答要求：
- 从基础概念开始，循序渐进
- 使用简单易懂的语言解释复杂概念
- 提供丰富的代码示例和实践练习
- 突出重点和关键知识点
```

### 动态优化规则

```python
optimization_rules = [
    {
        "condition": "avg_response_time > 5000 and context_count > 5",
        "action": "reduce_context_length",
        "priority": 1
    },
    {
        "condition": "avg_quality < 0.6 and context_count < 3", 
        "action": "increase_context_length",
        "priority": 2
    },
    {
        "condition": "query_type == 'debugging' and avg_satisfaction < 0.7",
        "action": "use_detailed_debugging_template",
        "priority": 1
    }
]
```

## 🤖 大模型集成

### 统一API路由器

```python
class UnifiedLLMRouter:
    def __init__(self):
        self.openai_adapter = OpenAIAdapter()
        self.claude_adapter = ClaudeAdapter()
        
    async def chat_completion(self, request, preferred_provider=None):
        # 智能路由逻辑
        provider = self._determine_provider(request.model, preferred_provider)
        
        try:
            if provider == LLMProvider.OPENAI:
                return await self.openai_adapter.chat_completion(request)
            elif provider == LLMProvider.CLAUDE:
                return await self.claude_adapter.chat_completion(request)
        except Exception:
            # 故障转移
            return await self._fallback_completion(request, provider)
```

### 智能上下文注入

```python
async def _enhance_with_context(self, messages, user_id, session_id):
    # 获取最新用户消息
    latest_query = messages[-1].content
    
    # 构建对话历史
    conversation_history = messages[-10:]
    
    # 请求智能上下文
    context_request = ContextRequest(
        query=latest_query,
        conversation_history=conversation_history,
        max_results=5,
        include_code=True,
        include_docs=True
    )
    
    context_response = await context_engine.get_context(context_request)
    
    # 构建增强的系统提示词
    if context_response.contexts:
        context_content = self._build_context_content(context_response)
        enhanced_messages = self._inject_context(messages, context_content)
        return enhanced_messages, {"context_used": True}
    
    return messages, {"context_used": False}
```

## 📚 知识库管理

### 支持的文件格式

| 格式 | 处理器 | 特性 |
|------|--------|------|
| PDF | PyPDF2 | 文本提取、元数据解析 |
| DOCX | python-docx | 结构化内容、样式保留 |
| Markdown | markdown-it | 语法解析、代码块识别 |
| HTML | BeautifulSoup | 标签清理、内容提取 |
| 代码文件 | Tree-sitter | 语法分析、结构提取 |

### 智能分块策略

```python
class DocumentProcessor:
    def chunk_document(self, content, doc_type):
        if doc_type == 'code':
            return self._chunk_code_by_structure(content)
        elif doc_type == 'markdown':
            return self._chunk_by_headers(content)
        else:
            return self._chunk_by_sentences(content)
    
    def _chunk_code_by_structure(self, code):
        # 使用Tree-sitter解析代码结构
        tree = self.parser.parse(code.encode())
        chunks = []
        
        for node in tree.root_node.children:
            if node.type in ['function_definition', 'class_definition']:
                chunk = self._extract_node_content(code, node)
                chunks.append(chunk)
        
        return chunks
```

### 向量存储

```python
class VectorStoreManager:
    def __init__(self):
        self.client = QdrantClient(url=settings.QDRANT_URL)
        self.embedding_service = EmbeddingService()
    
    async def add_vectors(self, documents):
        # 批量生成嵌入向量
        embeddings = await self.embedding_service.embed_batch(
            [doc.content for doc in documents]
        )
        
        # 存储到Qdrant
        points = [
            PointStruct(
                id=doc.id,
                vector=embedding,
                payload=doc.metadata
            )
            for doc, embedding in zip(documents, embeddings)
        ]
        
        await self.client.upsert(
            collection_name=self.collection_name,
            points=points
        )
```

## 📊 性能监控

### 关键指标

| 指标 | 目标值 | 监控方式 |
|------|--------|----------|
| 检索响应时间 | < 100ms | Prometheus |
| 上下文相关性 | > 0.8 | 用户反馈 |
| LLM调用成功率 | > 99% | 健康检查 |
| 知识库覆盖率 | > 90% | 查询分析 |

### 性能优化策略

1. **缓存策略**
   - 查询结果缓存 (Redis)
   - 嵌入向量缓存
   - 模板渲染缓存

2. **并发优化**
   - 异步处理
   - 连接池管理
   - 批量操作

3. **资源管理**
   - 内存使用监控
   - GPU资源调度
   - 磁盘空间管理

## 🔧 部署配置

### Docker Compose 部署

```yaml
services:
  backend:
    build: ./backend
    environment:
      - DATABASE_URL=********************************************/artifacts_chat
      - QDRANT_URL=http://qdrant:6333
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - qdrant
      - redis

  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: artifacts_chat
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
```

### 环境变量配置

```bash
# 核心配置
DATABASE_URL="postgresql://user:pass@localhost:5432/db"
QDRANT_URL="http://localhost:6333"
REDIS_URL="redis://localhost:6379/0"

# LLM API
OPENAI_API_KEY="sk-your-openai-key"
CLAUDE_API_KEY="sk-ant-your-claude-key"

# 向量模型
EMBEDDING_MODEL="all-MiniLM-L6-v2"
EMBEDDING_DIMENSION=384

# 性能参数
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_CONTEXT_LENGTH=8000
SIMILARITY_THRESHOLD=0.7
```

## 🚀 未来规划

### 短期目标 (1-3个月)
- [ ] 支持更多文件格式 (PPT, Excel等)
- [ ] 实现多语言支持
- [ ] 添加用户权限管理
- [ ] 优化移动端体验

### 中期目标 (3-6个月)
- [ ] 集成更多LLM提供商
- [ ] 实现知识图谱构建
- [ ] 添加协作功能
- [ ] 支持私有化部署

### 长期目标 (6-12个月)
- [ ] 多模态支持 (图像、音频)
- [ ] 自动化知识库更新
- [ ] 企业级安全认证
- [ ] AI助手个性化训练

---

**智能上下文引擎** - 让AI真正理解你的技术需求 🚀
