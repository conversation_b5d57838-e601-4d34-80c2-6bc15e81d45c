<template>
  <div class="chat-interface">
    <!-- 消息列表 -->
    <div class="message-list" ref="messageListRef">
      <div v-if="messages.length === 0" class="welcome-container">
        <div class="welcome-message">
          <h2>欢迎使用 Artifacts Chat</h2>
          <p>开始与 AI 对话，体验 Artifacts 功能</p>
          <div class="example-prompts">
            <a-button
              v-for="example in examplePrompts"
              :key="example"
              type="outline"
              size="small"
              @click="sendExample(example)"
              class="example-btn"
            >
              {{ example }}
            </a-button>
            <a-button
              @click="testArtifact"
              class="example-btn test-artifact-btn"
              type="primary"
              size="small"
            >
              🧪 测试Artifact
            </a-button>
          </div>
        </div>
      </div>
      
      <div v-else class="messages">
        <MessageItem 
          v-for="message in messages" 
          :key="message.id"
          :message="message"
        />
      </div>
      
      <!-- 正在输入指示器 -->
      <div v-if="isTyping" class="typing-indicator">
        <div class="typing-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <span class="typing-text">AI 正在思考...</span>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
      <div class="input-container">
        <a-textarea
          v-model="inputMessage"
          :placeholder="isLoading ? '请等待回复完成...' : '输入消息...'"
          :disabled="isLoading"
          :auto-size="{ minRows: 1, maxRows: 6 }"
          class="message-input"
          @keydown="handleKeyDown"
        />
        <a-button
          type="primary"
          :loading="isLoading"
          :disabled="!inputMessage.trim() || isLoading"
          @click="sendMessage"
          class="send-button"
        >
          <template #icon>
            <icon-send />
          </template>
        </a-button>
      </div>
      <div class="input-tips">
        <span>按 Enter 发送，Shift + Enter 换行</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'
import { IconSend } from '@arco-design/web-vue/es/icon'
import { useChatStore } from '@/stores/chat'
import MessageItem from './MessageItem.vue'

const chatStore = useChatStore()

// 状态
const inputMessage = ref('')
const messageListRef = ref<HTMLElement>()
const isTyping = ref(false)

// 示例提示
const examplePrompts = [
  '创建一个简单的 HTML 页面',
  '生成一个 Vue 组件',
  '制作一个数据可视化图表',
  '设计一个登录表单'
]

// 测试Artifact功能
import { useLayoutStore } from '@/stores/layout'
const layoutStore = useLayoutStore()
  

// 计算属性
const messages = computed(() => chatStore.currentMessages)
const isLoading = computed(() => chatStore.isLoading)

// 方法
const sendMessage = async () => {
  if (!inputMessage.value.trim() || isLoading.value) return

  const message = inputMessage.value.trim()
  inputMessage.value = ''

  // 如果没有当前会话，创建一个
  if (!chatStore.currentSession) {
    chatStore.createSession()
  }

  // 添加用户消息
  chatStore.addMessage(message, 'user')

  // 滚动到底部
  await nextTick()
  scrollToBottom()

  // 发送到后端
  await sendToAPI(message)
}

const sendExample = (example: string) => {
  inputMessage.value = example
  sendMessage()
}

const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

const sendToAPI = async (message: string) => {
  try {
    chatStore.isLoading = true
    isTyping.value = true

    // 调用后端 API，设置较长的超时时间
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 600000) // 60秒超时

    const response = await fetch('/api/chat/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          { role: 'user', content: message }
        ]
      }),
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
    }

    const data = await response.json()
    // 处理转义字符，将 \n 转换为真正的换行符
    const processedContent = (data.content || '抱歉，没有收到有效回复。').replace(/\\n/g, '\n')
    chatStore.addMessage(processedContent, 'assistant')

  } catch (error) {
    console.error('发送消息失败:', error)
    if (error.name === 'AbortError') {
      chatStore.addMessage('请求超时，AI服务响应时间较长，请稍后重试。', 'assistant')
    } else if (error.message.includes('500')) {
      chatStore.addMessage('AI服务暂时不可用，请稍后重试。', 'assistant')
    } else {
      chatStore.addMessage('抱歉，发送消息时出现错误，请稍后重试。', 'assistant')
    }
  } finally {
    chatStore.isLoading = false
    isTyping.value = false
    await nextTick()
    scrollToBottom()
  }
}



const scrollToBottom = () => {
  if (messageListRef.value) {
    messageListRef.value.scrollTop = messageListRef.value.scrollHeight
  }
}

// 监听消息变化，自动滚动到底部
watch(messages, () => {
  nextTick(() => {
    scrollToBottom()
  })
}, { deep: true })
</script>

<style scoped>
.chat-interface {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  scroll-behavior: smooth;
}

.welcome-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.welcome-message {
  text-align: center;
  max-width: 500px;
}

.welcome-message h2 {
  margin: 0 0 8px 0;
  color: #1d2129;
  font-size: 24px;
  font-weight: 600;
}

.welcome-message p {
  margin: 0 0 24px 0;
  color: #86909c;
  font-size: 16px;
}

.example-prompts {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.example-btn {
  margin: 4px;
}

.messages {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px;
  color: #86909c;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  background: #86909c;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

.input-area {
  border-top: 1px solid #e5e6eb;
  padding: 16px 20px;
  background: #fff;
}

.input-container {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  height: 80px;
}

.send-button {
  flex-shrink: 0;
}

.input-tips {
  margin-top: 8px;
  font-size: 12px;
  color: #86909c;
  text-align: center;
}
</style>
