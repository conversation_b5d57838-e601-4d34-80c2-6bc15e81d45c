"""
Context Ranking and Filtering System

Advanced ranking algorithms for context relevance and quality assessment.
"""
import asyncio
import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from collections import Counter
import math
from datetime import datetime, timedelta

from app.services.context_engine.retrieval_engine import SearchResult
from app.core.context_config import context_settings
import structlog

logger = structlog.get_logger(__name__)

@dataclass
class RankingFeatures:
    """Features used for ranking"""
    semantic_similarity: float
    keyword_relevance: float
    title_relevance: float
    content_quality: float
    recency: float
    popularity: float
    completeness: float
    code_quality: float
    
    def to_dict(self) -> Dict[str, float]:
        return {
            'semantic_similarity': self.semantic_similarity,
            'keyword_relevance': self.keyword_relevance,
            'title_relevance': self.title_relevance,
            'content_quality': self.content_quality,
            'recency': self.recency,
            'popularity': self.popularity,
            'completeness': self.completeness,
            'code_quality': self.code_quality
        }

@dataclass
class RankingWeights:
    """Weights for different ranking features"""
    semantic_similarity: float = 0.3
    keyword_relevance: float = 0.2
    title_relevance: float = 0.15
    content_quality: float = 0.15
    recency: float = 0.05
    popularity: float = 0.05
    completeness: float = 0.05
    code_quality: float = 0.05

class ContentQualityAnalyzer:
    """Analyze content quality for ranking"""
    
    def __init__(self):
        # Quality indicators
        self.quality_patterns = {
            'code_blocks': r'```[\s\S]*?```',
            'examples': r'\b(example|demo|sample|tutorial)\b',
            'explanations': r'\b(because|since|therefore|thus|explanation)\b',
            'step_by_step': r'\b(step|first|second|third|next|then|finally)\b',
            'best_practices': r'\b(best practice|recommended|should|avoid|don\'t)\b',
            'documentation': r'\b(documentation|docs|guide|reference)\b'
        }
        
        # Negative quality indicators
        self.negative_patterns = {
            'too_short': lambda text: len(text.strip()) < 50,
            'no_context': lambda text: len(text.split()) < 10,
            'repetitive': lambda text: self._is_repetitive(text)
        }
    
    def analyze_quality(self, content: str, metadata: Dict[str, Any]) -> float:
        """Analyze content quality and return score 0-1"""
        if not content:
            return 0.0
        
        quality_score = 0.5  # Base score
        
        # Positive indicators
        for pattern_name, pattern in self.quality_patterns.items():
            if re.search(pattern, content, re.IGNORECASE):
                quality_score += 0.1
        
        # Length bonus (but not too long)
        length = len(content)
        if 200 <= length <= 2000:
            quality_score += 0.1
        elif length > 2000:
            quality_score += 0.05
        
        # Structure bonus
        if self._has_good_structure(content):
            quality_score += 0.1
        
        # Metadata quality
        if metadata.get('tags'):
            quality_score += 0.05
        if metadata.get('category'):
            quality_score += 0.05
        
        # Negative indicators
        for check_name, check_func in self.negative_patterns.items():
            if check_func(content):
                quality_score -= 0.1
        
        return max(0.0, min(1.0, quality_score))
    
    def _has_good_structure(self, content: str) -> bool:
        """Check if content has good structure"""
        # Check for headers
        if re.search(r'^#+\s', content, re.MULTILINE):
            return True
        
        # Check for lists
        if re.search(r'^\s*[-*+]\s', content, re.MULTILINE):
            return True
        
        # Check for numbered lists
        if re.search(r'^\s*\d+\.\s', content, re.MULTILINE):
            return True
        
        return False
    
    def _is_repetitive(self, content: str) -> bool:
        """Check if content is repetitive"""
        words = content.lower().split()
        if len(words) < 10:
            return False
        
        word_counts = Counter(words)
        most_common = word_counts.most_common(5)
        
        # If top words appear too frequently, it's repetitive
        total_words = len(words)
        for word, count in most_common:
            if count / total_words > 0.3:  # 30% threshold
                return True
        
        return False

class CodeQualityAnalyzer:
    """Analyze code quality for ranking"""
    
    def __init__(self):
        self.quality_indicators = {
            'comments': r'#.*|//.*|/\*[\s\S]*?\*/',
            'docstrings': r'"""[\s\S]*?"""|\'\'\'[\s\S]*?\'\'\'',
            'type_hints': r':\s*\w+|-> \w+',
            'error_handling': r'\b(try|catch|except|finally|throw|raise)\b',
            'async_patterns': r'\b(async|await|Promise)\b',
            'imports': r'\b(import|from|require)\b',
            'functions': r'\b(def|function|const|let|var)\s+\w+',
            'classes': r'\b(class|interface|type)\s+\w+'
        }
    
    def analyze_code_quality(self, code: str, language: str) -> float:
        """Analyze code quality and return score 0-1"""
        if not code:
            return 0.0
        
        quality_score = 0.3  # Base score for any code
        
        # Check for quality indicators
        for indicator, pattern in self.quality_indicators.items():
            if re.search(pattern, code, re.IGNORECASE):
                quality_score += 0.1
        
        # Language-specific checks
        if language == 'python':
            quality_score += self._analyze_python_quality(code)
        elif language in ['javascript', 'typescript']:
            quality_score += self._analyze_js_quality(code)
        
        # Length and complexity
        lines = code.split('\n')
        non_empty_lines = [line for line in lines if line.strip()]
        
        if 10 <= len(non_empty_lines) <= 100:
            quality_score += 0.1
        
        # Indentation consistency
        if self._has_consistent_indentation(lines):
            quality_score += 0.05
        
        return max(0.0, min(1.0, quality_score))
    
    def _analyze_python_quality(self, code: str) -> float:
        """Python-specific quality analysis"""
        bonus = 0.0
        
        # PEP 8 style indicators
        if re.search(r'if __name__ == ["\']__main__["\']:', code):
            bonus += 0.05
        
        # Type hints
        if re.search(r':\s*(str|int|float|bool|List|Dict|Optional)', code):
            bonus += 0.05
        
        # Docstrings
        if re.search(r'"""[\s\S]*?"""', code):
            bonus += 0.05
        
        return bonus
    
    def _analyze_js_quality(self, code: str) -> float:
        """JavaScript/TypeScript-specific quality analysis"""
        bonus = 0.0
        
        # Modern JS patterns
        if re.search(r'\b(const|let)\b', code):
            bonus += 0.05
        
        # Arrow functions
        if re.search(r'=>', code):
            bonus += 0.03
        
        # Destructuring
        if re.search(r'\{.*\}\s*=', code):
            bonus += 0.03
        
        # Template literals
        if re.search(r'`.*\$\{.*\}.*`', code):
            bonus += 0.03
        
        return bonus
    
    def _has_consistent_indentation(self, lines: List[str]) -> bool:
        """Check for consistent indentation"""
        indentations = []
        for line in lines:
            if line.strip():  # Skip empty lines
                indent = len(line) - len(line.lstrip())
                if indent > 0:
                    indentations.append(indent)
        
        if not indentations:
            return True
        
        # Check if indentations follow a pattern (2 or 4 spaces)
        common_indents = Counter(indentations)
        most_common = common_indents.most_common(1)[0][1]
        
        return most_common / len(indentations) > 0.7

class ContextRanker:
    """Advanced context ranking system"""
    
    def __init__(self, weights: Optional[RankingWeights] = None):
        self.weights = weights or RankingWeights()
        self.content_analyzer = ContentQualityAnalyzer()
        self.code_analyzer = CodeQualityAnalyzer()
        
        logger.info("Initialized ContextRanker")
    
    async def rank_results(
        self,
        results: List[SearchResult],
        query: str,
        user_context: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Rank search results using multiple factors"""
        try:
            if not results:
                return results
            
            # Extract features for each result
            ranked_results = []
            
            for result in results:
                features = await self._extract_features(result, query, user_context)
                final_score = self._calculate_final_score(features)
                
                # Update result score
                result.score = final_score
                result.metadata['ranking_features'] = features.to_dict()
                
                ranked_results.append(result)
            
            # Sort by final score
            ranked_results.sort(key=lambda x: x.score, reverse=True)
            
            logger.info(
                "Ranked search results",
                total_results=len(results),
                query_length=len(query)
            )
            
            return ranked_results
            
        except Exception as e:
            logger.error("Ranking failed", error=str(e))
            return results
    
    async def _extract_features(
        self,
        result: SearchResult,
        query: str,
        user_context: Optional[Dict[str, Any]] = None
    ) -> RankingFeatures:
        """Extract ranking features from a search result"""
        
        # Semantic similarity (from original search)
        semantic_similarity = result.score
        
        # Keyword relevance
        keyword_relevance = self._calculate_keyword_relevance(query, result)
        
        # Title relevance
        title_relevance = self._calculate_title_relevance(query, result.title)
        
        # Content quality
        content_quality = self.content_analyzer.analyze_quality(
            result.content, result.metadata
        )
        
        # Code quality (if applicable)
        code_quality = 0.0
        if result.source_type == 'code':
            language = result.metadata.get('language', '')
            code_quality = self.code_analyzer.analyze_code_quality(
                result.content, language
            )
        
        # Recency
        recency = self._calculate_recency(result.metadata)
        
        # Popularity (based on metadata)
        popularity = self._calculate_popularity(result.metadata)
        
        # Completeness
        completeness = self._calculate_completeness(result)
        
        return RankingFeatures(
            semantic_similarity=semantic_similarity,
            keyword_relevance=keyword_relevance,
            title_relevance=title_relevance,
            content_quality=content_quality,
            recency=recency,
            popularity=popularity,
            completeness=completeness,
            code_quality=code_quality
        )
    
    def _calculate_keyword_relevance(self, query: str, result: SearchResult) -> float:
        """Calculate keyword relevance score"""
        query_terms = set(query.lower().split())
        content_terms = set(result.content.lower().split())
        title_terms = set(result.title.lower().split())
        
        # Calculate overlap
        content_overlap = len(query_terms.intersection(content_terms))
        title_overlap = len(query_terms.intersection(title_terms))
        
        total_query_terms = len(query_terms)
        if total_query_terms == 0:
            return 0.0
        
        # Weight title matches higher
        relevance = (content_overlap + title_overlap * 2) / (total_query_terms * 2)
        return min(1.0, relevance)
    
    def _calculate_title_relevance(self, query: str, title: str) -> float:
        """Calculate title relevance score"""
        if not title:
            return 0.0
        
        query_terms = set(query.lower().split())
        title_terms = set(title.lower().split())
        
        overlap = len(query_terms.intersection(title_terms))
        total_query_terms = len(query_terms)
        
        if total_query_terms == 0:
            return 0.0
        
        return overlap / total_query_terms
    
    def _calculate_recency(self, metadata: Dict[str, Any]) -> float:
        """Calculate recency score based on creation/update time"""
        created_at = metadata.get('created_at')
        updated_at = metadata.get('updated_at')
        
        # Use the more recent timestamp
        timestamp_str = updated_at or created_at
        if not timestamp_str:
            return 0.5  # Neutral score for unknown dates
        
        try:
            # Parse timestamp (assuming ISO format)
            timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            now = datetime.now(timestamp.tzinfo)
            
            # Calculate age in days
            age_days = (now - timestamp).days
            
            # Recency score decreases over time
            if age_days <= 7:
                return 1.0
            elif age_days <= 30:
                return 0.8
            elif age_days <= 90:
                return 0.6
            elif age_days <= 365:
                return 0.4
            else:
                return 0.2
                
        except Exception:
            return 0.5
    
    def _calculate_popularity(self, metadata: Dict[str, Any]) -> float:
        """Calculate popularity score based on usage metrics"""
        # This would typically use view counts, likes, etc.
        # For now, use simple heuristics
        
        score = 0.5  # Base score
        
        # Framework popularity
        framework = metadata.get('framework', '').lower()
        popular_frameworks = {'vue', 'react', 'fastapi', 'django'}
        if framework in popular_frameworks:
            score += 0.2
        
        # Category popularity
        category = metadata.get('category', '').lower()
        popular_categories = {'tutorial', 'guide', 'example', 'component'}
        if category in popular_categories:
            score += 0.1
        
        # Tags count (more tags might indicate more comprehensive content)
        tags = metadata.get('tags', [])
        if len(tags) > 3:
            score += 0.1
        
        return min(1.0, score)
    
    def _calculate_completeness(self, result: SearchResult) -> float:
        """Calculate completeness score"""
        score = 0.5  # Base score
        
        # Content length
        content_length = len(result.content)
        if content_length > 500:
            score += 0.2
        elif content_length > 200:
            score += 0.1
        
        # Has title
        if result.title:
            score += 0.1
        
        # Has metadata
        metadata = result.metadata
        if metadata.get('description'):
            score += 0.1
        if metadata.get('tags'):
            score += 0.05
        if metadata.get('category'):
            score += 0.05
        
        return min(1.0, score)
    
    def _calculate_final_score(self, features: RankingFeatures) -> float:
        """Calculate final weighted score"""
        score = (
            features.semantic_similarity * self.weights.semantic_similarity +
            features.keyword_relevance * self.weights.keyword_relevance +
            features.title_relevance * self.weights.title_relevance +
            features.content_quality * self.weights.content_quality +
            features.recency * self.weights.recency +
            features.popularity * self.weights.popularity +
            features.completeness * self.weights.completeness +
            features.code_quality * self.weights.code_quality
        )
        
        return max(0.0, min(1.0, score))

class ResultFilter:
    """Filter search results based on various criteria"""
    
    def __init__(self):
        self.min_content_length = 20
        self.max_results_per_source = 3
    
    async def filter_results(
        self,
        results: List[SearchResult],
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Apply filters to search results"""
        if not results:
            return results
        
        filtered_results = results
        
        # Apply basic quality filters
        filtered_results = self._filter_by_quality(filtered_results)
        
        # Apply user-specified filters
        if filters:
            filtered_results = self._apply_user_filters(filtered_results, filters)
        
        # Diversify results
        filtered_results = self._diversify_results(filtered_results)
        
        logger.info(
            "Filtered search results",
            original_count=len(results),
            filtered_count=len(filtered_results)
        )
        
        return filtered_results
    
    def _filter_by_quality(self, results: List[SearchResult]) -> List[SearchResult]:
        """Filter out low-quality results"""
        return [
            result for result in results
            if len(result.content.strip()) >= self.min_content_length
            and result.title.strip()  # Must have a title
        ]
    
    def _apply_user_filters(
        self,
        results: List[SearchResult],
        filters: Dict[str, Any]
    ) -> List[SearchResult]:
        """Apply user-specified filters"""
        filtered_results = []
        
        for result in results:
            metadata = result.metadata
            
            # Language filter
            if 'language' in filters:
                if metadata.get('language') != filters['language']:
                    continue
            
            # Framework filter
            if 'framework' in filters:
                if metadata.get('framework') != filters['framework']:
                    continue
            
            # Source type filter
            if 'source_type' in filters:
                if result.source_type != filters['source_type']:
                    continue
            
            # Category filter
            if 'category' in filters:
                if metadata.get('category') != filters['category']:
                    continue
            
            filtered_results.append(result)
        
        return filtered_results
    
    def _diversify_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """Diversify results to avoid too many from same source"""
        source_counts = {}
        diversified_results = []
        
        for result in results:
            source = result.metadata.get('file_path', 'unknown')
            source_count = source_counts.get(source, 0)
            
            if source_count < self.max_results_per_source:
                diversified_results.append(result)
                source_counts[source] = source_count + 1
        
        return diversified_results
