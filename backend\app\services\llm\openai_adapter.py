"""
OpenAI API Adapter for LLM Integration

Provides unified interface for OpenAI GPT models with context injection.
"""
import asyncio
import json
from typing import List, Dict, Any, Optional, AsyncGenerator, Union
from dataclasses import dataclass
import httpx
from datetime import datetime

from app.core.context_config import context_settings
from app.services.context_engine.intelligent_context import get_context_engine, ContextRequest
import logging
from app.core.error_handler import (
    retry_async, circuit_breaker, handle_errors,
    API_RETRY_CONFIG, API_CIRCUIT_BREAKER_CONFIG,
    NetworkError, TimeoutError, RateLimitError, ErrorType
)

logger = logging.getLogger(__name__)

@dataclass
class ChatMessage:
    """Chat message structure"""
    role: str  # 'system', 'user', 'assistant'
    content: str
    name: Optional[str] = None

@dataclass
class ChatRequest:
    """Chat request with context enhancement"""
    messages: List[ChatMessage]
    model: str = "gpt-3.5-turbo"
    temperature: float = 0.7
    max_tokens: Optional[int] = None
    stream: bool = False
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    enable_context: bool = True

@dataclass
class ChatResponse:
    """Chat response structure"""
    id: str
    model: str
    choices: List[Dict[str, Any]]
    usage: Dict[str, int]
    created: int
    context_used: bool = False
    context_sources: List[str] = None

class OpenAIAdapter:
    """OpenAI API adapter with intelligent context injection"""
    
    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None):
        self.api_key = api_key or context_settings.OPENAI_API_KEY
        self.base_url = base_url or context_settings.OPENAI_BASE_URL or "https://api.openai.com/v1"
        self.timeout = context_settings.LLM_TIMEOUT or 60
        
        # HTTP client configuration
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.timeout),
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
        )
        
        logger.info("Initialized OpenAI adapter", base_url=self.base_url)
    
    @retry_async(API_RETRY_CONFIG)
    @circuit_breaker("openai_api", API_CIRCUIT_BREAKER_CONFIG)
    @handle_errors([ErrorType.NETWORK_ERROR, ErrorType.TIMEOUT_ERROR, ErrorType.RATE_LIMIT_ERROR])
    async def chat_completion(self, request: ChatRequest) -> Union[ChatResponse, AsyncGenerator]:
        """Generate chat completion with optional context enhancement"""
        try:
            # Enhance messages with context if enabled
            enhanced_messages = request.messages
            context_used = False
            context_sources = []
            
            if request.enable_context and len(request.messages) > 0:
                enhanced_messages, context_info = await self._enhance_with_context(
                    request.messages, request.user_id, request.session_id
                )
                context_used = context_info.get('context_used', False)
                context_sources = context_info.get('sources', [])
            
            # Prepare OpenAI API request
            api_request = {
                "model": request.model,
                "messages": [{"role": msg.role, "content": msg.content} for msg in enhanced_messages],
                "temperature": request.temperature,
                "stream": request.stream
            }
            
            if request.max_tokens:
                api_request["max_tokens"] = request.max_tokens
            
            # Make API call
            if request.stream:
                return self._stream_completion(api_request, context_used, context_sources)
            else:
                return await self._complete_completion(api_request, context_used, context_sources)
                
        except Exception as e:
            logger.error("Chat completion failed", error=str(e))
            raise
    
    async def _enhance_with_context(
        self, 
        messages: List[ChatMessage], 
        user_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> tuple[List[ChatMessage], Dict[str, Any]]:
        """Enhance messages with intelligent context"""
        try:
            # Get the latest user message for context search
            user_messages = [msg for msg in messages if msg.role == "user"]
            if not user_messages:
                return messages, {"context_used": False}
            
            latest_query = user_messages[-1].content
            
            # Get conversation history for context
            conversation_history = []
            for msg in messages[-10:]:  # Last 10 messages for context
                conversation_history.append({
                    "role": msg.role,
                    "content": msg.content
                })
            
            # Request intelligent context
            context_engine = await get_context_engine()
            context_request = ContextRequest(
                query=latest_query,
                conversation_history=conversation_history,
                max_results=5,
                include_code=True,
                include_docs=True
            )
            
            context_response = await context_engine.get_context(context_request)
            
            # Build context if results found
            if context_response.contexts:
                context_content = self._build_context_content(context_response)
                
                # Insert context as system message or enhance existing system message
                enhanced_messages = self._inject_context(messages, context_content)
                
                context_info = {
                    "context_used": True,
                    "sources": [ctx.id for ctx in context_response.contexts],
                    "total_results": context_response.total_found,
                    "processing_time": context_response.processing_time_ms
                }
                
                logger.info(
                    "Enhanced messages with context",
                    query_length=len(latest_query),
                    context_results=len(context_response.contexts),
                    processing_time=context_response.processing_time_ms
                )
                
                return enhanced_messages, context_info
            else:
                return messages, {"context_used": False}
                
        except Exception as e:
            logger.error("Context enhancement failed", error=str(e))
            return messages, {"context_used": False, "error": str(e)}
    
    def _build_context_content(self, context_response) -> str:
        """Build context content from search results"""
        context_parts = [
            "# 相关技术文档和代码示例\n",
            "以下是与用户查询相关的技术文档和代码示例，请参考这些内容来提供准确和实用的回答：\n"
        ]
        
        for i, context in enumerate(context_response.contexts, 1):
            context_parts.append(f"\n## 参考资料 {i}: {context.title}")
            context_parts.append(f"**类型**: {context.source_type}")
            context_parts.append(f"**相关度**: {context.score:.2f}")
            
            # Add metadata
            metadata = context.metadata
            if metadata.get('framework'):
                context_parts.append(f"**框架**: {metadata['framework']}")
            if metadata.get('language'):
                context_parts.append(f"**语言**: {metadata['language']}")
            
            # Add content
            if context.source_type == 'code':
                context_parts.append(f"\n```{metadata.get('language', '')}")
                context_parts.append(context.content)
                context_parts.append("```")
            else:
                context_parts.append(f"\n{context.content}")
            
            context_parts.append("\n---")
        
        context_parts.append(
            "\n请基于以上参考资料回答用户问题，确保答案准确、实用且符合最佳实践。"
            "如果参考资料中有代码示例，请优先使用这些示例来说明概念。"
        )
        
        return "\n".join(context_parts)
    
    def _inject_context(self, messages: List[ChatMessage], context_content: str) -> List[ChatMessage]:
        """Inject context into message list"""
        enhanced_messages = []
        
        # Check if there's already a system message
        has_system_message = any(msg.role == "system" for msg in messages)
        
        if has_system_message:
            # Enhance existing system message
            for msg in messages:
                if msg.role == "system":
                    enhanced_content = f"{msg.content}\n\n{context_content}"
                    enhanced_messages.append(ChatMessage(
                        role="system",
                        content=enhanced_content
                    ))
                else:
                    enhanced_messages.append(msg)
        else:
            # Add new system message with context
            enhanced_messages.append(ChatMessage(
                role="system",
                content=context_content
            ))
            enhanced_messages.extend(messages)
        
        return enhanced_messages
    
    async def _complete_completion(
        self, 
        api_request: Dict[str, Any], 
        context_used: bool, 
        context_sources: List[str]
    ) -> ChatResponse:
        """Handle non-streaming completion"""
        try:
            response = await self.client.post(
                f"{self.base_url}/chat/completions",
                json=api_request
            )
            response.raise_for_status()
            
            data = response.json()
            
            return ChatResponse(
                id=data["id"],
                model=data["model"],
                choices=data["choices"],
                usage=data["usage"],
                created=data["created"],
                context_used=context_used,
                context_sources=context_sources
            )
            
        except httpx.HTTPStatusError as e:
            status_code = e.response.status_code
            response_text = e.response.text

            if status_code == 429:
                raise RateLimitError(f"OpenAI rate limit exceeded: {response_text}")
            elif status_code >= 500:
                raise NetworkError(f"OpenAI server error {status_code}: {response_text}")
            else:
                logger.error(f"OpenAI API error {status_code}: {response_text}")
                raise
        except httpx.TimeoutException as e:
            raise TimeoutError(f"OpenAI API timeout: {str(e)}")
        except httpx.NetworkError as e:
            raise NetworkError(f"OpenAI network error: {str(e)}")
        except Exception as e:
            logger.error(f"Completion request failed: {str(e)}")
            raise
    
    async def _stream_completion(
        self, 
        api_request: Dict[str, Any], 
        context_used: bool, 
        context_sources: List[str]
    ) -> AsyncGenerator:
        """Handle streaming completion"""
        try:
            async with self.client.stream(
                "POST",
                f"{self.base_url}/chat/completions",
                json=api_request
            ) as response:
                response.raise_for_status()
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]  # Remove "data: " prefix
                        
                        if data_str.strip() == "[DONE]":
                            break
                        
                        try:
                            data = json.loads(data_str)
                            
                            # Add context information to first chunk
                            if "choices" in data and len(data["choices"]) > 0:
                                if not hasattr(self, '_context_info_sent'):
                                    data["context_used"] = context_used
                                    data["context_sources"] = context_sources
                                    self._context_info_sent = True
                            
                            yield data
                            
                        except json.JSONDecodeError:
                            continue
                            
        except httpx.HTTPStatusError as e:
            logger.error("OpenAI streaming API error", status_code=e.response.status_code)
            raise
        except Exception as e:
            logger.error("Streaming completion failed", error=str(e))
            raise
    
    async def list_models(self) -> List[Dict[str, Any]]:
        """List available models"""
        try:
            response = await self.client.get(f"{self.base_url}/models")
            response.raise_for_status()
            
            data = response.json()
            return data.get("data", [])
            
        except Exception as e:
            logger.error("Failed to list models", error=str(e))
            return []
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

# Singleton instance
_openai_adapter = None

async def get_openai_adapter() -> OpenAIAdapter:
    """Get singleton OpenAI adapter instance"""
    global _openai_adapter
    
    if _openai_adapter is None:
        _openai_adapter = OpenAIAdapter()
    
    return _openai_adapter
