# Artifacts Chat

一个类似 Claude.ai 的本地化聊天网站，核心功能是实现 Artifacts 预览功能，支持 HTML/CSS/JS、Vue 组件、G2Plot 图表等的即时渲染。

## 项目结构

```
/artifacts-chat
├── /backend          # FastAPI 后端
├── /frontend         # Vue 3 前端
│   ├── /public
│   │   └── /vendor   # 离线依赖库 (Vue编译器、G2Plot、Mermaid等)
│   └── /src
└── README.md
```

## 技术栈

### 前端
- **框架**: Vue 3 (使用 Vite 构建)
- **UI 组件库**: Arco Design Vue
- **状态管理**: Pinia
- **Markdown 解析**: markdown-it
- **代码高亮**: highlight.js
- **包管理**: pnpm

### 后端
- **语言**: Python 3
- **包管理**: uv
- **Web 框架**: FastAPI
- **数据库**: SQLite (开发阶段)

### Artifacts 渲染
- **沙箱**: `<iframe>` + `postMessage` API
- **Vue 实时编译**: `@vue/compiler-sfc` (本地化部署)
- **图表库**: G2Plot, Mermaid (本地化部署)

## 开发阶段

- [x] 阶段一：项目初始化与基础架构
  - [x] 创建项目目录结构
  - [x] 初始化后端 FastAPI 项目
  - [x] 初始化前端 Vite + Vue 3 项目
  - [x] 配置离线依赖资源
  - [x] 设计数据库模型

- [x] 阶段二：核心布局与聊天功能
  - [x] 实现主界面布局组件
  - [x] 实现聊天历史侧边栏
  - [x] 实现聊天界面组件
  - [x] 集成Pinia状态管理
  - [x] 实现Markdown渲染和代码高亮

- [ ] 阶段三：Artifacts功能MVP
- [ ] 阶段四：功能完善与优化

## 🎉 阶段一完成总结

### ✅ 已完成的工作

1. **项目结构创建**
   - 创建了完整的前后端目录结构
   - 建立了清晰的代码组织架构

2. **后端 FastAPI 项目**
   - 使用 uv 初始化 Python 项目
   - 配置了 FastAPI、SQLAlchemy、uvicorn 等核心依赖
   - 创建了完整的 MVC 架构（models、schemas、api、services）
   - 实现了基本的 API 路由结构
   - 配置了 CORS 和基本的应用设置

3. **前端 Vue 3 项目**
   - 使用 pnpm 初始化 Vite + Vue 3 项目
   - 集成了 Arco Design Vue 组件库
   - 配置了 Pinia 状态管理
   - 集成了 markdown-it 和 highlight.js
   - 创建了基本的路由和组件结构

4. **离线依赖配置** ⭐ **关键特性**
   - 复制了 Vue 编译器到本地 (`compiler-sfc.esm-browser.js`)
   - 复制了 G2Plot 图表库到本地 (`g2plot.min.js`)
   - 复制了 Mermaid 图表库到本地 (`mermaid.min.js`)
   - 创建了 `renderer.html` 沙箱渲染器页面

5. **数据库模型设计**
   - 设计了完整的 SQLite 数据库模式
   - 创建了用户、聊天会话、消息、Artifacts 表
   - 实现了数据库初始化脚本
   - 添加了示例数据

### 🚀 如何启动项目

#### 后端启动
```bash
cd artifacts-chat/backend
uv run python run.py
# 服务器将在 http://localhost:8080 启动
```

#### 前端启动
```bash
cd artifacts-chat/frontend
pnpm dev
# 开发服务器将在 http://localhost:5173 启动
```

### 📁 项目结构概览
```
/artifacts-chat
├── /backend                 # FastAPI 后端
│   ├── /app
│   │   ├── /api            # API 路由
│   │   ├── /core           # 核心配置
│   │   ├── /db             # 数据库配置
│   │   ├── /models         # SQLAlchemy 模型
│   │   ├── /schemas        # Pydantic 模式
│   │   └── /services       # 业务逻辑
│   ├── /migrations         # 数据库迁移
│   └── artifacts_chat.db   # SQLite 数据库
└── /frontend               # Vue 3 前端
    ├── /public
    │   ├── /vendor         # 离线依赖库
    │   └── renderer.html   # Artifacts 渲染器
    └── /src
        ├── /components     # Vue 组件
        ├── /stores         # Pinia 状态管理
        └── /views          # 页面视图
```

## 🎉 阶段二完成总结

### ✅ 新增完成的工作

6. **主界面布局组件**
   - 实现了响应式布局，支持单栏/双栏动态切换
   - 创建了可折叠的左侧边栏
   - 实现了 Artifact 面板的显示/隐藏逻辑
   - 支持移动端适配

7. **聊天历史侧边栏**
   - 实现了聊天会话列表展示
   - 支持新建、重命名、删除会话功能
   - 添加了搜索功能
   - 实现了会话切换逻辑

8. **聊天界面组件**
   - 创建了完整的聊天界面
   - 实现了消息流展示
   - 支持多行输入和快捷键发送
   - 添加了欢迎页面和示例提示
   - 实现了打字指示器

9. **Markdown渲染器**
   - 集成了 markdown-it 和 highlight.js
   - 实现了代码高亮功能
   - **关键特性**：自动检测 Artifacts 代码块
   - 支持点击预览按钮切换到 Artifact 面板

10. **消息组件系统**
    - 创建了用户和AI消息的不同样式
    - 实现了时间戳显示
    - 支持头像和角色标识

11. **状态管理完善**
    - 扩展了 Pinia stores
    - 添加了布局状态管理 (layout store)
    - 实现了 Artifact 历史记录管理
    - 完善了聊天状态管理

### 🔧 技术亮点

- **响应式设计**：完全适配桌面端和移动端
- **组件化架构**：高度模块化的 Vue 组件设计
- **状态管理**：使用 Pinia 进行集中状态管理
- **类型安全**：全面使用 TypeScript
- **样式系统**：使用 Arco Design Vue 组件库
- **代码高亮**：集成 highlight.js 支持多种语言

### 🚀 当前功能演示

现在您可以：
1. 启动前端服务 (`pnpm dev`)
2. 体验完整的聊天界面
3. 测试侧边栏功能
4. 查看 Markdown 渲染效果
5. 观察 Artifacts 代码块的特殊样式

### 📋 下一步：阶段三 - Artifacts功能MVP

接下来我们将实现：
- Artifact 渲染器的完整功能
- postMessage 通信机制
- HTML/CSS/JS 和 Vue SFC 渲染
- 动态布局切换逻辑

## 安全特性

- 严格的 iframe sandbox 配置
- CSP (Content Security Policy) 策略
- postMessage 通信验证
- 离线依赖，无外网访问

## 开发指南

详细的开发指南请参考 `/docs/requirement.md` 和 `/docs/requirement-ai.md`。
