/**
 * 安全工具和验证
 */

export class SecurityValidator {
  // XSS 防护 - HTML 转义
  static escapeHtml(text: string): string {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  // 验证 URL 是否安全
  static isUrlSafe(url: string): boolean {
    try {
      const urlObj = new URL(url)
      // 只允许 http 和 https 协议
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return false
      }
      // 不允许访问本地地址（除了开发环境）
      if (urlObj.hostname === 'localhost' || urlObj.hostname === '127.0.0.1') {
        return process.env.NODE_ENV === 'development'
      }
      return true
    } catch {
      return false
    }
  }

  // 验证代码是否包含危险内容
  static validateCode(code: string, type: string): { safe: boolean; issues: string[] } {
    const issues: string[] = []
    
    // 通用危险模式
    const dangerousPatterns = [
      /eval\s*\(/gi,
      /Function\s*\(/gi,
      /setTimeout\s*\(\s*["'`][^"'`]*["'`]/gi,
      /setInterval\s*\(\s*["'`][^"'`]*["'`]/gi,
      /document\.write/gi,
      /innerHTML\s*=/gi,
      /outerHTML\s*=/gi,
      /insertAdjacentHTML/gi,
      /execScript/gi,
      /javascript:/gi,
      /vbscript:/gi,
      /data:/gi,
      /file:/gi
    ]

    // 检查危险模式
    dangerousPatterns.forEach(pattern => {
      if (pattern.test(code)) {
        issues.push(`Potentially dangerous pattern detected: ${pattern.source}`)
      }
    })

    // 特定类型的检查
    if (type === 'html') {
      // HTML 特定检查
      const htmlDangerousPatterns = [
        /<script[^>]*>/gi,
        /<iframe[^>]*>/gi,
        /<object[^>]*>/gi,
        /<embed[^>]*>/gi,
        /<form[^>]*>/gi,
        /on\w+\s*=/gi, // 事件处理器
        /href\s*=\s*["']javascript:/gi
      ]

      htmlDangerousPatterns.forEach(pattern => {
        if (pattern.test(code)) {
          issues.push(`HTML security issue: ${pattern.source}`)
        }
      })
    }

    return {
      safe: issues.length === 0,
      issues
    }
  }

  // 清理用户输入
  static sanitizeInput(input: string): string {
    return input
      .replace(/[<>]/g, '') // 移除尖括号
      .replace(/javascript:/gi, '') // 移除 javascript: 协议
      .replace(/on\w+\s*=/gi, '') // 移除事件处理器
      .trim()
  }

  // 验证 JSON 数据
  static validateJson(jsonString: string): { valid: boolean; data?: any; error?: string } {
    try {
      const data = JSON.parse(jsonString)
      
      // 检查是否包含函数或危险内容
      const jsonStr = JSON.stringify(data)
      if (jsonStr.includes('function') || jsonStr.includes('eval')) {
        return {
          valid: false,
          error: 'JSON contains potentially dangerous content'
        }
      }
      
      return { valid: true, data }
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : 'Invalid JSON'
      }
    }
  }

  // CSP 违规报告处理
  static setupCSPReporting(): void {
    document.addEventListener('securitypolicyviolation', (event) => {
      console.warn('CSP Violation:', {
        blockedURI: event.blockedURI,
        violatedDirective: event.violatedDirective,
        originalPolicy: event.originalPolicy,
        sourceFile: event.sourceFile,
        lineNumber: event.lineNumber
      })
      
      // 在生产环境中，这里应该发送到监控服务
      if (process.env.NODE_ENV === 'production') {
        // 发送 CSP 违规报告到服务器
        fetch('/api/csp-report', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            blockedURI: event.blockedURI,
            violatedDirective: event.violatedDirective,
            sourceFile: event.sourceFile,
            lineNumber: event.lineNumber,
            timestamp: new Date().toISOString()
          })
        }).catch(console.error)
      }
    })
  }
}

// Artifact 沙箱安全管理
export class ArtifactSandbox {
  private static allowedOrigins = [
    window.location.origin
  ]

  // 验证 postMessage 来源
  static validateMessageOrigin(origin: string): boolean {
    return this.allowedOrigins.includes(origin)
  }

  // 创建安全的 iframe 配置
  static createSecureIframeConfig(): Record<string, string> {
    return {
      sandbox: 'allow-scripts allow-same-origin',
      'csp': "default-src 'self'; script-src 'self' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self';",
      'referrerpolicy': 'no-referrer',
      'loading': 'lazy'
    }
  }

  // 验证 Artifact 代码安全性
  static validateArtifactCode(code: string, type: string): { safe: boolean; sanitizedCode?: string; issues: string[] } {
    const validation = SecurityValidator.validateCode(code, type)
    
    if (!validation.safe) {
      return {
        safe: false,
        issues: validation.issues
      }
    }

    // 如果代码安全，返回原始代码
    return {
      safe: true,
      sanitizedCode: code,
      issues: []
    }
  }

  // 监控 iframe 行为
  static monitorIframeActivity(iframe: HTMLIFrameElement): void {
    // 监听 iframe 的加载事件
    iframe.addEventListener('load', () => {
      console.log('Artifact iframe loaded successfully')
    })

    // 监听错误事件
    iframe.addEventListener('error', (event) => {
      console.error('Artifact iframe error:', event)
    })

    // 设置超时检查
    setTimeout(() => {
      if (!iframe.contentDocument) {
        console.warn('Artifact iframe may have failed to load')
      }
    }, 5000)
  }
}

// 初始化安全设置
export function initializeSecurity(): void {
  // 设置 CSP 报告
  SecurityValidator.setupCSPReporting()
  
  // 禁用控制台中的危险函数（生产环境）
  if (process.env.NODE_ENV === 'production') {
    // 重写 eval 函数
    window.eval = function() {
      throw new Error('eval is disabled for security reasons')
    }
    
    // 重写 Function 构造函数
    window.Function = function() {
      throw new Error('Function constructor is disabled for security reasons')
    } as any
  }
  
  console.log('Security measures initialized')
}
