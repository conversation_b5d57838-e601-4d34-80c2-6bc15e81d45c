#!/bin/bash

# Stop Context Engine Development Environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

COMPOSE_FILE="docker-compose.dev.yml"

echo -e "${BLUE}🛑 Stopping Context Engine Development Environment${NC}"
echo "=================================================="

# Stop development servers
stop_dev_servers() {
    echo -e "${YELLOW}🔌 Stopping development servers...${NC}"
    
    # Kill backend server
    if [ -f .backend.pid ]; then
        echo "Stopping backend server..."
        kill $(cat .backend.pid) 2>/dev/null || true
        rm .backend.pid
        echo -e "${GREEN}✅ Backend server stopped${NC}"
    fi
    
    # Kill frontend server
    if [ -f .frontend.pid ]; then
        echo "Stopping frontend server..."
        kill $(cat .frontend.pid) 2>/dev/null || true
        rm .frontend.pid
        echo -e "${GREEN}✅ Frontend server stopped${NC}"
    fi
    
    # Kill any remaining uvicorn processes
    pkill -f "uvicorn app.main:app" 2>/dev/null || true
    
    # Kill any remaining vite processes
    pkill -f "vite" 2>/dev/null || true
}

# Stop Docker services
stop_docker_services() {
    echo -e "${YELLOW}🐳 Stopping Docker services...${NC}"
    
    if [ -f "$COMPOSE_FILE" ]; then
        docker-compose -f $COMPOSE_FILE down --remove-orphans
        echo -e "${GREEN}✅ Docker services stopped${NC}"
    else
        echo -e "${YELLOW}⚠️  Docker compose file not found${NC}"
    fi
}

# Clean up volumes (optional)
cleanup_volumes() {
    if [ "$1" = "--clean-volumes" ]; then
        echo -e "${YELLOW}🧹 Cleaning up Docker volumes...${NC}"
        docker-compose -f $COMPOSE_FILE down -v
        echo -e "${GREEN}✅ Volumes cleaned${NC}"
    fi
}

# Show final status
show_final_status() {
    echo ""
    echo -e "${GREEN}✅ All services stopped successfully${NC}"
    echo ""
    echo -e "${YELLOW}💡 Tips:${NC}"
    echo "• Use './scripts/start-dev-orbstack.sh' to restart the environment"
    echo "• Use '--clean-volumes' flag to also remove data volumes"
    echo "• Check remaining processes with 'docker ps' and 'ps aux | grep uvicorn'"
}

# Main execution
main() {
    stop_dev_servers
    stop_docker_services
    cleanup_volumes $1
    show_final_status
}

# Run main function with arguments
main $@
