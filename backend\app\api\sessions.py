"""
Chat sessions API endpoints
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from app.db.database import get_db
from app.schemas.session import SessionResponse, SessionCreate
from app.services.database import get_database_service

router = APIRouter()

@router.get("/", response_model=List[SessionResponse])
async def get_sessions(
    user_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """
    Get all chat sessions for current user
    """
    db_service = get_database_service(db)
    sessions = db_service.get_sessions(user_id)
    return sessions

@router.post("/", response_model=SessionResponse)
async def create_session(
    session: SessionCreate,
    db: Session = Depends(get_db)
):
    """
    Create new chat session
    """
    db_service = get_database_service(db)
    new_session = db_service.create_session(session)
    return new_session

@router.get("/{session_id}", response_model=SessionResponse)
async def get_session(
    session_id: int,
    db: Session = Depends(get_db)
):
    """
    Get specific chat session
    """
    db_service = get_database_service(db)
    session = db_service.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    return session

@router.put("/{session_id}", response_model=SessionResponse)
async def update_session(
    session_id: int,
    title: str,
    db: Session = Depends(get_db)
):
    """
    Update chat session title
    """
    db_service = get_database_service(db)
    session = db_service.update_session_title(session_id, title)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    return session

@router.delete("/{session_id}")
async def delete_session(
    session_id: int,
    db: Session = Depends(get_db)
):
    """
    Delete chat session
    """
    db_service = get_database_service(db)
    success = db_service.delete_session(session_id)
    if not success:
        raise HTTPException(status_code=404, detail="Session not found")
    return {"message": "Session deleted successfully"}

@router.get("/{session_id}/messages")
async def get_session_messages(
    session_id: int,
    db: Session = Depends(get_db)
):
    """
    Get all messages for a session
    """
    db_service = get_database_service(db)
    messages = db_service.get_messages(session_id)
    return messages
