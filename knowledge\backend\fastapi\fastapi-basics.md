---
title: "FastAPI快速入门指南"
description: "FastAPI框架的基础使用教程，包含API开发、数据验证、文档生成等核心功能"
category: backend
subcategory: fastapi
tags: [fastapi, python, api, tutorial, beginner]
difficulty: beginner
framework: fastapi
language: python
author: "技术团队"
created_date: "2024-01-15"
updated_date: "2024-01-15"
version: "1.0"
related_docs:
  - "fastapi-database.md"
  - "fastapi-authentication.md"
code_examples: true
external_links:
  - "https://fastapi.tiangolo.com/"
---

# FastAPI快速入门指南

## 概述

FastAPI是一个现代、快速的Python Web框架，用于构建API。它基于标准Python类型提示，具有自动API文档生成、数据验证、序列化等强大功能。

## 前置条件

- Python 3.7+
- 基本的Python编程知识
- 了解HTTP协议和RESTful API概念

## 安装和设置

### 安装FastAPI

```bash
# 安装FastAPI和ASGI服务器
pip install fastapi uvicorn

# 或者安装完整版本（包含所有可选依赖）
pip install "fastapi[all]"
```

### 创建第一个API

```python
# main.py
from fastapi import FastAPI

app = FastAPI()

@app.get("/")
def read_root():
    return {"Hello": "World"}

@app.get("/items/{item_id}")
def read_item(item_id: int, q: str = None):
    return {"item_id": item_id, "q": q}
```

### 运行应用

```bash
uvicorn main:app --reload
```

访问 http://127.0.0.1:8000 查看API响应
访问 http://127.0.0.1:8000/docs 查看自动生成的API文档

## 核心概念

### 路径操作

FastAPI使用装饰器定义路径操作：

```python
from fastapi import FastAPI

app = FastAPI()

# GET请求
@app.get("/users")
def get_users():
    return {"users": []}

# POST请求
@app.post("/users")
def create_user():
    return {"message": "User created"}

# PUT请求
@app.put("/users/{user_id}")
def update_user(user_id: int):
    return {"user_id": user_id, "message": "User updated"}

# DELETE请求
@app.delete("/users/{user_id}")
def delete_user(user_id: int):
    return {"user_id": user_id, "message": "User deleted"}
```

### 路径参数

```python
@app.get("/users/{user_id}")
def get_user(user_id: int):
    return {"user_id": user_id}

# 路径参数验证
from enum import Enum

class ModelName(str, Enum):
    alexnet = "alexnet"
    resnet = "resnet"
    lenet = "lenet"

@app.get("/models/{model_name}")
def get_model(model_name: ModelName):
    if model_name == ModelName.alexnet:
        return {"model_name": model_name, "message": "Deep Learning FTW!"}
    
    if model_name.value == "lenet":
        return {"model_name": model_name, "message": "LeCNN all the images"}
    
    return {"model_name": model_name, "message": "Have some residuals"}
```

### 查询参数

```python
fake_items_db = [{"item_name": "Foo"}, {"item_name": "Bar"}, {"item_name": "Baz"}]

@app.get("/items/")
def read_items(skip: int = 0, limit: int = 10):
    return fake_items_db[skip : skip + limit]

# 可选参数
@app.get("/items/{item_id}")
def read_item(item_id: str, q: str = None, short: bool = False):
    item = {"item_id": item_id}
    if q:
        item.update({"q": q})
    if not short:
        item.update(
            {"description": "This is an amazing item that has a long description"}
        )
    return item
```

## 数据模型和验证

### 使用Pydantic模型

```python
from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class User(BaseModel):
    id: Optional[int] = None
    name: str
    email: str
    age: int
    is_active: bool = True
    created_at: Optional[datetime] = None

class UserCreate(BaseModel):
    name: str
    email: str
    age: int

class UserResponse(BaseModel):
    id: int
    name: str
    email: str
    is_active: bool
    created_at: datetime

# 使用模型
@app.post("/users/", response_model=UserResponse)
def create_user(user: UserCreate):
    # 模拟创建用户
    new_user = User(
        id=1,
        name=user.name,
        email=user.email,
        age=user.age,
        created_at=datetime.now()
    )
    return new_user
```

### 数据验证

```python
from pydantic import BaseModel, validator, EmailStr
from typing import List

class User(BaseModel):
    name: str
    email: EmailStr
    age: int
    tags: List[str] = []

    @validator('age')
    def validate_age(cls, v):
        if v < 0 or v > 150:
            raise ValueError('Age must be between 0 and 150')
        return v

    @validator('name')
    def validate_name(cls, v):
        if len(v) < 2:
            raise ValueError('Name must be at least 2 characters long')
        return v.title()
```

## 请求和响应处理

### 请求体

```python
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel

class Item(BaseModel):
    name: str
    description: str = None
    price: float
    tax: float = None

@app.post("/items/")
def create_item(item: Item):
    item_dict = item.dict()
    if item.tax:
        price_with_tax = item.price + item.tax
        item_dict.update({"price_with_tax": price_with_tax})
    return item_dict

# 组合路径参数和请求体
@app.put("/items/{item_id}")
def update_item(item_id: int, item: Item, q: str = None):
    result = {"item_id": item_id, **item.dict()}
    if q:
        result.update({"q": q})
    return result
```

### 响应模型

```python
from typing import List

class ItemResponse(BaseModel):
    name: str
    price: float
    is_offer: bool = False

@app.get("/items/", response_model=List[ItemResponse])
def read_items():
    return [
        {"name": "Foo", "price": 50.2},
        {"name": "Bar", "price": 62, "is_offer": True},
    ]

# 排除未设置的字段
@app.get("/items/{item_id}", response_model=ItemResponse, response_model_exclude_unset=True)
def read_item(item_id: str):
    return items[item_id]
```

## 错误处理

```python
from fastapi import HTTPException

items = {"foo": "The Foo Wrestlers"}

@app.get("/items/{item_id}")
def read_item(item_id: str):
    if item_id not in items:
        raise HTTPException(status_code=404, detail="Item not found")
    return {"item": items[item_id]}

# 自定义异常处理器
from fastapi import Request
from fastapi.responses import JSONResponse

class UnicornException(Exception):
    def __init__(self, name: str):
        self.name = name

@app.exception_handler(UnicornException)
async def unicorn_exception_handler(request: Request, exc: UnicornException):
    return JSONResponse(
        status_code=418,
        content={"message": f"Oops! {exc.name} did something. There goes a rainbow..."},
    )

@app.get("/unicorns/{name}")
async def read_unicorn(name: str):
    if name == "yolo":
        raise UnicornException(name=name)
    return {"unicorn_name": name}
```

## 依赖注入

```python
from fastapi import Depends

# 简单依赖
def common_parameters(q: str = None, skip: int = 0, limit: int = 100):
    return {"q": q, "skip": skip, "limit": limit}

@app.get("/items/")
def read_items(commons: dict = Depends(common_parameters)):
    return commons

# 类作为依赖
class CommonQueryParams:
    def __init__(self, q: str = None, skip: int = 0, limit: int = 100):
        self.q = q
        self.skip = skip
        self.limit = limit

@app.get("/items/")
def read_items(commons: CommonQueryParams = Depends(CommonQueryParams)):
    response = {}
    if commons.q:
        response.update({"q": commons.q})
    items = fake_items_db[commons.skip : commons.skip + commons.limit]
    response.update({"items": items})
    return response
```

## 中间件

```python
import time
from fastapi import Request

@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response

# CORS中间件
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

## 文件上传

```python
from fastapi import File, UploadFile
from typing import List

@app.post("/uploadfile/")
async def create_upload_file(file: UploadFile = File(...)):
    return {"filename": file.filename}

@app.post("/uploadfiles/")
async def create_upload_files(files: List[UploadFile] = File(...)):
    return {"filenames": [file.filename for file in files]}

# 处理文件内容
@app.post("/files/")
async def create_file(file: bytes = File(...)):
    return {"file_size": len(file)}
```

## 后台任务

```python
from fastapi import BackgroundTasks

def write_notification(email: str, message=""):
    with open("log.txt", mode="w") as email_file:
        content = f"notification for {email}: {message}"
        email_file.write(content)

@app.post("/send-notification/{email}")
async def send_notification(email: str, background_tasks: BackgroundTasks):
    background_tasks.add_task(write_notification, email, message="some notification")
    return {"message": "Notification sent in the background"}
```

## 最佳实践

### 1. 项目结构
```
app/
├── __init__.py
├── main.py
├── dependencies.py
├── routers/
│   ├── __init__.py
│   ├── items.py
│   └── users.py
├── models/
│   ├── __init__.py
│   ├── item.py
│   └── user.py
└── schemas/
    ├── __init__.py
    ├── item.py
    └── user.py
```

### 2. 使用路由器
```python
# routers/users.py
from fastapi import APIRouter

router = APIRouter(
    prefix="/users",
    tags=["users"],
    responses={404: {"description": "Not found"}},
)

@router.get("/")
def read_users():
    return [{"username": "Rick"}, {"username": "Morty"}]

# main.py
from .routers import users

app.include_router(users.router)
```

### 3. 环境配置
```python
from pydantic import BaseSettings

class Settings(BaseSettings):
    app_name: str = "Awesome API"
    admin_email: str
    items_per_user: int = 50

    class Config:
        env_file = ".env"

settings = Settings()
```

## 常见问题

### Q1: 如何处理异步操作？
A: FastAPI原生支持async/await，可以直接使用异步函数。

### Q2: 如何集成数据库？
A: 可以使用SQLAlchemy、Tortoise ORM等，配合依赖注入使用。

### Q3: 如何部署FastAPI应用？
A: 可以使用Uvicorn、Gunicorn、Docker等方式部署。

## 相关资源

- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [Pydantic文档](https://pydantic-docs.helpmanual.io/)
- [Uvicorn文档](https://www.uvicorn.org/)

---

通过本指南，你应该已经掌握了FastAPI的基础使用方法。FastAPI的强大之处在于其简洁的语法和自动化功能，继续探索更高级的特性来构建强大的API应用！
