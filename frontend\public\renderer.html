<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-eval'; style-src 'self' 'unsafe-inline';">
    <title>Artifact Renderer</title>
    <!-- Vue 3 核心库 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- 本地依赖引用 -->
    <script type="module">
        import * as VueCompilerSFC from '/vendor/compiler-sfc.esm-browser.js';
        window.VueCompilerSFC = VueCompilerSFC;
    </script>
    <script src="/vendor/g2plot.min.js"></script>
    <script src="/vendor/mermaid.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }
        #app {
            width: 100%;
            height: 100%;
        }
        .error-message {
            color: #f5222d;
            padding: 16px;
            border: 1px solid #ffccc7;
            background-color: #fff2f0;
            border-radius: 4px;
            margin: 16px;
        }
    </style>
</head>
<body>
    <div id="app"></div>
    <script>
        // 全局变量，用于存储当前渲染的 artifact
        let currentArtifact = null;

        // 监听来自主应用的消息
        window.addEventListener('message', (event) => {
            // 验证来源
            if (event.origin !== window.location.origin) {
                console.error('Invalid message origin:', event.origin);
                return;
            }

            // 验证消息格式
            if (!event.data || !event.data.type) {
                console.error('Invalid message format:', event.data);
                return;
            }

            const { type, artifactType, code, id } = event.data;

            if (type === 'render') {
                // 清空容器
                document.getElementById('app').innerHTML = '';
                
                // 存储当前 artifact
                currentArtifact = {
                    type: artifactType,
                    code,
                    id
                };

                // 根据类型渲染
                switch (artifactType) {
                    case 'html':
                        renderHTML(code);
                        break;
                    case 'vue':
                        renderVue(code);
                        break;
                    case 'g2plot':
                        renderG2Plot(code);
                        break;
                    case 'mermaid':
                        renderMermaid(code);
                        break;
                    default:
                        showError(`不支持的 Artifact 类型: ${artifactType}`);
                }
            }
        });

        // 渲染 HTML
        function renderHTML(code) {
            try {
                document.getElementById('app').innerHTML = code;
                
                // 执行内联脚本
                const scripts = document.querySelectorAll('script');
                scripts.forEach(script => {
                    const newScript = document.createElement('script');
                    if (script.src) {
                        newScript.src = script.src;
                    } else {
                        newScript.textContent = script.textContent;
                    }
                    document.head.appendChild(newScript);
                });
                
                // 通知主应用渲染成功
                sendRenderStatus(true);
            } catch (error) {
                showError(`HTML 渲染错误: ${error.message}`);
                sendRenderStatus(false, error.message);
            }
        }

        // 渲染 Vue 单文件组件
        async function renderVue(sfc) {
            try {
                if (!window.VueCompilerSFC || !window.Vue) {
                    showError('Vue 编译器或 Vue 核心库未加载');
                    sendRenderStatus(false, 'Vue 编译器或 Vue 核心库未加载');
                    return;
                }

                // 解析 SFC
                const { parse, compileScript, compileTemplate, compileStyle } = window.VueCompilerSFC;
                const { descriptor, errors } = parse(sfc);

                if (errors.length > 0) {
                    throw new Error('SFC 解析错误: ' + errors.map(e => e.message).join(', '));
                }

                // 编译样式
                if (descriptor.styles && descriptor.styles.length > 0) {
                    descriptor.styles.forEach((style, index) => {
                        const styleEl = document.createElement('style');
                        if (style.scoped) {
                            // 简单的 scoped 样式处理
                            const scopedId = `data-v-${Math.random().toString(36).substr(2, 8)}`;
                            const scopedCss = style.content.replace(/([^{}]+){/g, (match, selector) => {
                                return selector.split(',').map(s => s.trim() + `[${scopedId}]`).join(',') + '{';
                            });
                            styleEl.textContent = scopedCss;
                            document.getElementById('app').setAttribute(scopedId, '');
                        } else {
                            styleEl.textContent = style.content;
                        }
                        document.head.appendChild(styleEl);
                    });
                }

                // 编译脚本
                let scriptContent = 'const _sfc_main = {};';
                if (descriptor.script || descriptor.scriptSetup) {
                    const script = descriptor.script || descriptor.scriptSetup;
                    if (descriptor.scriptSetup) {
                        // 处理 setup 语法
                        const compiled = compileScript(descriptor, { id: 'artifact' });
                        scriptContent = compiled.content;
                    } else {
                        scriptContent = script.content;
                    }
                }

                // 编译模板
                let renderFunction = 'function render() { return Vue.h("div", "Empty component"); }';
                if (descriptor.template) {
                    const compiled = compileTemplate({
                        source: descriptor.template.content,
                        id: 'artifact',
                        compilerOptions: {
                            mode: 'module'
                        }
                    });
                    if (compiled.errors.length > 0) {
                        throw new Error('模板编译错误: ' + compiled.errors.map(e => e.message).join(', '));
                    }
                    renderFunction = compiled.code;
                }

                // 创建组件
                const componentCode = `
                    ${scriptContent}
                    ${renderFunction}
                    if (typeof _sfc_main === 'undefined') {
                        window._sfc_main = {};
                    }
                    _sfc_main.render = render;
                    return _sfc_main;
                `;

                const component = new Function('Vue', componentCode)(window.Vue);

                // 创建 Vue 应用并挂载
                const app = window.Vue.createApp(component);
                app.mount('#app');

                // 通知主应用渲染成功
                sendRenderStatus(true);
            } catch (error) {
                console.error('Vue 渲染错误:', error);
                showError(`Vue 渲染错误: ${error.message}`);
                sendRenderStatus(false, error.message);
            }
        }

        // 渲染 G2Plot 图表
        function renderG2Plot(code) {
            try {
                if (!window.G2Plot) {
                    showError('G2Plot 未加载');
                    sendRenderStatus(false, 'G2Plot 未加载');
                    return;
                }

                // 创建容器
                const container = document.createElement('div');
                container.id = 'chart-container';
                container.style.width = '100%';
                container.style.height = '400px';
                container.style.padding = '20px';
                document.getElementById('app').appendChild(container);

                // 解析配置
                let config;
                try {
                    config = JSON.parse(code);
                } catch (parseError) {
                    throw new Error('JSON 配置解析失败: ' + parseError.message);
                }

                // 创建图表
                const chartType = config.type || 'Line';
                const ChartClass = window.G2Plot[chartType];

                if (!ChartClass) {
                    // 尝试常见的图表类型映射
                    const typeMapping = {
                        'line': 'Line',
                        'bar': 'Column',
                        'column': 'Column',
                        'pie': 'Pie',
                        'area': 'Area',
                        'scatter': 'Scatter'
                    };
                    const mappedType = typeMapping[chartType.toLowerCase()];
                    if (mappedType && window.G2Plot[mappedType]) {
                        config.type = mappedType;
                        const chart = new window.G2Plot[mappedType]('chart-container', config);
                        chart.render();
                    } else {
                        showError(`未知的图表类型: ${chartType}。支持的类型: Line, Column, Pie, Area, Scatter 等`);
                        sendRenderStatus(false, `未知的图表类型: ${chartType}`);
                        return;
                    }
                } else {
                    const chart = new ChartClass('chart-container', config);
                    chart.render();
                }

                // 通知主应用渲染成功
                sendRenderStatus(true);
            } catch (error) {
                console.error('G2Plot 渲染错误:', error);
                showError(`G2Plot 渲染错误: ${error.message}`);
                sendRenderStatus(false, error.message);
            }
        }

        // 渲染 Mermaid 图表
        async function renderMermaid(code) {
            try {
                if (!window.mermaid) {
                    showError('Mermaid 未加载');
                    sendRenderStatus(false, 'Mermaid 未加载');
                    return;
                }

                // 创建容器
                const container = document.createElement('div');
                container.id = 'mermaid-container';
                container.style.width = '100%';
                container.style.height = 'auto';
                container.style.padding = '20px';
                container.style.textAlign = 'center';
                document.getElementById('app').appendChild(container);

                // 初始化 Mermaid
                window.mermaid.initialize({
                    startOnLoad: false,
                    theme: 'default',
                    securityLevel: 'loose',
                    fontFamily: 'Arial, sans-serif'
                });

                // 渲染图表
                try {
                    const { svg } = await window.mermaid.render('mermaid-graph', code);
                    document.getElementById('mermaid-container').innerHTML = svg;

                    // 通知主应用渲染成功
                    sendRenderStatus(true);
                } catch (renderError) {
                    // 如果新版本 API 失败，尝试旧版本 API
                    window.mermaid.render('mermaid-graph', code, (svgCode) => {
                        document.getElementById('mermaid-container').innerHTML = svgCode;
                        sendRenderStatus(true);
                    }, (error) => {
                        throw error;
                    });
                }
            } catch (error) {
                console.error('Mermaid 渲染错误:', error);
                showError(`Mermaid 渲染错误: ${error.message}`);
                sendRenderStatus(false, error.message);
            }
        }

        // 显示错误信息
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = message;
            document.getElementById('app').appendChild(errorDiv);
            console.error(message);
        }

        // 向主应用发送渲染状态
        function sendRenderStatus(success, errorMessage = '') {
            window.parent.postMessage({
                type: 'renderStatus',
                success,
                errorMessage,
                artifactId: currentArtifact?.id
            }, '*');
        }

        // 通知主应用渲染器已准备好
        window.parent.postMessage({ type: 'rendererReady' }, '*');
    </script>
</body>
</html>
