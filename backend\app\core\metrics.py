"""
性能监控和指标收集系统

提供系统性能监控、指标收集和分析功能。
"""
import time
import asyncio
import psutil
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
from datetime import datetime, timedelta
import json
import logging

logger = logging.getLogger(__name__)

@dataclass
class MetricPoint:
    """指标数据点"""
    name: str
    value: float
    timestamp: float
    tags: Dict[str, str] = field(default_factory=dict)
    unit: str = ""

@dataclass
class PerformanceStats:
    """性能统计信息"""
    avg: float
    min: float
    max: float
    p50: float
    p95: float
    p99: float
    count: int
    total: float

class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, max_points: int = 10000):
        self.max_points = max_points
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_points))
        self.counters: Dict[str, float] = defaultdict(float)
        self.gauges: Dict[str, float] = defaultdict(float)
        self.histograms: Dict[str, List[float]] = defaultdict(list)
        
    def record_counter(self, name: str, value: float = 1.0, tags: Dict[str, str] = None):
        """记录计数器指标"""
        key = self._make_key(name, tags)
        self.counters[key] += value
        
        point = MetricPoint(
            name=name,
            value=self.counters[key],
            timestamp=time.time(),
            tags=tags or {},
            unit="count"
        )
        self.metrics[key].append(point)
    
    def record_gauge(self, name: str, value: float, tags: Dict[str, str] = None):
        """记录仪表盘指标"""
        key = self._make_key(name, tags)
        self.gauges[key] = value
        
        point = MetricPoint(
            name=name,
            value=value,
            timestamp=time.time(),
            tags=tags or {},
            unit="gauge"
        )
        self.metrics[key].append(point)
    
    def record_histogram(self, name: str, value: float, tags: Dict[str, str] = None):
        """记录直方图指标"""
        key = self._make_key(name, tags)
        self.histograms[key].append(value)
        
        # 保持最近1000个值
        if len(self.histograms[key]) > 1000:
            self.histograms[key] = self.histograms[key][-1000:]
        
        point = MetricPoint(
            name=name,
            value=value,
            timestamp=time.time(),
            tags=tags or {},
            unit="histogram"
        )
        self.metrics[key].append(point)
    
    def record_timing(self, name: str, duration_ms: float, tags: Dict[str, str] = None):
        """记录时间指标"""
        self.record_histogram(f"{name}_duration", duration_ms, tags)
    
    def get_stats(self, name: str, tags: Dict[str, str] = None) -> Optional[PerformanceStats]:
        """获取性能统计"""
        key = self._make_key(name, tags)
        
        if key in self.histograms and self.histograms[key]:
            values = sorted(self.histograms[key])
            count = len(values)
            
            return PerformanceStats(
                avg=sum(values) / count,
                min=values[0],
                max=values[-1],
                p50=values[int(count * 0.5)],
                p95=values[int(count * 0.95)],
                p99=values[int(count * 0.99)],
                count=count,
                total=sum(values)
            )
        
        return None
    
    def get_counter(self, name: str, tags: Dict[str, str] = None) -> float:
        """获取计数器值"""
        key = self._make_key(name, tags)
        return self.counters.get(key, 0.0)
    
    def get_gauge(self, name: str, tags: Dict[str, str] = None) -> Optional[float]:
        """获取仪表盘值"""
        key = self._make_key(name, tags)
        return self.gauges.get(key)
    
    def get_recent_metrics(self, name: str, minutes: int = 5) -> List[MetricPoint]:
        """获取最近的指标数据"""
        cutoff_time = time.time() - (minutes * 60)
        all_metrics = []
        
        for key, points in self.metrics.items():
            if name in key:
                recent_points = [p for p in points if p.timestamp >= cutoff_time]
                all_metrics.extend(recent_points)
        
        return sorted(all_metrics, key=lambda x: x.timestamp)
    
    def _make_key(self, name: str, tags: Dict[str, str] = None) -> str:
        """生成指标键"""
        if not tags:
            return name
        
        tag_str = ",".join(f"{k}={v}" for k, v in sorted(tags.items()))
        return f"{name}[{tag_str}]"

class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
        self.monitoring = False
        self.monitor_task = None
    
    async def start_monitoring(self, interval: float = 30.0):
        """开始系统监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop(interval))
        logger.info("System monitoring started")
    
    async def stop_monitoring(self):
        """停止系统监控"""
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("System monitoring stopped")
    
    async def _monitor_loop(self, interval: float):
        """监控循环"""
        while self.monitoring:
            try:
                await self._collect_system_metrics()
                await asyncio.sleep(interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(interval)
    
    async def _collect_system_metrics(self):
        """收集系统指标"""
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        self.metrics.record_gauge("system_cpu_percent", cpu_percent)
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        self.metrics.record_gauge("system_memory_percent", memory.percent)
        self.metrics.record_gauge("system_memory_used_bytes", memory.used)
        self.metrics.record_gauge("system_memory_available_bytes", memory.available)
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        self.metrics.record_gauge("system_disk_percent", (disk.used / disk.total) * 100)
        self.metrics.record_gauge("system_disk_used_bytes", disk.used)
        self.metrics.record_gauge("system_disk_free_bytes", disk.free)
        
        # 网络IO
        net_io = psutil.net_io_counters()
        self.metrics.record_gauge("system_network_bytes_sent", net_io.bytes_sent)
        self.metrics.record_gauge("system_network_bytes_recv", net_io.bytes_recv)

class PerformanceTracker:
    """性能跟踪器"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
    
    def track_function_performance(self, name: str = None, tags: Dict[str, str] = None):
        """函数性能跟踪装饰器"""
        def decorator(func: Callable):
            metric_name = name or f"function_{func.__name__}"
            
            if asyncio.iscoroutinefunction(func):
                async def async_wrapper(*args, **kwargs):
                    start_time = time.time()
                    try:
                        result = await func(*args, **kwargs)
                        self.metrics.record_counter(f"{metric_name}_success", tags=tags)
                        return result
                    except Exception as e:
                        self.metrics.record_counter(f"{metric_name}_error", tags=tags)
                        raise
                    finally:
                        duration = (time.time() - start_time) * 1000
                        self.metrics.record_timing(metric_name, duration, tags)
                
                return async_wrapper
            else:
                def sync_wrapper(*args, **kwargs):
                    start_time = time.time()
                    try:
                        result = func(*args, **kwargs)
                        self.metrics.record_counter(f"{metric_name}_success", tags=tags)
                        return result
                    except Exception as e:
                        self.metrics.record_counter(f"{metric_name}_error", tags=tags)
                        raise
                    finally:
                        duration = (time.time() - start_time) * 1000
                        self.metrics.record_timing(metric_name, duration, tags)
                
                return sync_wrapper
        
        return decorator
    
    def track_api_performance(self, endpoint: str):
        """API性能跟踪装饰器"""
        def decorator(func: Callable):
            async def wrapper(*args, **kwargs):
                start_time = time.time()
                status_code = 200
                
                try:
                    result = await func(*args, **kwargs)
                    return result
                except Exception as e:
                    status_code = getattr(e, 'status_code', 500)
                    raise
                finally:
                    duration = (time.time() - start_time) * 1000
                    tags = {
                        "endpoint": endpoint,
                        "status_code": str(status_code)
                    }
                    
                    self.metrics.record_timing("api_request", duration, tags)
                    self.metrics.record_counter("api_requests_total", tags=tags)
                    
                    if status_code >= 400:
                        self.metrics.record_counter("api_errors_total", tags=tags)
            
            return wrapper
        return decorator

class MetricsExporter:
    """指标导出器"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
    
    def export_prometheus_format(self) -> str:
        """导出Prometheus格式指标"""
        lines = []
        
        # 导出计数器
        for key, value in self.metrics.counters.items():
            metric_name, tags = self._parse_key(key)
            lines.append(f"# TYPE {metric_name} counter")
            if tags:
                tag_str = ",".join(f'{k}="{v}"' for k, v in tags.items())
                lines.append(f"{metric_name}{{{tag_str}}} {value}")
            else:
                lines.append(f"{metric_name} {value}")
        
        # 导出仪表盘
        for key, value in self.metrics.gauges.items():
            metric_name, tags = self._parse_key(key)
            lines.append(f"# TYPE {metric_name} gauge")
            if tags:
                tag_str = ",".join(f'{k}="{v}"' for k, v in tags.items())
                lines.append(f"{metric_name}{{{tag_str}}} {value}")
            else:
                lines.append(f"{metric_name} {value}")
        
        return "\n".join(lines)
    
    def export_json_format(self) -> str:
        """导出JSON格式指标"""
        data = {
            "timestamp": time.time(),
            "counters": dict(self.metrics.counters),
            "gauges": dict(self.metrics.gauges),
            "histograms": {}
        }
        
        # 导出直方图统计
        for key, values in self.metrics.histograms.items():
            if values:
                metric_name, tags = self._parse_key(key)
                stats = self.metrics.get_stats(metric_name, tags)
                if stats:
                    data["histograms"][key] = {
                        "avg": stats.avg,
                        "min": stats.min,
                        "max": stats.max,
                        "p50": stats.p50,
                        "p95": stats.p95,
                        "p99": stats.p99,
                        "count": stats.count
                    }
        
        return json.dumps(data, indent=2)
    
    def _parse_key(self, key: str) -> tuple:
        """解析指标键"""
        if '[' in key and ']' in key:
            name = key.split('[')[0]
            tag_str = key.split('[')[1].split(']')[0]
            tags = {}
            for tag in tag_str.split(','):
                if '=' in tag:
                    k, v = tag.split('=', 1)
                    tags[k] = v
            return name, tags
        else:
            return key, {}

# 全局指标收集器
metrics_collector = MetricsCollector()
system_monitor = SystemMonitor(metrics_collector)
performance_tracker = PerformanceTracker(metrics_collector)
metrics_exporter = MetricsExporter(metrics_collector)

# 便捷函数
def record_counter(name: str, value: float = 1.0, tags: Dict[str, str] = None):
    """记录计数器指标"""
    metrics_collector.record_counter(name, value, tags)

def record_gauge(name: str, value: float, tags: Dict[str, str] = None):
    """记录仪表盘指标"""
    metrics_collector.record_gauge(name, value, tags)

def record_timing(name: str, duration_ms: float, tags: Dict[str, str] = None):
    """记录时间指标"""
    metrics_collector.record_timing(name, duration_ms, tags)

def track_performance(name: str = None, tags: Dict[str, str] = None):
    """性能跟踪装饰器"""
    return performance_tracker.track_function_performance(name, tags)

def track_api(endpoint: str):
    """API性能跟踪装饰器"""
    return performance_tracker.track_api_performance(endpoint)
