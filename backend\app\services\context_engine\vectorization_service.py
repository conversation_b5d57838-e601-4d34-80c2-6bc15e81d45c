"""
向量化服务

实现文档内容的向量化处理和存储功能。
"""
import asyncio
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import logging
from sentence_transformers import SentenceTransformer
import torch

from app.services.context_engine.document_processor import ProcessedDocument, DocumentChunk

logger = logging.getLogger(__name__)

@dataclass
class VectorizedChunk:
    """向量化的文档分块"""
    chunk_id: str
    content: str
    title: str
    embedding: np.ndarray
    metadata: Dict[str, Any]
    document_id: str

@dataclass
class VectorizedDocument:
    """向量化的文档"""
    document_id: str
    title: str
    summary_embedding: np.ndarray
    chunks: List[VectorizedChunk]
    metadata: Dict[str, Any]

class EmbeddingService:
    """嵌入向量生成服务"""
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        self.model_name = model_name
        self.model = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.embedding_dimension = 384  # all-MiniLM-L6-v2的维度
        
        # 初始化模型
        self._load_model()
    
    def _load_model(self):
        """加载嵌入模型"""
        try:
            logger.info(f"Loading embedding model: {self.model_name}")
            self.model = SentenceTransformer(self.model_name, device=self.device)
            logger.info(f"Model loaded successfully on {self.device}")
        except Exception as e:
            logger.error(f"Failed to load embedding model: {e}")
            # 使用备用模型
            try:
                self.model_name = "paraphrase-MiniLM-L6-v2"
                self.model = SentenceTransformer(self.model_name, device=self.device)
                logger.info(f"Loaded backup model: {self.model_name}")
            except Exception as backup_e:
                logger.error(f"Failed to load backup model: {backup_e}")
                raise
    
    async def embed_text(self, text: str) -> np.ndarray:
        """生成单个文本的嵌入向量"""
        try:
            # 预处理文本
            processed_text = self._preprocess_text(text)
            
            # 生成嵌入向量
            embedding = self.model.encode(processed_text, convert_to_numpy=True)
            
            return embedding
        except Exception as e:
            logger.error(f"Failed to embed text: {e}")
            # 返回零向量作为fallback
            return np.zeros(self.embedding_dimension)
    
    async def embed_batch(self, texts: List[str], batch_size: int = 32) -> List[np.ndarray]:
        """批量生成嵌入向量"""
        try:
            # 预处理文本
            processed_texts = [self._preprocess_text(text) for text in texts]
            
            embeddings = []
            
            # 分批处理
            for i in range(0, len(processed_texts), batch_size):
                batch = processed_texts[i:i + batch_size]
                
                # 生成嵌入向量
                batch_embeddings = self.model.encode(
                    batch, 
                    convert_to_numpy=True,
                    show_progress_bar=False
                )
                
                embeddings.extend(batch_embeddings)
                
                # 避免内存溢出
                if i % (batch_size * 4) == 0:
                    await asyncio.sleep(0.01)
            
            return embeddings
        except Exception as e:
            logger.error(f"Failed to embed batch: {e}")
            # 返回零向量作为fallback
            return [np.zeros(self.embedding_dimension) for _ in texts]
    
    def _preprocess_text(self, text: str) -> str:
        """预处理文本"""
        # 移除过多的空白字符
        text = ' '.join(text.split())
        
        # 限制长度（模型通常有最大token限制）
        max_length = 512  # 大多数模型的最大长度
        if len(text) > max_length:
            text = text[:max_length]
        
        return text
    
    def calculate_similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """计算两个向量的余弦相似度"""
        try:
            # 计算余弦相似度
            dot_product = np.dot(embedding1, embedding2)
            norm1 = np.linalg.norm(embedding1)
            norm2 = np.linalg.norm(embedding2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
        except Exception as e:
            logger.error(f"Failed to calculate similarity: {e}")
            return 0.0

class VectorizationService:
    """向量化服务主类"""
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        self.embedding_service = EmbeddingService(model_name)
        
    async def vectorize_document(self, document: ProcessedDocument) -> VectorizedDocument:
        """向量化整个文档"""
        try:
            logger.info(f"Vectorizing document: {document.title}")
            
            # 向量化文档摘要
            summary_embedding = await self.embedding_service.embed_text(
                f"{document.title}. {document.summary}"
            )
            
            # 向量化所有分块
            vectorized_chunks = await self._vectorize_chunks(document.chunks, document.id)
            
            return VectorizedDocument(
                document_id=document.id,
                title=document.title,
                summary_embedding=summary_embedding,
                chunks=vectorized_chunks,
                metadata=document.metadata
            )
            
        except Exception as e:
            logger.error(f"Failed to vectorize document {document.id}: {e}")
            raise
    
    async def _vectorize_chunks(self, chunks: List[DocumentChunk], document_id: str) -> List[VectorizedChunk]:
        """向量化文档分块"""
        try:
            # 准备文本列表
            texts = []
            for chunk in chunks:
                # 组合标题和内容
                combined_text = f"{chunk.title}. {chunk.content}"
                texts.append(combined_text)
            
            # 批量生成嵌入向量
            embeddings = await self.embedding_service.embed_batch(texts)
            
            # 创建向量化分块
            vectorized_chunks = []
            for chunk, embedding in zip(chunks, embeddings):
                vectorized_chunk = VectorizedChunk(
                    chunk_id=chunk.id,
                    content=chunk.content,
                    title=chunk.title,
                    embedding=embedding,
                    metadata=chunk.metadata,
                    document_id=document_id
                )
                vectorized_chunks.append(vectorized_chunk)
            
            logger.info(f"Vectorized {len(vectorized_chunks)} chunks for document {document_id}")
            return vectorized_chunks
            
        except Exception as e:
            logger.error(f"Failed to vectorize chunks: {e}")
            raise
    
    async def vectorize_query(self, query: str) -> np.ndarray:
        """向量化查询文本"""
        return await self.embedding_service.embed_text(query)
    
    async def find_similar_chunks(
        self, 
        query_embedding: np.ndarray, 
        vectorized_chunks: List[VectorizedChunk],
        top_k: int = 10,
        similarity_threshold: float = 0.5
    ) -> List[Tuple[VectorizedChunk, float]]:
        """查找相似的文档分块"""
        try:
            similarities = []
            
            for chunk in vectorized_chunks:
                similarity = self.embedding_service.calculate_similarity(
                    query_embedding, chunk.embedding
                )
                
                if similarity >= similarity_threshold:
                    similarities.append((chunk, similarity))
            
            # 按相似度排序
            similarities.sort(key=lambda x: x[1], reverse=True)
            
            return similarities[:top_k]
            
        except Exception as e:
            logger.error(f"Failed to find similar chunks: {e}")
            return []
    
    async def update_chunk_embedding(self, chunk: DocumentChunk, document_id: str) -> VectorizedChunk:
        """更新单个分块的嵌入向量"""
        try:
            combined_text = f"{chunk.title}. {chunk.content}"
            embedding = await self.embedding_service.embed_text(combined_text)
            
            return VectorizedChunk(
                chunk_id=chunk.id,
                content=chunk.content,
                title=chunk.title,
                embedding=embedding,
                metadata=chunk.metadata,
                document_id=document_id
            )
            
        except Exception as e:
            logger.error(f"Failed to update chunk embedding: {e}")
            raise

class VectorStore:
    """向量存储管理器"""
    
    def __init__(self):
        self.vectors: Dict[str, VectorizedDocument] = {}
        self.chunk_index: Dict[str, VectorizedChunk] = {}
    
    def add_document(self, vectorized_doc: VectorizedDocument):
        """添加向量化文档"""
        self.vectors[vectorized_doc.document_id] = vectorized_doc
        
        # 更新分块索引
        for chunk in vectorized_doc.chunks:
            self.chunk_index[chunk.chunk_id] = chunk
        
        logger.info(f"Added document to vector store: {vectorized_doc.title}")
    
    def remove_document(self, document_id: str):
        """移除文档"""
        if document_id in self.vectors:
            doc = self.vectors[document_id]
            
            # 移除分块索引
            for chunk in doc.chunks:
                if chunk.chunk_id in self.chunk_index:
                    del self.chunk_index[chunk.chunk_id]
            
            # 移除文档
            del self.vectors[document_id]
            
            logger.info(f"Removed document from vector store: {document_id}")
    
    def get_all_chunks(self) -> List[VectorizedChunk]:
        """获取所有分块"""
        return list(self.chunk_index.values())
    
    def get_document_chunks(self, document_id: str) -> List[VectorizedChunk]:
        """获取指定文档的分块"""
        if document_id in self.vectors:
            return self.vectors[document_id].chunks
        return []
    
    def search_by_metadata(self, filters: Dict[str, Any]) -> List[VectorizedChunk]:
        """根据元数据筛选分块"""
        filtered_chunks = []
        
        for chunk in self.chunk_index.values():
            match = True
            for key, value in filters.items():
                if key not in chunk.metadata or chunk.metadata[key] != value:
                    match = False
                    break
            
            if match:
                filtered_chunks.append(chunk)
        
        return filtered_chunks
    
    def get_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        return {
            "total_documents": len(self.vectors),
            "total_chunks": len(self.chunk_index),
            "avg_chunks_per_doc": len(self.chunk_index) / max(len(self.vectors), 1)
        }

# 全局向量化服务实例
vectorization_service = VectorizationService()
vector_store = VectorStore()
