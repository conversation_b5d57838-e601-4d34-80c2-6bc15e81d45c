"""
Dynamic Prompt Optimizer

Optimizes prompts based on query patterns, user feedback, and performance metrics.
"""
import asyncio
import json
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import statistics

from app.services.context_engine.prompt_builder import QueryType, IntelligentPromptBuilder
from app.services.context_engine.retrieval_engine import SearchResult
import logging

logger = logging.getLogger(__name__)

@dataclass
class PromptPerformance:
    """Prompt performance metrics"""
    query_type: str
    template_name: str
    response_quality: float  # 0-1 score
    response_time_ms: int
    context_relevance: float  # 0-1 score
    user_satisfaction: Optional[float] = None  # 0-1 score from user feedback
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()

@dataclass
class OptimizationRule:
    """Dynamic optimization rule"""
    name: str
    condition: str  # Python expression to evaluate
    action: str  # Action to take
    priority: int = 1
    enabled: bool = True

class PerformanceTracker:
    """Track prompt performance metrics"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.performance_history: List[PromptPerformance] = []
        self.query_patterns = defaultdict(list)
        
    def record_performance(self, performance: PromptPerformance):
        """Record prompt performance"""
        self.performance_history.append(performance)
        self.query_patterns[performance.query_type].append(performance)
        
        # Maintain history size
        if len(self.performance_history) > self.max_history:
            self.performance_history = self.performance_history[-self.max_history:]
        
        logger.info(
            "Recorded prompt performance",
            query_type=performance.query_type,
            quality=performance.response_quality,
            response_time=performance.response_time_ms
        )
    
    def get_performance_stats(self, query_type: Optional[str] = None, days: int = 7) -> Dict[str, Any]:
        """Get performance statistics"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Filter by date and query type
        filtered_history = [
            p for p in self.performance_history
            if p.timestamp >= cutoff_date and (query_type is None or p.query_type == query_type)
        ]
        
        if not filtered_history:
            return {}
        
        # Calculate statistics
        qualities = [p.response_quality for p in filtered_history]
        response_times = [p.response_time_ms for p in filtered_history]
        relevances = [p.context_relevance for p in filtered_history]
        
        stats = {
            "total_queries": len(filtered_history),
            "avg_quality": statistics.mean(qualities),
            "avg_response_time": statistics.mean(response_times),
            "avg_relevance": statistics.mean(relevances),
            "quality_trend": self._calculate_trend([p.response_quality for p in filtered_history[-10:]]),
            "query_type_distribution": dict(Counter(p.query_type for p in filtered_history))
        }
        
        # Add user satisfaction if available
        satisfactions = [p.user_satisfaction for p in filtered_history if p.user_satisfaction is not None]
        if satisfactions:
            stats["avg_satisfaction"] = statistics.mean(satisfactions)
        
        return stats
    
    def _calculate_trend(self, values: List[float]) -> str:
        """Calculate trend direction"""
        if len(values) < 3:
            return "insufficient_data"
        
        # Simple linear trend
        x = list(range(len(values)))
        n = len(values)
        
        sum_x = sum(x)
        sum_y = sum(values)
        sum_xy = sum(x[i] * values[i] for i in range(n))
        sum_x2 = sum(x[i] ** 2 for i in range(n))
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x ** 2)
        
        if slope > 0.01:
            return "improving"
        elif slope < -0.01:
            return "declining"
        else:
            return "stable"

class DynamicPromptOptimizer:
    """Optimize prompts dynamically based on performance"""
    
    def __init__(self):
        self.prompt_builder = IntelligentPromptBuilder()
        self.performance_tracker = PerformanceTracker()
        
        # Optimization rules
        self.optimization_rules = self._load_optimization_rules()
        
        # Dynamic adjustments
        self.template_adjustments = defaultdict(dict)
        
        logger.info("Initialized DynamicPromptOptimizer")
    
    def _load_optimization_rules(self) -> List[OptimizationRule]:
        """Load optimization rules"""
        return [
            OptimizationRule(
                name="reduce_context_for_slow_queries",
                condition="avg_response_time > 5000 and context_count > 5",
                action="reduce_context_length",
                priority=1
            ),
            OptimizationRule(
                name="increase_context_for_low_quality",
                condition="avg_quality < 0.6 and context_count < 3",
                action="increase_context_length",
                priority=2
            ),
            OptimizationRule(
                name="adjust_template_for_debugging",
                condition="query_type == 'debugging' and avg_satisfaction < 0.7",
                action="use_detailed_debugging_template",
                priority=1
            ),
            OptimizationRule(
                name="simplify_for_learning_queries",
                condition="query_type == 'learning' and avg_quality < 0.7",
                action="use_simplified_language",
                priority=2
            )
        ]
    
    async def optimize_prompt(
        self,
        user_query: str,
        contexts: List[SearchResult],
        conversation_history: Optional[List[Dict[str, str]]] = None,
        user_preferences: Optional[Dict[str, Any]] = None
    ) -> Tuple[str, Dict[str, Any]]:
        """Build optimized prompt with dynamic adjustments"""
        try:
            # Analyze query type
            query_type, confidence = self.prompt_builder.query_analyzer.analyze_query(user_query)
            
            # Get performance stats for this query type
            stats = self.performance_tracker.get_performance_stats(query_type.value)
            
            # Apply dynamic optimizations
            optimized_contexts = await self._optimize_contexts(contexts, query_type, stats)
            
            # Build base prompt
            system_prompt, metadata = await self.prompt_builder.build_prompt(
                user_query, optimized_contexts, conversation_history
            )
            
            # Apply template adjustments
            adjusted_prompt = self._apply_template_adjustments(
                system_prompt, query_type, stats, user_preferences
            )
            
            # Update metadata
            metadata.update({
                "optimization_applied": True,
                "performance_stats": stats,
                "adjustments_made": self.template_adjustments.get(query_type.value, {})
            })
            
            logger.info(
                "Optimized prompt",
                query_type=query_type.value,
                original_contexts=len(contexts),
                optimized_contexts=len(optimized_contexts),
                adjustments=len(self.template_adjustments.get(query_type.value, {}))
            )
            
            return adjusted_prompt, metadata
            
        except Exception as e:
            logger.error("Prompt optimization failed", error=str(e))
            # Fallback to basic prompt building
            return await self.prompt_builder.build_prompt(user_query, contexts, conversation_history)
    
    async def _optimize_contexts(
        self,
        contexts: List[SearchResult],
        query_type: QueryType,
        stats: Dict[str, Any]
    ) -> List[SearchResult]:
        """Optimize context selection based on performance stats"""
        if not stats:
            return contexts
        
        optimized_contexts = contexts.copy()
        
        # Apply optimization rules
        for rule in self.optimization_rules:
            if not rule.enabled:
                continue
            
            # Evaluate condition
            try:
                condition_vars = {
                    "avg_response_time": stats.get("avg_response_time", 0),
                    "avg_quality": stats.get("avg_quality", 0.5),
                    "avg_satisfaction": stats.get("avg_satisfaction", 0.5),
                    "context_count": len(optimized_contexts),
                    "query_type": query_type.value
                }
                
                if eval(rule.condition, {"__builtins__": {}}, condition_vars):
                    optimized_contexts = self._apply_optimization_action(
                        optimized_contexts, rule.action, query_type
                    )
                    
                    logger.info(
                        "Applied optimization rule",
                        rule=rule.name,
                        action=rule.action,
                        query_type=query_type.value
                    )
                    
            except Exception as e:
                logger.warning("Failed to evaluate optimization rule", rule=rule.name, error=str(e))
        
        return optimized_contexts
    
    def _apply_optimization_action(
        self,
        contexts: List[SearchResult],
        action: str,
        query_type: QueryType
    ) -> List[SearchResult]:
        """Apply optimization action to contexts"""
        
        if action == "reduce_context_length":
            # Keep only top 3 contexts
            return contexts[:3]
        
        elif action == "increase_context_length":
            # Already have all available contexts
            return contexts
        
        elif action == "prioritize_code_examples":
            # Move code examples to the front
            code_contexts = [ctx for ctx in contexts if ctx.source_type == 'code']
            doc_contexts = [ctx for ctx in contexts if ctx.source_type != 'code']
            return code_contexts + doc_contexts
        
        elif action == "prioritize_documentation":
            # Move documentation to the front
            doc_contexts = [ctx for ctx in contexts if ctx.source_type != 'code']
            code_contexts = [ctx for ctx in contexts if ctx.source_type == 'code']
            return doc_contexts + code_contexts
        
        else:
            return contexts
    
    def _apply_template_adjustments(
        self,
        system_prompt: str,
        query_type: QueryType,
        stats: Dict[str, Any],
        user_preferences: Optional[Dict[str, Any]] = None
    ) -> str:
        """Apply dynamic template adjustments"""
        
        adjusted_prompt = system_prompt
        adjustments = self.template_adjustments.get(query_type.value, {})
        
        # Apply user preferences
        if user_preferences:
            if user_preferences.get("detailed_explanations", False):
                adjusted_prompt += "\n\n请提供详细的解释和步骤说明。"
                adjustments["detailed_explanations"] = True
            
            if user_preferences.get("prefer_code_examples", False):
                adjusted_prompt += "\n\n请优先提供代码示例来说明概念。"
                adjustments["prefer_code_examples"] = True
            
            if user_preferences.get("beginner_friendly", False):
                adjusted_prompt += "\n\n请使用简单易懂的语言，适合初学者理解。"
                adjustments["beginner_friendly"] = True
        
        # Apply performance-based adjustments
        if stats:
            avg_quality = stats.get("avg_quality", 0.5)
            avg_satisfaction = stats.get("avg_satisfaction", 0.5)
            
            if avg_quality < 0.6:
                if query_type == QueryType.DEBUGGING:
                    adjusted_prompt += "\n\n请特别注意提供具体的调试步骤和错误排查方法。"
                    adjustments["enhanced_debugging"] = True
                elif query_type == QueryType.LEARNING:
                    adjusted_prompt += "\n\n请提供更多的背景知识和基础概念解释。"
                    adjustments["enhanced_learning"] = True
            
            if avg_satisfaction and avg_satisfaction < 0.7:
                adjusted_prompt += "\n\n请确保回答完整、准确，并提供可验证的解决方案。"
                adjustments["enhanced_completeness"] = True
        
        # Store adjustments for tracking
        if adjustments:
            self.template_adjustments[query_type.value] = adjustments
        
        return adjusted_prompt
    
    async def record_feedback(
        self,
        query: str,
        response: str,
        contexts_used: List[SearchResult],
        response_time_ms: int,
        user_feedback: Optional[Dict[str, Any]] = None
    ):
        """Record user feedback for prompt optimization"""
        try:
            # Analyze query type
            query_type, _ = self.prompt_builder.query_analyzer.analyze_query(query)
            
            # Calculate metrics
            response_quality = self._estimate_response_quality(response, contexts_used)
            context_relevance = self._calculate_context_relevance(query, contexts_used)
            
            # Extract user satisfaction from feedback
            user_satisfaction = None
            if user_feedback:
                user_satisfaction = user_feedback.get("satisfaction_score")
            
            # Create performance record
            performance = PromptPerformance(
                query_type=query_type.value,
                template_name=self.prompt_builder.templates[query_type].name,
                response_quality=response_quality,
                response_time_ms=response_time_ms,
                context_relevance=context_relevance,
                user_satisfaction=user_satisfaction
            )
            
            # Record performance
            self.performance_tracker.record_performance(performance)
            
            logger.info(
                "Recorded prompt feedback",
                query_type=query_type.value,
                quality=response_quality,
                satisfaction=user_satisfaction
            )
            
        except Exception as e:
            logger.error("Failed to record feedback", error=str(e))
    
    def _estimate_response_quality(self, response: str, contexts: List[SearchResult]) -> float:
        """Estimate response quality based on heuristics"""
        quality_score = 0.5  # Base score
        
        # Length check
        if 100 <= len(response) <= 2000:
            quality_score += 0.1
        
        # Code example check
        if "```" in response:
            quality_score += 0.1
        
        # Structure check
        if any(marker in response for marker in ["##", "**", "1.", "2.", "3."]):
            quality_score += 0.1
        
        # Context utilization check
        context_keywords = set()
        for ctx in contexts:
            # Extract key terms from context
            words = ctx.content.lower().split()
            context_keywords.update(words[:10])  # Top 10 words
        
        response_words = set(response.lower().split())
        overlap = len(context_keywords.intersection(response_words))
        
        if overlap > 5:
            quality_score += 0.2
        elif overlap > 2:
            quality_score += 0.1
        
        return min(1.0, quality_score)
    
    def _calculate_context_relevance(self, query: str, contexts: List[SearchResult]) -> float:
        """Calculate how relevant the contexts are to the query"""
        if not contexts:
            return 0.0
        
        query_words = set(query.lower().split())
        total_relevance = 0
        
        for context in contexts:
            context_words = set(context.content.lower().split())
            overlap = len(query_words.intersection(context_words))
            relevance = overlap / max(len(query_words), 1)
            total_relevance += relevance
        
        return total_relevance / len(contexts)
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """Get optimization performance report"""
        stats = self.performance_tracker.get_performance_stats()
        
        return {
            "overall_stats": stats,
            "query_type_performance": {
                qt.value: self.performance_tracker.get_performance_stats(qt.value)
                for qt in QueryType
            },
            "active_adjustments": dict(self.template_adjustments),
            "optimization_rules": [
                {
                    "name": rule.name,
                    "enabled": rule.enabled,
                    "priority": rule.priority
                }
                for rule in self.optimization_rules
            ]
        }

# Singleton instance
_prompt_optimizer = None

async def get_prompt_optimizer() -> DynamicPromptOptimizer:
    """Get singleton prompt optimizer instance"""
    global _prompt_optimizer
    
    if _prompt_optimizer is None:
        _prompt_optimizer = DynamicPromptOptimizer()
    
    return _prompt_optimizer
