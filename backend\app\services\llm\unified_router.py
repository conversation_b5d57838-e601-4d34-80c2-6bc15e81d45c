"""
Unified LLM API Router

Provides a unified interface for multiple LLM providers with intelligent routing.
"""
import asyncio
from typing import List, Dict, Any, Optional, AsyncGenerator, Union
from dataclasses import dataclass
from enum import Enum
import time

from app.services.llm.openai_adapter import OpenAIAdapter, get_openai_adapter, ChatRequest, ChatResponse
from app.services.llm.claude_adapter import ClaudeA<PERSON><PERSON><PERSON>, get_claude_adapter
from app.core.context_config import context_settings
import structlog

logger = structlog.get_logger(__name__)

class LLMProvider(str, Enum):
    OPENAI = "openai"
    CLAUDE = "claude"
    AUTO = "auto"

@dataclass
class ModelInfo:
    """Model information"""
    id: str
    provider: LLMProvider
    display_name: str
    context_length: int
    supports_streaming: bool = True
    cost_per_1k_tokens: float = 0.0

class UnifiedLLMRouter:
    """Unified router for multiple LLM providers"""
    
    def __init__(self):
        self.openai_adapter = None
        self.claude_adapter = None
        
        # Model registry - deprecated, use simple_llm_service instead
        self.models = {}
        
        logger.info("Initialized UnifiedLLMRouter", models=len(self.models))
    
    async def initialize(self):
        """Initialize adapters"""
        try:
            self.openai_adapter = await get_openai_adapter()
            self.claude_adapter = await get_claude_adapter()
            logger.info("LLM adapters initialized successfully")
        except Exception as e:
            logger.error("Failed to initialize LLM adapters", error=str(e))
            raise
    
    async def chat_completion(
        self, 
        request: ChatRequest,
        preferred_provider: Optional[LLMProvider] = None
    ) -> Union[ChatResponse, AsyncGenerator]:
        """Route chat completion to appropriate provider"""
        try:
            # Determine provider
            provider = self._determine_provider(request.model, preferred_provider)
            
            # Route to appropriate adapter
            if provider == LLMProvider.OPENAI:
                if not self.openai_adapter:
                    self.openai_adapter = await get_openai_adapter()
                return await self.openai_adapter.chat_completion(request)
            
            elif provider == LLMProvider.CLAUDE:
                if not self.claude_adapter:
                    self.claude_adapter = await get_claude_adapter()
                return await self.claude_adapter.chat_completion(request)
            
            else:
                raise ValueError(f"Unsupported provider: {provider}")
                
        except Exception as e:
            logger.error("Chat completion routing failed", model=request.model, error=str(e))
            
            # Fallback to alternative provider
            return await self._fallback_completion(request, provider)
    
    def _determine_provider(
        self, 
        model: str, 
        preferred_provider: Optional[LLMProvider] = None
    ) -> LLMProvider:
        """Determine which provider to use for a model"""
        
        # Use preferred provider if specified and model is available
        if preferred_provider and preferred_provider != LLMProvider.AUTO:
            if model in self.models and self.models[model].provider == preferred_provider:
                return preferred_provider
        
        # Check if model is registered
        if model in self.models:
            return self.models[model].provider
        
        # Auto-detect based on model name
        if "gpt" in model.lower() or "openai" in model.lower():
            return LLMProvider.OPENAI
        elif "claude" in model.lower() or "anthropic" in model.lower():
            return LLMProvider.CLAUDE
        
        # Default to OpenAI
        return LLMProvider.OPENAI
    
    async def _fallback_completion(
        self, 
        request: ChatRequest, 
        failed_provider: LLMProvider
    ) -> Union[ChatResponse, AsyncGenerator]:
        """Fallback to alternative provider on failure"""
        try:
            # Determine fallback provider
            if failed_provider == LLMProvider.OPENAI:
                fallback_provider = LLMProvider.CLAUDE
                fallback_model = "claude-3-sonnet"
            else:
                fallback_provider = LLMProvider.OPENAI
                fallback_model = "gpt-3.5-turbo"
            
            # Create fallback request
            fallback_request = ChatRequest(
                messages=request.messages,
                model=fallback_model,
                temperature=request.temperature,
                max_tokens=request.max_tokens,
                stream=request.stream,
                user_id=request.user_id,
                session_id=request.session_id,
                enable_context=request.enable_context
            )
            
            logger.info(
                "Falling back to alternative provider",
                original_provider=failed_provider,
                fallback_provider=fallback_provider,
                fallback_model=fallback_model
            )
            
            # Route to fallback provider
            if fallback_provider == LLMProvider.OPENAI:
                if not self.openai_adapter:
                    self.openai_adapter = await get_openai_adapter()
                return await self.openai_adapter.chat_completion(fallback_request)
            else:
                if not self.claude_adapter:
                    self.claude_adapter = await get_claude_adapter()
                return await self.claude_adapter.chat_completion(fallback_request)
                
        except Exception as e:
            logger.error("Fallback completion also failed", error=str(e))
            raise
    
    async def list_models(self) -> List[Dict[str, Any]]:
        """List all available models from all providers"""
        all_models = []
        
        # Add registered models
        for model_id, model_info in self.models.items():
            all_models.append({
                "id": model_id,
                "object": "model",
                "owned_by": model_info.provider.value,
                "display_name": model_info.display_name,
                "context_length": model_info.context_length,
                "supports_streaming": model_info.supports_streaming,
                "cost_per_1k_tokens": model_info.cost_per_1k_tokens
            })
        
        # Try to get live models from providers
        try:
            if self.openai_adapter:
                openai_models = await self.openai_adapter.list_models()
                for model in openai_models:
                    if model["id"] not in self.models:
                        all_models.append({
                            **model,
                            "provider": "openai",
                            "display_name": model["id"],
                            "context_length": 4096,  # Default
                            "supports_streaming": True,
                            "cost_per_1k_tokens": 0.002  # Default
                        })
        except Exception as e:
            logger.warning("Failed to fetch OpenAI models", error=str(e))
        
        try:
            if self.claude_adapter:
                claude_models = await self.claude_adapter.list_models()
                for model in claude_models:
                    if model["id"] not in self.models:
                        all_models.append({
                            **model,
                            "provider": "claude",
                            "display_name": model["id"],
                            "context_length": 200000,  # Default for Claude
                            "supports_streaming": True,
                            "cost_per_1k_tokens": 0.003  # Default
                        })
        except Exception as e:
            logger.warning("Failed to fetch Claude models", error=str(e))
        
        return all_models
    
    def get_model_info(self, model_id: str) -> Optional[ModelInfo]:
        """Get information about a specific model"""
        return self.models.get(model_id)
    
    async def health_check(self) -> Dict[str, Any]:
        """Check health of all providers"""
        health_status = {
            "status": "healthy",
            "providers": {},
            "timestamp": time.time()
        }
        
        # Check OpenAI
        try:
            if self.openai_adapter:
                models = await self.openai_adapter.list_models()
                health_status["providers"]["openai"] = {
                    "status": "healthy",
                    "models_available": len(models)
                }
            else:
                health_status["providers"]["openai"] = {
                    "status": "not_initialized"
                }
        except Exception as e:
            health_status["providers"]["openai"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_status["status"] = "degraded"
        
        # Check Claude
        try:
            if self.claude_adapter:
                models = await self.claude_adapter.list_models()
                health_status["providers"]["claude"] = {
                    "status": "healthy",
                    "models_available": len(models)
                }
            else:
                health_status["providers"]["claude"] = {
                    "status": "not_initialized"
                }
        except Exception as e:
            health_status["providers"]["claude"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            if health_status["status"] == "healthy":
                health_status["status"] = "degraded"
        
        return health_status
    
    async def close(self):
        """Close all adapters"""
        if self.openai_adapter:
            await self.openai_adapter.close()
        if self.claude_adapter:
            await self.claude_adapter.close()

# Singleton instance
_llm_router = None

async def get_llm_router() -> UnifiedLLMRouter:
    """Get singleton LLM router instance"""
    global _llm_router
    
    if _llm_router is None:
        _llm_router = UnifiedLLMRouter()
        await _llm_router.initialize()
    
    return _llm_router
