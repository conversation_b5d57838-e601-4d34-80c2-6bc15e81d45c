<template>
  <div class="data-table">
    <!-- 搜索和过滤 -->
    <div class="table-header">
      <div class="search-box">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索..."
          class="search-input"
        />
      </div>
      
      <div class="table-actions">
        <button @click="refreshData" class="btn btn-secondary">
          刷新
        </button>
        <button @click="exportData" class="btn btn-primary">
          导出
        </button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <table class="table">
        <thead>
          <tr>
            <th
              v-for="column in columns"
              :key="column.key"
              @click="handleSort(column.key)"
              :class="{ sortable: column.sortable }"
            >
              {{ column.title }}
              <span v-if="column.sortable" class="sort-indicator">
                <i
                  :class="{
                    'sort-asc': sortKey === column.key && sortOrder === 'asc',
                    'sort-desc': sortKey === column.key && sortOrder === 'desc'
                  }"
                ></i>
              </span>
            </th>
            <th v-if="showActions" class="actions-column">操作</th>
          </tr>
        </thead>
        
        <tbody>
          <tr
            v-for="(item, index) in paginatedData"
            :key="item.id || index"
            :class="{ selected: selectedItems.includes(item.id) }"
            @click="handleRowClick(item)"
          >
            <td v-for="column in columns" :key="column.key">
              <slot
                :name="`column-${column.key}`"
                :item="item"
                :value="getNestedValue(item, column.key)"
              >
                {{ formatValue(getNestedValue(item, column.key), column) }}
              </slot>
            </td>
            
            <td v-if="showActions" class="actions-cell">
              <div class="action-buttons">
                <button
                  v-for="action in actions"
                  :key="action.key"
                  @click.stop="handleAction(action.key, item)"
                  :class="`btn btn-${action.type || 'secondary'} btn-sm`"
                  :title="action.title"
                >
                  <i v-if="action.icon" :class="action.icon"></i>
                  {{ action.label }}
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      
      <!-- 空状态 -->
      <div v-if="filteredData.length === 0" class="empty-state">
        <div class="empty-icon">📄</div>
        <p>{{ searchQuery ? '没有找到匹配的数据' : '暂无数据' }}</p>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="showPagination && filteredData.length > 0" class="pagination">
      <div class="pagination-info">
        显示 {{ startIndex + 1 }} - {{ endIndex }} 条，共 {{ filteredData.length }} 条
      </div>
      
      <div class="pagination-controls">
        <button
          @click="currentPage = 1"
          :disabled="currentPage === 1"
          class="btn btn-secondary btn-sm"
        >
          首页
        </button>
        
        <button
          @click="currentPage--"
          :disabled="currentPage === 1"
          class="btn btn-secondary btn-sm"
        >
          上一页
        </button>
        
        <span class="page-info">
          第 {{ currentPage }} 页，共 {{ totalPages }} 页
        </span>
        
        <button
          @click="currentPage++"
          :disabled="currentPage === totalPages"
          class="btn btn-secondary btn-sm"
        >
          下一页
        </button>
        
        <button
          @click="currentPage = totalPages"
          :disabled="currentPage === totalPages"
          class="btn btn-secondary btn-sm"
        >
          末页
        </button>
      </div>
      
      <div class="page-size-selector">
        <select v-model="pageSize" @change="currentPage = 1">
          <option v-for="size in pageSizeOptions" :key="size" :value="size">
            {{ size }} 条/页
          </option>
        </select>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// 类型定义
interface Column {
  key: string
  title: string
  sortable?: boolean
  formatter?: (value: any) => string
  width?: string
}

interface Action {
  key: string
  label: string
  icon?: string
  type?: 'primary' | 'secondary' | 'danger'
  title?: string
}

interface Props {
  data: Array<any>
  columns: Column[]
  actions?: Action[]
  showActions?: boolean
  showPagination?: boolean
  pageSize?: number
  pageSizeOptions?: number[]
  searchable?: boolean
  selectable?: boolean
}

// Props
const props = withDefaults(defineProps<Props>(), {
  showActions: true,
  showPagination: true,
  pageSize: 10,
  pageSizeOptions: () => [10, 20, 50, 100],
  searchable: true,
  selectable: false
})

// Emits
const emit = defineEmits<{
  rowClick: [item: any]
  action: [actionKey: string, item: any]
  refresh: []
  export: []
}>()

// 响应式数据
const searchQuery = ref('')
const sortKey = ref('')
const sortOrder = ref<'asc' | 'desc'>('asc')
const currentPage = ref(1)
const pageSize = ref(props.pageSize)
const selectedItems = ref<any[]>([])

// 计算属性
const filteredData = computed(() => {
  let result = [...props.data]
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(item =>
      props.columns.some(column => {
        const value = getNestedValue(item, column.key)
        return String(value).toLowerCase().includes(query)
      })
    )
  }
  
  // 排序
  if (sortKey.value) {
    result.sort((a, b) => {
      const aVal = getNestedValue(a, sortKey.value)
      const bVal = getNestedValue(b, sortKey.value)
      
      if (aVal < bVal) return sortOrder.value === 'asc' ? -1 : 1
      if (aVal > bVal) return sortOrder.value === 'asc' ? 1 : -1
      return 0
    })
  }
  
  return result
})

const totalPages = computed(() => 
  Math.ceil(filteredData.value.length / pageSize.value)
)

const startIndex = computed(() => 
  (currentPage.value - 1) * pageSize.value
)

const endIndex = computed(() => 
  Math.min(startIndex.value + pageSize.value, filteredData.value.length)
)

const paginatedData = computed(() => 
  filteredData.value.slice(startIndex.value, endIndex.value)
)

// 方法
const getNestedValue = (obj: any, path: string) => {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}

const formatValue = (value: any, column: Column) => {
  if (column.formatter) {
    return column.formatter(value)
  }
  
  if (value === null || value === undefined) {
    return '-'
  }
  
  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }
  
  if (value instanceof Date) {
    return value.toLocaleDateString()
  }
  
  return String(value)
}

const handleSort = (key: string) => {
  const column = props.columns.find(col => col.key === key)
  if (!column?.sortable) return
  
  if (sortKey.value === key) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortKey.value = key
    sortOrder.value = 'asc'
  }
}

const handleRowClick = (item: any) => {
  emit('rowClick', item)
}

const handleAction = (actionKey: string, item: any) => {
  emit('action', actionKey, item)
}

const refreshData = () => {
  emit('refresh')
}

const exportData = () => {
  emit('export')
}

// 监听器
watch(() => props.data, () => {
  currentPage.value = 1
})

watch(searchQuery, () => {
  currentPage.value = 1
})
</script>

<style scoped>
.data-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  width: 300px;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.table-container {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.table th.sortable {
  cursor: pointer;
  user-select: none;
}

.table th.sortable:hover {
  background: #f3f4f6;
}

.sort-indicator {
  margin-left: 4px;
}

.table tr:hover {
  background: #f9fafb;
}

.table tr.selected {
  background: #eff6ff;
}

.actions-cell {
  white-space: nowrap;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.empty-state {
  text-align: center;
  padding: 48px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-top: 1px solid #e5e7eb;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-info {
  margin: 0 16px;
  color: #6b7280;
}

.page-size-selector select {
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
}
</style>
