"""
Pydantic schemas for Knowledge Base API
"""
from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum

class DocumentType(str, Enum):
    PDF = "pdf"
    DOCX = "docx"
    TXT = "txt"
    MD = "md"
    HTML = "html"
    CODE = "code"

class DocumentStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

# Collection schemas
class CollectionBase(BaseModel):
    name: str = Field(..., description="Collection name")
    description: Optional[str] = Field(None, description="Collection description")
    category: Optional[str] = Field(None, description="Collection category")
    subcategory: Optional[str] = Field(None, description="Collection subcategory")

class CollectionCreate(CollectionBase):
    embedding_model: Optional[str] = Field("all-MiniLM-L6-v2", description="Embedding model to use")
    vector_dimension: Optional[int] = Field(384, description="Vector dimension")

class CollectionResponse(CollectionBase):
    id: str
    created_at: datetime
    document_count: int = 0
    code_example_count: int = 0
    
    class Config:
        from_attributes = True

# Document schemas
class DocumentBase(BaseModel):
    title: str = Field(..., description="Document title")
    content: str = Field(..., description="Document content")
    document_type: DocumentType
    language: Optional[str] = Field("en", description="Document language")
    tags: Optional[List[str]] = Field(default_factory=list, description="Document tags")

class DocumentCreate(DocumentBase):
    collection_name: str = Field("general-docs", description="Collection to add document to")
    file_path: Optional[str] = Field(None, description="Original file path")

class DocumentResponse(DocumentBase):
    id: str
    collection_id: str
    status: DocumentStatus
    file_size: Optional[int]
    file_hash: Optional[str]
    created_at: datetime
    processed_at: Optional[datetime]
    chunk_count: Optional[int] = 0
    
    class Config:
        from_attributes = True

# Code example schemas
class CodeExampleBase(BaseModel):
    title: str = Field(..., description="Code example title")
    description: Optional[str] = Field(None, description="Code example description")
    code: str = Field(..., description="Code content")
    language: str = Field(..., description="Programming language")
    framework: Optional[str] = Field(None, description="Framework used")
    category: Optional[str] = Field(None, description="Code category")
    tags: Optional[List[str]] = Field(default_factory=list, description="Code tags")
    dependencies: Optional[List[str]] = Field(default_factory=list, description="Code dependencies")

class CodeExampleCreate(CodeExampleBase):
    collection_name: Optional[str] = Field("code-examples", description="Collection to add code to")

class CodeExampleResponse(CodeExampleBase):
    id: str
    collection_id: str
    created_at: datetime
    
    class Config:
        from_attributes = True

# Search schemas
class SearchRequest(BaseModel):
    query: str = Field(..., description="Search query")
    collection_names: Optional[List[str]] = Field(None, description="Collections to search in")
    document_types: Optional[List[str]] = Field(None, description="Document types to filter")
    languages: Optional[List[str]] = Field(None, description="Languages to filter")
    limit: int = Field(10, ge=1, le=100, description="Maximum number of results")
    similarity_threshold: float = Field(0.7, ge=0.0, le=1.0, description="Minimum similarity score")
    
    @validator('query')
    def query_not_empty(cls, v):
        if not v.strip():
            raise ValueError('Query cannot be empty')
        return v.strip()

class SearchResult(BaseModel):
    id: str = Field(..., description="Result ID")
    score: float = Field(..., description="Similarity score")
    content: str = Field(..., description="Content snippet")
    title: str = Field(..., description="Content title")
    type: str = Field(..., description="Content type (document/code)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

class SearchResponse(BaseModel):
    query: str = Field(..., description="Original search query")
    results: List[SearchResult] = Field(..., description="Search results")
    total_results: int = Field(..., description="Total number of results")
    limit: int = Field(..., description="Applied limit")

# Upload schemas
class UploadResponse(BaseModel):
    document_id: str = Field(..., description="Created document ID")
    message: str = Field(..., description="Success message")
    collection: str = Field(..., description="Collection name")
    filename: str = Field(..., description="Original filename")

class BulkUploadResult(BaseModel):
    filename: str
    document_id: Optional[str] = None
    status: str
    error: Optional[str] = None

class BulkUploadResponse(BaseModel):
    processed: int = Field(..., description="Number of successfully processed files")
    errors: int = Field(..., description="Number of files with errors")
    results: List[BulkUploadResult] = Field(..., description="Detailed results")
    error_details: List[str] = Field(default_factory=list, description="Error details")

# Statistics schemas
class CollectionStats(BaseModel):
    name: str
    document_count: int
    code_example_count: int
    total_items: int
    created_at: datetime

class KnowledgeBaseStats(BaseModel):
    total_collections: int
    total_documents: int
    total_code_examples: int
    total_items: int
    collections: List[CollectionResponse]

# Health check schema
class HealthResponse(BaseModel):
    status: str = Field(..., description="Health status")
    collections_count: int = Field(..., description="Number of collections")
    timestamp: datetime = Field(..., description="Check timestamp")

# Initialization schema
class InitializeResponse(BaseModel):
    message: str = Field(..., description="Initialization result message")
    success: bool = Field(True, description="Whether initialization was successful")

# Error schemas
class ErrorResponse(BaseModel):
    detail: str = Field(..., description="Error detail")
    error_code: Optional[str] = Field(None, description="Error code")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")

# Chunk schema (for internal use)
class DocumentChunk(BaseModel):
    id: str
    chunk_index: int
    content: str
    token_count: int
    vector_id: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        from_attributes = True
