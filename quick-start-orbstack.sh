#!/bin/bash

# Quick Start Script for Context Engine with OrbStack
# This is a simplified version for rapid development setup

set -e

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 Context Engine Quick Start (OrbStack)${NC}"
echo "=========================================="

# Make scripts executable
chmod +x scripts/*.sh

# Create basic directories
echo -e "${YELLOW}📁 Setting up directories...${NC}"
mkdir -p knowledge/{frontend,backend,examples} data/{chroma,models} backend/logs

# Create .env if it doesn't exist
if [ ! -f .env ]; then
    echo -e "${YELLOW}📝 Creating .env file...${NC}"
    cat > .env << 'EOF'
ENVIRONMENT=development
DEBUG=true
DATABASE_URL=****************************************************/context_engine
VECTOR_DB_TYPE=qdrant
VECTOR_DB_URL=http://qdrant:6333
REDIS_URL=redis://redis:6379
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
SECRET_KEY=dev-secret-key
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
EOF
fi

# Start core services only
echo -e "${YELLOW}🐳 Starting core services...${NC}"
docker-compose -f docker-compose.dev.yml up -d qdrant redis postgres

# Wait for services
echo -e "${YELLOW}⏳ Waiting for services...${NC}"
sleep 10

# Check if services are ready
echo -e "${YELLOW}🔍 Checking service health...${NC}"

# Simple health checks
until curl -s http://localhost:6333/health > /dev/null; do
    echo -n "."
    sleep 2
done
echo -e "\n${GREEN}✅ Qdrant ready${NC}"

until docker-compose -f docker-compose.dev.yml exec -T redis redis-cli ping > /dev/null; do
    echo -n "."
    sleep 1
done
echo -e "${GREEN}✅ Redis ready${NC}"

until docker-compose -f docker-compose.dev.yml exec -T postgres pg_isready -U context_user > /dev/null; do
    echo -n "."
    sleep 2
done
echo -e "${GREEN}✅ PostgreSQL ready${NC}"

echo ""
echo -e "${GREEN}🎉 Core services are ready!${NC}"
echo "=================================="
echo -e "${BLUE}🔍 Qdrant Dashboard:${NC} http://localhost:6333/dashboard"
echo -e "${BLUE}📊 Service Status:${NC} docker-compose -f docker-compose.dev.yml ps"
echo ""
echo -e "${YELLOW}📋 Next Steps:${NC}"
echo "1. Set up backend: cd backend && python -m venv venv && source venv/bin/activate && pip install -r requirements.txt"
echo "2. Set up frontend: cd frontend && pnpm install"
echo "3. Start backend: cd backend && source venv/bin/activate && uvicorn app.main:app --reload --port 8080"
echo "4. Start frontend: cd frontend && pnpm dev"
echo ""
echo -e "${YELLOW}🔧 Or use the full script:${NC} ./scripts/start-dev-orbstack.sh"
echo -e "${YELLOW}🏥 Health check:${NC} ./scripts/health-check.sh"
echo -e "${YELLOW}🛑 Stop services:${NC} ./scripts/stop-dev.sh"
