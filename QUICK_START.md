# 🚀 Artifacts Chat 快速开始指南

## 📋 环境要求

### 最低要求
- **Python**: 3.11+
- **Node.js**: 18+
- **Git**: 2.30+

### 推荐环境
- **Docker**: 20.10+ (最简单的部署方式)
- **pnpm**: 8.0+ (更快的包管理器)

## ⚡ 方式一：Docker Compose 一键启动 (推荐)

### 1. 克隆项目
```bash
git clone <repository-url>
cd artifacts-chat
```

### 2. 配置API密钥
```bash
# 复制环境变量模板
cp backend/.env.example backend/.env

# 编辑配置文件
vim backend/.env
```

**必需配置** (在 `backend/.env` 中):
```bash
# OpenAI API 密钥 (必需)
OPENAI_API_KEY=sk-your-openai-api-key-here

# 或者 Claude API 密钥
CLAUDE_API_KEY=sk-ant-your-claude-api-key-here

# 安全密钥 (生产环境必须修改)
SECRET_KEY=your-secret-key-change-in-production
```

### 3. 一键启动
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

### 4. 验证部署
```bash
# 检查后端健康状态
curl http://localhost:8080/health

# 访问前端应用
open http://localhost:3000
```

🎉 **完成！** 现在可以开始使用 Artifacts Chat 了！

---

## 🛠️ 方式二：本地开发环境

### 1. 克隆项目
```bash
git clone <repository-url>
cd artifacts-chat
```

### 2. 后端设置

#### 2.1 创建Python虚拟环境
```bash
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
source venv/bin/activate  # macOS/Linux
# 或 venv\Scripts\activate  # Windows
```

#### 2.2 安装依赖
```bash
# 升级pip
pip install --upgrade pip

# 安装Python依赖
pip install -r requirements.txt
```

#### 2.3 配置环境变量
```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件
vim .env
```

**开发环境配置** (在 `backend/.env` 中):
```bash
# 开发模式
DEBUG=true
LOG_LEVEL=INFO

# 数据库 (开发环境使用SQLite)
DATABASE_URL=sqlite:///./artifacts_chat.db

# LLM API 密钥 (至少配置一个)
OPENAI_API_KEY=sk-your-openai-api-key-here
# CLAUDE_API_KEY=sk-ant-your-claude-api-key-here

# 安全密钥
SECRET_KEY=development-secret-key
```

#### 2.4 初始化数据库
```bash
# 创建数据库表
python -c "
from app.database import engine
from app.models import Base
Base.metadata.create_all(bind=engine)
print('Database initialized successfully')
"
```

#### 2.5 启动后端服务
```bash
# 启动开发服务器
uvicorn app.main:app --reload --host 0.0.0.0 --port 8080

# 验证服务
curl http://localhost:8080/health
```

### 3. 前端设置

#### 3.1 安装依赖
```bash
cd frontend

# 使用pnpm (推荐)
pnpm install

# 或使用npm
npm install
```

#### 3.2 配置环境变量
```bash
# 复制配置模板
cp .env.example .env.local

# 编辑配置
vim .env.local
```

**前端配置** (在 `frontend/.env.local` 中):
```bash
# API基础URL
VITE_API_BASE_URL=http://localhost:8080

# 开发模式
VITE_DEV_MODE=true
```

#### 3.3 启动前端服务
```bash
# 启动开发服务器
pnpm dev

# 或使用npm
npm run dev
```

### 4. 验证完整系统
```bash
# 后端健康检查
curl http://localhost:8080/health

# 前端访问
open http://localhost:3000

# API文档
open http://localhost:8080/docs
```

---

## 🧪 测试核心功能

### 1. 基础聊天测试
1. 访问 http://localhost:3000
2. 在聊天框中输入: "你好，请介绍一下自己"
3. 验证AI回复功能

### 2. Artifacts渲染测试
在聊天框中输入以下内容测试不同类型的渲染：

#### HTML/CSS/JS 测试
```
请创建一个简单的计数器网页，包含一个按钮和显示数字的区域。
```

#### Vue组件测试
```
请创建一个Vue组件，实现一个待办事项列表，可以添加和删除任务。
```

#### 图表测试
```
请创建一个柱状图，显示2023年各月份的销售数据。
```

### 3. API测试
```bash
# 测试聊天API
curl -X POST http://localhost:8080/api/enhanced-chat/mock-completions \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "Hello"}],
    "model": "gpt-3.5-turbo"
  }'

# 测试健康检查
curl http://localhost:8080/health

# 测试模型列表
curl http://localhost:8080/api/enhanced-chat/models
```

---

## 🔧 常见问题解决

### Q1: 后端启动失败
```bash
# 检查Python版本
python --version  # 应该是3.11+

# 检查虚拟环境
which python  # 应该指向venv中的python

# 重新安装依赖
pip install -r requirements.txt --force-reinstall
```

### Q2: 前端启动失败
```bash
# 检查Node.js版本
node --version  # 应该是18+

# 清理缓存重新安装
rm -rf node_modules package-lock.json
pnpm install
```

### Q3: API调用失败
```bash
# 检查API密钥配置
grep OPENAI_API_KEY backend/.env

# 检查后端服务状态
curl http://localhost:8080/health

# 检查CORS配置
# 浏览器开发者工具 -> Console 查看错误信息
```

### Q4: Docker启动失败
```bash
# 检查Docker状态
docker --version
docker-compose --version

# 查看服务日志
docker-compose logs backend
docker-compose logs frontend

# 重新构建
docker-compose down
docker-compose up --build -d
```

### Q5: 端口冲突
```bash
# 检查端口占用
lsof -i :8080  # 后端端口
lsof -i :3000  # 前端端口

# 修改端口配置
# 编辑 docker-compose.yml 或启动命令中的端口
```

---

## 📚 下一步

### 开发相关
- 📖 [开发指南](docs/DEVELOPMENT_GUIDE.md) - 详细的开发环境搭建
- 🏗️ [系统架构](docs/SYSTEM_ARCHITECTURE.md) - 了解系统设计
- 🔌 [API文档](docs/API_SPECIFICATION.md) - 接口规范说明

### 部署相关
- 🚀 [部署指南](docs/DEPLOYMENT_GUIDE.md) - 生产环境部署
- 🐳 [Docker配置](docker-compose.yml) - 容器化部署
- 🔒 [安全配置](docs/DEPLOYMENT_GUIDE.md#安全配置) - 生产环境安全

### 功能特性
- 🧠 [智能上下文引擎](docs/INTELLIGENT_CONTEXT_ENGINE.md) - 核心技术
- 🎨 [Artifacts渲染](frontend/public/renderer.html) - 代码渲染器
- 📊 [监控指标](docs/DEPLOYMENT_GUIDE.md#监控和维护) - 系统监控

---

## 🆘 获取帮助

### 技术支持
- **文档**: 查看 `docs/` 目录下的详细文档
- **日志**: 检查应用和系统日志排查问题
- **健康检查**: 使用 `/health` 端点检查服务状态

### 社区支持
- **GitHub Issues**: 提交bug报告和功能请求
- **讨论区**: 参与技术讨论和经验分享
- **贡献指南**: 查看如何为项目做贡献

---

**🎉 欢迎使用 Artifacts Chat！** 

这是一个功能完整的智能聊天平台，集成了先进的上下文引擎和代码渲染能力。开始探索AI驱动的开发体验吧！
