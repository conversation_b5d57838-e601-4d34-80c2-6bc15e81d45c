# 🚀 Artifacts Chat 开发指南

## 📋 目录
- [环境要求](#环境要求)
- [本地开发环境搭建](#本地开发环境搭建)
- [项目结构说明](#项目结构说明)
- [开发流程](#开发流程)
- [调试指南](#调试指南)
- [常见问题](#常见问题)

## 🔧 环境要求

### 基础环境
- **Node.js**: 18.0+ (推荐 18.17.0)
- **Python**: 3.11+ (推荐 3.11.5)
- **Git**: 2.30+

### 可选环境 (完整功能)
- **PostgreSQL**: 15+ (生产环境推荐)
- **Redis**: 7+ (缓存和会话管理)
- **Docker**: 20.10+ (容器化部署)
- **Docker Compose**: 2.0+ (多服务编排)

### 开发工具推荐
- **IDE**: VS Code / WebStorm / PyCharm
- **API测试**: Postman / Insomnia
- **数据库管理**: DBeaver / pgAdmin
- **Git客户端**: SourceTree / GitKraken

## 🏗️ 本地开发环境搭建

### 1. 克隆项目
```bash
git clone <repository-url>
cd artifacts-chat
```

### 2. 后端环境搭建

#### 2.1 创建Python虚拟环境
```bash
cd backend

# 使用 venv (推荐)
python -m venv venv

# 激活虚拟环境
# macOS/Linux:
source venv/bin/activate
# Windows:
venv\Scripts\activate
```

#### 2.2 安装Python依赖
```bash
# 升级pip
pip install --upgrade pip

# 安装依赖
pip install -r requirements.txt

# 验证安装
python -c "import fastapi; print('FastAPI installed successfully')"
```

#### 2.3 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
vim .env  # 或使用其他编辑器
```

**必需配置项**:
```bash
# 基础配置
DEBUG=true
LOG_LEVEL=INFO

# 数据库配置 (开发环境使用SQLite)
DATABASE_URL=sqlite:///./artifacts_chat.db

# LLM API配置 (至少配置一个)
OPENAI_API_KEY=sk-your-openai-api-key-here
# CLAUDE_API_KEY=sk-ant-your-claude-api-key-here

# 安全配置
SECRET_KEY=your-development-secret-key-change-in-production
```

#### 2.4 初始化数据库
```bash
# 创建数据库表
python -c "
from app.database import engine
from app.models import Base
Base.metadata.create_all(bind=engine)
print('Database initialized successfully')
"
```

#### 2.5 启动后端服务
```bash
# 开发模式启动
uvicorn app.main:app --reload --host 0.0.0.0 --port 8080

# 验证服务
curl http://localhost:8080/health
```

### 3. 前端环境搭建

#### 3.1 安装Node.js依赖
```bash
cd frontend

# 使用pnpm (推荐)
pnpm install

# 或使用npm
npm install

# 或使用yarn
yarn install
```

#### 3.2 配置前端环境
```bash
# 复制环境变量模板
cp .env.example .env.local

# 编辑配置
vim .env.local
```

**前端配置**:
```bash
# API基础URL
VITE_API_BASE_URL=http://localhost:8080

# 开发模式配置
VITE_DEV_MODE=true
VITE_ENABLE_MOCK=false
```

#### 3.3 启动前端服务
```bash
# 开发模式启动
pnpm dev

# 或
npm run dev

# 验证服务
open http://localhost:3000
```

### 4. 验证完整系统

#### 4.1 检查服务状态
```bash
# 后端健康检查
curl http://localhost:8080/health

# 前端访问
open http://localhost:3000

# API文档
open http://localhost:8080/docs
```

#### 4.2 测试核心功能
```bash
# 测试聊天API
curl -X POST http://localhost:8080/api/enhanced-chat/mock-completions \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "Hello"}],
    "model": "gpt-3.5-turbo"
  }'

# 测试Artifacts渲染
# 在前端界面中输入包含代码的消息，验证渲染功能
```

## 📁 项目结构说明

```
artifacts-chat/
├── backend/                    # FastAPI后端
│   ├── app/
│   │   ├── api/               # API路由
│   │   │   ├── chat.py        # 聊天相关API
│   │   │   ├── enhanced_chat.py # 增强聊天API
│   │   │   └── artifacts.py   # Artifacts API
│   │   ├── core/              # 核心配置
│   │   │   ├── config.py      # 应用配置
│   │   │   ├── database.py    # 数据库配置
│   │   │   ├── error_handler.py # 错误处理
│   │   │   └── metrics.py     # 性能监控
│   │   ├── models/            # 数据模型
│   │   │   ├── chat.py        # 聊天模型
│   │   │   ├── artifacts.py   # Artifacts模型
│   │   │   └── knowledge.py   # 知识库模型
│   │   ├── services/          # 业务逻辑
│   │   │   ├── llm/           # LLM服务
│   │   │   └── context_engine/ # 上下文引擎
│   │   ├── middleware/        # 中间件
│   │   └── main.py           # 应用入口
│   ├── requirements.txt       # Python依赖
│   ├── .env.example          # 环境变量模板
│   └── Dockerfile            # 容器配置
├── frontend/                  # Vue.js前端
│   ├── src/
│   │   ├── components/        # Vue组件
│   │   │   ├── Chat/         # 聊天组件
│   │   │   ├── Artifacts/    # Artifacts组件
│   │   │   └── Layout/       # 布局组件
│   │   ├── stores/           # Pinia状态管理
│   │   ├── views/            # 页面视图
│   │   ├── utils/            # 工具函数
│   │   └── main.js          # 应用入口
│   ├── public/
│   │   ├── vendor/           # 离线依赖
│   │   └── renderer.html     # Artifacts渲染器
│   ├── package.json          # Node.js依赖
│   ├── vite.config.js        # Vite配置
│   └── Dockerfile           # 容器配置
├── docs/                     # 项目文档
├── knowledge/                # 知识库文档
├── docker-compose.yml        # 容器编排
└── README.md                # 项目说明
```

## 🔄 开发流程

### 1. 功能开发流程

#### 1.1 创建功能分支
```bash
git checkout -b feature/your-feature-name
```

#### 1.2 后端开发
```bash
# 1. 定义数据模型 (如需要)
# 编辑 app/models/

# 2. 创建API路由
# 编辑 app/api/

# 3. 实现业务逻辑
# 编辑 app/services/

# 4. 测试API
curl -X POST http://localhost:8080/api/your-endpoint
```

#### 1.3 前端开发
```bash
# 1. 创建Vue组件
# 编辑 src/components/

# 2. 添加路由 (如需要)
# 编辑 src/router/

# 3. 状态管理 (如需要)
# 编辑 src/stores/

# 4. 测试界面
pnpm dev
```

#### 1.4 集成测试
```bash
# 启动完整系统
# 终端1: 后端
cd backend && uvicorn app.main:app --reload

# 终端2: 前端
cd frontend && pnpm dev

# 测试功能完整性
```

### 2. 代码规范

#### 2.1 Python代码规范
```bash
# 安装代码格式化工具
pip install black isort flake8

# 格式化代码
black app/
isort app/

# 检查代码质量
flake8 app/
```

#### 2.2 JavaScript代码规范
```bash
# 安装ESLint和Prettier
pnpm add -D eslint prettier

# 格式化代码
pnpm run lint:fix
pnpm run format
```

### 3. 测试流程

#### 3.1 后端测试
```bash
# 安装测试依赖
pip install pytest pytest-asyncio httpx

# 运行测试
pytest tests/ -v

# 测试覆盖率
pytest --cov=app tests/
```

#### 3.2 前端测试
```bash
# 安装测试依赖
pnpm add -D vitest @vue/test-utils

# 运行测试
pnpm test

# 测试覆盖率
pnpm test:coverage
```

## 🐛 调试指南

### 1. 后端调试

#### 1.1 日志调试
```python
# 在代码中添加日志
import logging
logger = logging.getLogger(__name__)

logger.info("Debug info")
logger.error("Error occurred", exc_info=True)
```

#### 1.2 断点调试
```bash
# 使用pdb调试
import pdb; pdb.set_trace()

# 或使用VS Code调试器
# 配置 .vscode/launch.json
```

#### 1.3 API调试
```bash
# 查看详细请求日志
uvicorn app.main:app --reload --log-level debug

# 使用curl测试API
curl -v -X POST http://localhost:8080/api/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"message": "test"}'
```

### 2. 前端调试

#### 2.1 浏览器调试
```javascript
// 在代码中添加断点
debugger;

// 使用console调试
console.log('Debug info:', data);
console.error('Error:', error);
```

#### 2.2 Vue DevTools
```bash
# 安装Vue DevTools浏览器扩展
# Chrome: Vue.js devtools
# Firefox: Vue.js devtools
```

#### 2.3 网络调试
```bash
# 查看网络请求
# 浏览器开发者工具 -> Network

# 查看Vite开发服务器日志
pnpm dev --debug
```

### 3. 常见调试场景

#### 3.1 CORS问题
```python
# 后端添加CORS配置
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

#### 3.2 API连接问题
```javascript
// 前端检查API配置
console.log('API Base URL:', import.meta.env.VITE_API_BASE_URL);

// 检查网络请求
fetch('/api/health')
  .then(response => console.log('API Status:', response.status))
  .catch(error => console.error('API Error:', error));
```

#### 3.3 数据库连接问题
```python
# 检查数据库连接
from app.database import engine

try:
    with engine.connect() as conn:
        result = conn.execute("SELECT 1")
        print("Database connected successfully")
except Exception as e:
    print(f"Database connection failed: {e}")
```

## ❓ 常见问题

### Q1: 后端启动失败
**A**: 检查以下项目：
```bash
# 1. 检查Python版本
python --version  # 应该是3.11+

# 2. 检查虚拟环境
which python  # 应该指向venv中的python

# 3. 检查依赖安装
pip list | grep fastapi

# 4. 检查端口占用
lsof -i :8080
```

### Q2: 前端启动失败
**A**: 检查以下项目：
```bash
# 1. 检查Node.js版本
node --version  # 应该是18+

# 2. 清理缓存
rm -rf node_modules package-lock.json
pnpm install

# 3. 检查端口占用
lsof -i :3000
```

### Q3: API调用失败
**A**: 检查以下项目：
```bash
# 1. 检查后端服务状态
curl http://localhost:8080/health

# 2. 检查CORS配置
# 浏览器开发者工具 -> Console

# 3. 检查API密钥配置
grep OPENAI_API_KEY backend/.env
```

### Q4: Artifacts渲染失败
**A**: 检查以下项目：
```bash
# 1. 检查renderer.html文件
ls frontend/public/renderer.html

# 2. 检查离线依赖
ls frontend/public/vendor/

# 3. 检查浏览器控制台错误
# 开发者工具 -> Console
```

### Q5: 数据库问题
**A**: 重置数据库：
```bash
# SQLite (开发环境)
rm backend/artifacts_chat.db

# 重新初始化
cd backend
python -c "
from app.database import engine
from app.models import Base
Base.metadata.create_all(bind=engine)
"
```

## 🔗 有用的链接

- **FastAPI文档**: https://fastapi.tiangolo.com/
- **Vue.js文档**: https://vuejs.org/
- **Arco Design Vue**: https://arco.design/vue
- **Pinia文档**: https://pinia.vuejs.org/
- **Vite文档**: https://vitejs.dev/

---

**开发愉快！** 🚀 如有问题，请查看项目文档或提交Issue。
