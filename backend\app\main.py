"""
FastAPI main application for Artifacts Chat
"""
from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.api import chat, artifacts, sessions, knowledge, enhanced_chat
from app.core.config import settings

# Create FastAPI app
app = FastAPI(
    title="Artifacts Chat API",
    description="Backend API for Artifacts Chat - A local Claude.ai clone with Artifacts support",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(chat.router, prefix="/api/chat", tags=["chat"])
app.include_router(enhanced_chat.router, prefix="/api/enhanced-chat", tags=["enhanced-chat"])
app.include_router(artifacts.router, prefix="/api/artifacts", tags=["artifacts"])
app.include_router(sessions.router, prefix="/api/sessions", tags=["sessions"])
app.include_router(knowledge.router, prefix="/api/knowledge", tags=["knowledge"])

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Artifacts Chat API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
