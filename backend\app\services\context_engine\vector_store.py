"""
Vector Store Manager for Context Engine

Supports both Qdrant and Chroma vector databases with unified interface.
"""
import asyncio
import uuid
from typing import List, Dict, Any, Optional, Tuple
from abc import ABC, abstractmethod

from llama_index.vector_stores import VectorStore
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.vector_stores.chroma import ChromaVectorStore
from llama_index.schema import NodeWithScore, TextNode

from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import Distance, VectorParams
import chromadb

from app.core.context_config import context_settings, VectorDBType
import structlog

logger = structlog.get_logger(__name__)

class BaseVectorStore(ABC):
    """Abstract base class for vector stores"""
    
    @abstractmethod
    async def create_collection(self, collection_name: str, dimension: int) -> bool:
        """Create a new collection"""
        pass
    
    @abstractmethod
    async def delete_collection(self, collection_name: str) -> bool:
        """Delete a collection"""
        pass
    
    @abstractmethod
    async def upsert_vectors(
        self, 
        collection_name: str, 
        vectors: List[List[float]], 
        payloads: List[Dict[str, Any]],
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """Insert or update vectors"""
        pass
    
    @abstractmethod
    async def search_vectors(
        self, 
        collection_name: str, 
        query_vector: List[float], 
        limit: int = 10,
        filter_conditions: Optional[Dict[str, Any]] = None
    ) -> List[Tuple[str, float, Dict[str, Any]]]:
        """Search for similar vectors"""
        pass
    
    @abstractmethod
    async def delete_vectors(self, collection_name: str, ids: List[str]) -> bool:
        """Delete vectors by IDs"""
        pass
    
    @abstractmethod
    async def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """Get collection information"""
        pass

class QdrantVectorStoreManager(BaseVectorStore):
    """Qdrant vector store implementation"""
    
    def __init__(self):
        self.client = QdrantClient(
            url=context_settings.VECTOR_DB_URL,
            api_key=context_settings.VECTOR_DB_API_KEY,
            prefer_grpc=context_settings.QDRANT_PREFER_GRPC,
            grpc_port=context_settings.QDRANT_GRPC_PORT
        )
        logger.info("Initialized Qdrant client", url=context_settings.VECTOR_DB_URL)
    
    async def create_collection(self, collection_name: str, dimension: int) -> bool:
        """Create a Qdrant collection"""
        try:
            # Check if collection exists
            collections = await asyncio.to_thread(self.client.get_collections)
            existing_names = [col.name for col in collections.collections]
            
            if collection_name in existing_names:
                logger.info("Collection already exists", collection=collection_name)
                return True
            
            # Create collection
            await asyncio.to_thread(
                self.client.create_collection,
                collection_name=collection_name,
                vectors_config=VectorParams(
                    size=dimension,
                    distance=Distance.COSINE
                )
            )
            
            logger.info("Created Qdrant collection", collection=collection_name, dimension=dimension)
            return True
            
        except Exception as e:
            logger.error("Failed to create collection", collection=collection_name, error=str(e))
            return False
    
    async def delete_collection(self, collection_name: str) -> bool:
        """Delete a Qdrant collection"""
        try:
            await asyncio.to_thread(self.client.delete_collection, collection_name)
            logger.info("Deleted Qdrant collection", collection=collection_name)
            return True
        except Exception as e:
            logger.error("Failed to delete collection", collection=collection_name, error=str(e))
            return False
    
    async def upsert_vectors(
        self, 
        collection_name: str, 
        vectors: List[List[float]], 
        payloads: List[Dict[str, Any]],
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """Upsert vectors to Qdrant"""
        try:
            if ids is None:
                ids = [str(uuid.uuid4()) for _ in vectors]
            
            points = [
                models.PointStruct(
                    id=point_id,
                    vector=vector,
                    payload=payload
                )
                for point_id, vector, payload in zip(ids, vectors, payloads)
            ]
            
            await asyncio.to_thread(
                self.client.upsert,
                collection_name=collection_name,
                points=points
            )
            
            logger.info("Upserted vectors to Qdrant", collection=collection_name, count=len(vectors))
            return ids
            
        except Exception as e:
            logger.error("Failed to upsert vectors", collection=collection_name, error=str(e))
            raise
    
    async def search_vectors(
        self, 
        collection_name: str, 
        query_vector: List[float], 
        limit: int = 10,
        filter_conditions: Optional[Dict[str, Any]] = None
    ) -> List[Tuple[str, float, Dict[str, Any]]]:
        """Search vectors in Qdrant"""
        try:
            search_result = await asyncio.to_thread(
                self.client.search,
                collection_name=collection_name,
                query_vector=query_vector,
                limit=limit,
                query_filter=models.Filter(**filter_conditions) if filter_conditions else None
            )
            
            results = [
                (str(hit.id), hit.score, hit.payload or {})
                for hit in search_result
            ]
            
            logger.info("Searched vectors in Qdrant", collection=collection_name, results=len(results))
            return results
            
        except Exception as e:
            logger.error("Failed to search vectors", collection=collection_name, error=str(e))
            return []
    
    async def delete_vectors(self, collection_name: str, ids: List[str]) -> bool:
        """Delete vectors from Qdrant"""
        try:
            await asyncio.to_thread(
                self.client.delete,
                collection_name=collection_name,
                points_selector=models.PointIdsList(points=ids)
            )
            logger.info("Deleted vectors from Qdrant", collection=collection_name, count=len(ids))
            return True
        except Exception as e:
            logger.error("Failed to delete vectors", collection=collection_name, error=str(e))
            return False
    
    async def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """Get Qdrant collection info"""
        try:
            info = await asyncio.to_thread(self.client.get_collection, collection_name)
            return {
                "name": collection_name,
                "vectors_count": info.vectors_count,
                "indexed_vectors_count": info.indexed_vectors_count,
                "points_count": info.points_count,
                "segments_count": info.segments_count,
                "config": {
                    "params": info.config.params.dict() if info.config.params else {},
                    "hnsw_config": info.config.hnsw_config.dict() if info.config.hnsw_config else {},
                    "optimizer_config": info.config.optimizer_config.dict() if info.config.optimizer_config else {},
                }
            }
        except Exception as e:
            logger.error("Failed to get collection info", collection=collection_name, error=str(e))
            return {}

class ChromaVectorStoreManager(BaseVectorStore):
    """Chroma vector store implementation"""
    
    def __init__(self):
        self.client = chromadb.PersistentClient(
            path=context_settings.CHROMA_PERSIST_DIRECTORY
        )
        logger.info("Initialized Chroma client", path=context_settings.CHROMA_PERSIST_DIRECTORY)
    
    async def create_collection(self, collection_name: str, dimension: int) -> bool:
        """Create a Chroma collection"""
        try:
            # Chroma creates collections automatically, just verify it works
            collection = self.client.get_or_create_collection(
                name=collection_name,
                metadata={"dimension": dimension}
            )
            logger.info("Created/verified Chroma collection", collection=collection_name)
            return True
        except Exception as e:
            logger.error("Failed to create collection", collection=collection_name, error=str(e))
            return False
    
    async def delete_collection(self, collection_name: str) -> bool:
        """Delete a Chroma collection"""
        try:
            self.client.delete_collection(name=collection_name)
            logger.info("Deleted Chroma collection", collection=collection_name)
            return True
        except Exception as e:
            logger.error("Failed to delete collection", collection=collection_name, error=str(e))
            return False
    
    async def upsert_vectors(
        self, 
        collection_name: str, 
        vectors: List[List[float]], 
        payloads: List[Dict[str, Any]],
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """Upsert vectors to Chroma"""
        try:
            if ids is None:
                ids = [str(uuid.uuid4()) for _ in vectors]
            
            collection = self.client.get_collection(collection_name)
            
            # Convert payloads to Chroma format
            documents = [payload.get("content", "") for payload in payloads]
            metadatas = [
                {k: v for k, v in payload.items() if k != "content"}
                for payload in payloads
            ]
            
            collection.upsert(
                ids=ids,
                embeddings=vectors,
                documents=documents,
                metadatas=metadatas
            )
            
            logger.info("Upserted vectors to Chroma", collection=collection_name, count=len(vectors))
            return ids
            
        except Exception as e:
            logger.error("Failed to upsert vectors", collection=collection_name, error=str(e))
            raise
    
    async def search_vectors(
        self, 
        collection_name: str, 
        query_vector: List[float], 
        limit: int = 10,
        filter_conditions: Optional[Dict[str, Any]] = None
    ) -> List[Tuple[str, float, Dict[str, Any]]]:
        """Search vectors in Chroma"""
        try:
            collection = self.client.get_collection(collection_name)
            
            results = collection.query(
                query_embeddings=[query_vector],
                n_results=limit,
                where=filter_conditions
            )
            
            # Convert Chroma results to unified format
            formatted_results = []
            if results["ids"] and results["ids"][0]:
                for i, doc_id in enumerate(results["ids"][0]):
                    score = 1.0 - results["distances"][0][i]  # Convert distance to similarity
                    metadata = results["metadatas"][0][i] if results["metadatas"][0] else {}
                    if results["documents"][0]:
                        metadata["content"] = results["documents"][0][i]
                    
                    formatted_results.append((doc_id, score, metadata))
            
            logger.info("Searched vectors in Chroma", collection=collection_name, results=len(formatted_results))
            return formatted_results
            
        except Exception as e:
            logger.error("Failed to search vectors", collection=collection_name, error=str(e))
            return []
    
    async def delete_vectors(self, collection_name: str, ids: List[str]) -> bool:
        """Delete vectors from Chroma"""
        try:
            collection = self.client.get_collection(collection_name)
            collection.delete(ids=ids)
            logger.info("Deleted vectors from Chroma", collection=collection_name, count=len(ids))
            return True
        except Exception as e:
            logger.error("Failed to delete vectors", collection=collection_name, error=str(e))
            return False
    
    async def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """Get Chroma collection info"""
        try:
            collection = self.client.get_collection(collection_name)
            count = collection.count()
            return {
                "name": collection_name,
                "count": count,
                "metadata": collection.metadata
            }
        except Exception as e:
            logger.error("Failed to get collection info", collection=collection_name, error=str(e))
            return {}

class VectorStoreManager:
    """Unified vector store manager"""
    
    def __init__(self):
        if context_settings.VECTOR_DB_TYPE == VectorDBType.QDRANT:
            self.store = QdrantVectorStoreManager()
        elif context_settings.VECTOR_DB_TYPE == VectorDBType.CHROMA:
            self.store = ChromaVectorStoreManager()
        else:
            raise ValueError(f"Unsupported vector DB type: {context_settings.VECTOR_DB_TYPE}")
        
        self.default_collection = context_settings.vector_collection_name
        logger.info("Initialized VectorStoreManager", type=context_settings.VECTOR_DB_TYPE)
    
    async def initialize(self) -> bool:
        """Initialize the vector store"""
        try:
            # Create default collection
            success = await self.store.create_collection(
                self.default_collection, 
                context_settings.EMBEDDING_DIMENSION
            )
            
            if success:
                logger.info("Vector store initialized successfully")
                return True
            else:
                logger.error("Failed to initialize vector store")
                return False
                
        except Exception as e:
            logger.error("Error initializing vector store", error=str(e))
            return False
    
    async def add_documents(
        self, 
        documents: List[Dict[str, Any]], 
        vectors: List[List[float]],
        collection_name: Optional[str] = None
    ) -> List[str]:
        """Add documents with their vectors"""
        collection_name = collection_name or self.default_collection
        return await self.store.upsert_vectors(collection_name, vectors, documents)
    
    async def search_similar(
        self, 
        query_vector: List[float], 
        limit: int = 10,
        collection_name: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Tuple[str, float, Dict[str, Any]]]:
        """Search for similar documents"""
        collection_name = collection_name or self.default_collection
        return await self.store.search_vectors(collection_name, query_vector, limit, filters)
    
    async def delete_documents(
        self, 
        document_ids: List[str], 
        collection_name: Optional[str] = None
    ) -> bool:
        """Delete documents by IDs"""
        collection_name = collection_name or self.default_collection
        return await self.store.delete_vectors(collection_name, document_ids)
    
    async def get_stats(self, collection_name: Optional[str] = None) -> Dict[str, Any]:
        """Get collection statistics"""
        collection_name = collection_name or self.default_collection
        return await self.store.get_collection_info(collection_name)
