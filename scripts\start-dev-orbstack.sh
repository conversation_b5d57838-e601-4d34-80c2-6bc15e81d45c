#!/bin/bash

# Context Engine Development Startup Script for OrbStack
# This script sets up the development environment optimized for OrbStack

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="Context Engine"
COMPOSE_FILE="docker-compose.dev.yml"
BACKEND_DIR="backend"
FRONTEND_DIR="frontend"

echo -e "${BLUE}🚀 Starting ${PROJECT_NAME} Development Environment with OrbStack${NC}"
echo "=================================================="

# Check if OrbStack is running
check_orbstack() {
    echo -e "${YELLOW}📋 Checking OrbStack status...${NC}"
    
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker command not found. Please make sure OrbStack is installed and running.${NC}"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        echo -e "${RED}❌ Docker daemon is not running. Please start OrbStack.${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ OrbStack is running${NC}"
}

# Create necessary directories
create_directories() {
    echo -e "${YELLOW}📁 Creating necessary directories...${NC}"
    
    mkdir -p knowledge/{frontend,backend,examples}
    mkdir -p backend/logs
    mkdir -p data/{chroma,models}
    mkdir -p monitoring/{prometheus,grafana}
    
    echo -e "${GREEN}✅ Directories created${NC}"
}

# Create environment file if it doesn't exist
create_env_file() {
    if [ ! -f .env ]; then
        echo -e "${YELLOW}📝 Creating .env file...${NC}"
        cat > .env << EOF
# Environment
ENVIRONMENT=development
DEBUG=true

# Database
DATABASE_URL=****************************************************/context_engine

# Vector Database
VECTOR_DB_TYPE=qdrant
VECTOR_DB_URL=http://qdrant:6333
VECTOR_DB_COLLECTION_PREFIX=context_engine

# Redis
REDIS_URL=redis://redis:6379

# Embedding Model
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
EMBEDDING_DEVICE=cpu

# LLM APIs (optional - add your keys)
OPENAI_API_KEY=
ANTHROPIC_API_KEY=

# Security
SECRET_KEY=dev-secret-key-change-in-production

# Monitoring
ENABLE_METRICS=true
LOG_LEVEL=INFO
EOF
        echo -e "${GREEN}✅ .env file created${NC}"
    else
        echo -e "${GREEN}✅ .env file already exists${NC}"
    fi
}

# Create monitoring configuration
create_monitoring_config() {
    echo -e "${YELLOW}📊 Creating monitoring configuration...${NC}"
    
    # Prometheus config
    mkdir -p monitoring
    cat > monitoring/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'context-engine'
    static_configs:
      - targets: ['context-engine:8080']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'qdrant'
    static_configs:
      - targets: ['qdrant:6333']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
EOF
    
    echo -e "${GREEN}✅ Monitoring configuration created${NC}"
}

# Start core services
start_core_services() {
    echo -e "${YELLOW}🐳 Starting core services with OrbStack...${NC}"
    
    # Stop any existing containers
    docker-compose -f $COMPOSE_FILE down --remove-orphans
    
    # Start core infrastructure services
    docker-compose -f $COMPOSE_FILE up -d qdrant redis postgres
    
    echo -e "${YELLOW}⏳ Waiting for services to be ready...${NC}"
    
    # Wait for services to be healthy
    echo "Waiting for Qdrant..."
    until docker-compose -f $COMPOSE_FILE exec -T qdrant wget --spider -q http://localhost:6333/health; do
        echo -n "."
        sleep 2
    done
    echo -e "\n${GREEN}✅ Qdrant is ready${NC}"
    
    echo "Waiting for Redis..."
    until docker-compose -f $COMPOSE_FILE exec -T redis redis-cli ping; do
        echo -n "."
        sleep 1
    done
    echo -e "\n${GREEN}✅ Redis is ready${NC}"
    
    echo "Waiting for PostgreSQL..."
    until docker-compose -f $COMPOSE_FILE exec -T postgres pg_isready -U context_user -d context_engine; do
        echo -n "."
        sleep 2
    done
    echo -e "\n${GREEN}✅ PostgreSQL is ready${NC}"
}

# Install backend dependencies
setup_backend() {
    echo -e "${YELLOW}🐍 Setting up backend environment...${NC}"
    
    cd $BACKEND_DIR
    
    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        echo "Creating Python virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Install dependencies
    echo "Installing Python dependencies..."
    pip install --upgrade pip
    pip install -r requirements.txt
    
    echo -e "${GREEN}✅ Backend environment ready${NC}"
    cd ..
}

# Install frontend dependencies
setup_frontend() {
    echo -e "${YELLOW}📦 Setting up frontend environment...${NC}"
    
    cd $FRONTEND_DIR
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        echo "Installing frontend dependencies..."
        if command -v pnpm &> /dev/null; then
            pnpm install
        elif command -v yarn &> /dev/null; then
            yarn install
        else
            npm install
        fi
    else
        echo -e "${GREEN}✅ Frontend dependencies already installed${NC}"
    fi
    
    cd ..
}

# Start development servers
start_dev_servers() {
    echo -e "${YELLOW}🚀 Starting development servers...${NC}"
    
    # Start backend in background
    echo "Starting backend server..."
    cd $BACKEND_DIR
    source venv/bin/activate
    uvicorn app.main:app --reload --host 0.0.0.0 --port 8080 &
    BACKEND_PID=$!
    cd ..
    
    # Wait a moment for backend to start
    sleep 3
    
    # Start frontend in background
    echo "Starting frontend server..."
    cd $FRONTEND_DIR
    if command -v pnpm &> /dev/null; then
        pnpm dev &
    elif command -v yarn &> /dev/null; then
        yarn dev &
    else
        npm run dev &
    fi
    FRONTEND_PID=$!
    cd ..
    
    # Store PIDs for cleanup
    echo $BACKEND_PID > .backend.pid
    echo $FRONTEND_PID > .frontend.pid
}

# Show status and URLs
show_status() {
    echo ""
    echo -e "${GREEN}🎉 Development environment is ready!${NC}"
    echo "=================================================="
    echo -e "${BLUE}📱 Frontend:${NC} http://localhost:5173"
    echo -e "${BLUE}🔧 Backend API:${NC} http://localhost:8080"
    echo -e "${BLUE}📚 API Docs:${NC} http://localhost:8080/docs"
    echo -e "${BLUE}🔍 Qdrant UI:${NC} http://localhost:6333/dashboard"
    echo -e "${BLUE}📊 Prometheus:${NC} http://localhost:9090 (if monitoring enabled)"
    echo -e "${BLUE}📈 Grafana:${NC} http://localhost:3000 (if monitoring enabled)"
    echo ""
    echo -e "${YELLOW}💡 Tips:${NC}"
    echo "• Use 'docker-compose -f $COMPOSE_FILE logs -f' to view container logs"
    echo "• Use './scripts/stop-dev.sh' to stop all services"
    echo "• Check service health with 'docker-compose -f $COMPOSE_FILE ps'"
    echo ""
    echo -e "${YELLOW}🔧 OrbStack specific:${NC}"
    echo "• Services are accessible via localhost (OrbStack handles networking)"
    echo "• Use OrbStack UI to monitor container resources"
    echo "• File changes are automatically synced (no volume performance issues)"
}

# Cleanup function
cleanup() {
    echo -e "\n${YELLOW}🧹 Cleaning up...${NC}"
    
    # Kill development servers
    if [ -f .backend.pid ]; then
        kill $(cat .backend.pid) 2>/dev/null || true
        rm .backend.pid
    fi
    
    if [ -f .frontend.pid ]; then
        kill $(cat .frontend.pid) 2>/dev/null || true
        rm .frontend.pid
    fi
    
    echo -e "${GREEN}✅ Cleanup completed${NC}"
}

# Set up signal handlers
trap cleanup EXIT INT TERM

# Main execution
main() {
    check_orbstack
    create_directories
    create_env_file
    create_monitoring_config
    start_core_services
    setup_backend
    setup_frontend
    start_dev_servers
    show_status
    
    # Keep script running
    echo -e "${YELLOW}Press Ctrl+C to stop all services${NC}"
    wait
}

# Run main function
main
