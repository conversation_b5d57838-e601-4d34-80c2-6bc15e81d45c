import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface User {
  id: number
  username: string
  email: string
  avatar?: string
}

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const isLoading = ref(false)

  // Getters
  const isAuthenticated = computed(() => !!user.value && !!token.value)
  const userDisplayName = computed(() => user.value?.username || user.value?.email || 'Guest')

  // Actions
  const setUser = (userData: User) => {
    user.value = userData
  }

  const setToken = (tokenValue: string) => {
    token.value = tokenValue
    // 存储到 localStorage
    localStorage.setItem('auth_token', tokenValue)
  }

  const clearAuth = () => {
    user.value = null
    token.value = null
    localStorage.removeItem('auth_token')
  }

  const login = async (email: string, password: string) => {
    isLoading.value = true
    try {
      // 这里应该调用实际的登录 API
      // 目前使用模拟数据
      const mockUser: User = {
        id: 1,
        username: 'demo_user',
        email: email,
        avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${email}`
      }
      
      const mockToken = 'mock_jwt_token_' + Date.now()
      
      setUser(mockUser)
      setToken(mockToken)
      
      return { success: true }
    } catch (error) {
      console.error('Login failed:', error)
      return { success: false, error: 'Login failed' }
    } finally {
      isLoading.value = false
    }
  }

  const logout = () => {
    clearAuth()
  }

  const initAuth = () => {
    // 从 localStorage 恢复认证状态
    const savedToken = localStorage.getItem('auth_token')
    if (savedToken) {
      token.value = savedToken
      // 这里应该验证 token 并获取用户信息
      // 目前使用模拟数据
      const mockUser: User = {
        id: 1,
        username: 'demo_user',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=<EMAIL>'
      }
      setUser(mockUser)
    }
  }

  const refreshToken = async () => {
    // 刷新 token 的逻辑
    if (!token.value) return false
    
    try {
      // 这里应该调用刷新 token 的 API
      return true
    } catch (error) {
      console.error('Token refresh failed:', error)
      clearAuth()
      return false
    }
  }

  // 初始化认证状态
  initAuth()

  return {
    // State
    user,
    token,
    isLoading,
    
    // Getters
    isAuthenticated,
    userDisplayName,
    
    // Actions
    setUser,
    setToken,
    clearAuth,
    login,
    logout,
    initAuth,
    refreshToken
  }
})
