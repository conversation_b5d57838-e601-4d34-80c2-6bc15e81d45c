# 🔌 智能上下文引擎 API 接口规范

## 概述

智能上下文引擎提供RESTful API接口，支持知识库管理、智能检索、聊天增强等核心功能。

## 🌐 基础信息

- **Base URL**: `http://localhost:8080/api`
- **API版本**: `v1`
- **认证方式**: <PERSON><PERSON> (JWT)
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## 📋 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "uuid"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {}
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "uuid"
}
```

## 🧠 智能聊天 API

### 1. 增强聊天完成
**POST** `/enhanced-chat/completions`

使用智能上下文增强的聊天完成接口。

#### 请求参数
```json
{
  "messages": [
    {
      "role": "user",
      "content": "如何创建Vue组件？"
    }
  ],
  "model": "gpt-3.5-turbo",
  "stream": false,
  "enable_context": true,
  "context_config": {
    "max_results": 5,
    "include_code": true,
    "include_docs": true,
    "frameworks": ["vue"],
    "languages": ["javascript"]
  },
  "user_preferences": {
    "detailed_explanations": true,
    "prefer_code_examples": true,
    "beginner_friendly": false
  }
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": "chatcmpl-123",
    "object": "chat.completion",
    "created": 1677652288,
    "model": "gpt-3.5-turbo",
    "choices": [
      {
        "index": 0,
        "message": {
          "role": "assistant",
          "content": "基于提供的Vue技术文档，我来详细解释如何创建Vue组件..."
        },
        "finish_reason": "stop"
      }
    ],
    "usage": {
      "prompt_tokens": 150,
      "completion_tokens": 300,
      "total_tokens": 450
    },
    "context_metadata": {
      "contexts_used": 3,
      "processing_time_ms": 45,
      "query_type": "implementation",
      "template_used": "implementation"
    }
  }
}
```

### 2. 流式聊天完成
**POST** `/enhanced-chat/completions` (stream=true)

支持服务器发送事件(SSE)的流式响应。

#### 流式响应格式
```
data: {"choices":[{"delta":{"content":"基于"}}]}

data: {"choices":[{"delta":{"content":"提供的"}}]}

data: {"choices":[{"delta":{"content":"Vue技术文档"}}]}

data: [DONE]
```

### 3. 获取可用模型
**GET** `/enhanced-chat/models`

获取系统支持的LLM模型列表。

#### 响应示例
```json
{
  "success": true,
  "data": {
    "models": [
      {
        "id": "gpt-3.5-turbo",
        "provider": "openai",
        "name": "GPT-3.5 Turbo",
        "description": "快速、经济的对话模型",
        "max_tokens": 4096,
        "available": true
      },
      {
        "id": "claude-3-sonnet",
        "provider": "anthropic",
        "name": "Claude 3 Sonnet",
        "description": "平衡性能和速度的模型",
        "max_tokens": 200000,
        "available": true
      }
    ]
  }
}
```

## 📚 知识库管理 API

### 1. 获取集合列表
**GET** `/knowledge/collections`

获取知识库集合列表。

#### 查询参数
- `page`: 页码 (默认: 1)
- `size`: 每页大小 (默认: 20)
- `category`: 分类筛选
- `search`: 搜索关键词

#### 响应示例
```json
{
  "success": true,
  "data": {
    "collections": [
      {
        "id": "uuid",
        "name": "frontend-docs",
        "description": "前端技术文档",
        "category": "frontend",
        "document_count": 150,
        "code_example_count": 80,
        "total_size_bytes": 5242880,
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 5,
      "pages": 1
    }
  }
}
```

### 2. 创建集合
**POST** `/knowledge/collections`

创建新的知识库集合。

#### 请求参数
```json
{
  "name": "backend-docs",
  "description": "后端技术文档",
  "category": "backend",
  "subcategory": "api",
  "settings": {
    "auto_update": true,
    "public": true
  }
}
```

### 3. 智能搜索
**POST** `/knowledge/intelligent-search`

执行智能混合检索。

#### 请求参数
```json
{
  "query": "vue component lifecycle",
  "max_results": 10,
  "include_code": true,
  "include_docs": true,
  "collections": ["frontend-docs"],
  "frameworks": ["vue"],
  "languages": ["javascript"],
  "filters": {
    "quality_score": {"min": 0.7},
    "created_after": "2024-01-01T00:00:00Z"
  }
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "query": "vue component lifecycle",
    "results": [
      {
        "id": "uuid",
        "title": "Vue组件生命周期详解",
        "content": "Vue组件的生命周期包括创建、挂载、更新和销毁四个阶段...",
        "type": "document",
        "score": 0.95,
        "source": {
          "collection": "frontend-docs",
          "framework": "vue",
          "language": "javascript"
        },
        "metadata": {
          "file_type": "markdown",
          "category": "tutorial",
          "tags": ["vue", "lifecycle", "component"]
        }
      }
    ],
    "total_found": 25,
    "processing_time_ms": 45,
    "suggestions": [
      "vue component methods",
      "vue component props",
      "vue component events"
    ]
  }
}
```

### 4. 上传文档
**POST** `/knowledge/documents/upload`

上传文档到知识库。

#### 请求参数 (multipart/form-data)
- `file`: 文档文件
- `collection_name`: 集合名称
- `metadata`: JSON格式的元数据

#### 响应示例
```json
{
  "success": true,
  "data": {
    "document_id": "uuid",
    "title": "Vue组件开发指南",
    "file_size": 1024000,
    "chunks_created": 15,
    "processing_time_ms": 2500
  }
}
```

### 5. 批量上传
**POST** `/knowledge/bulk-upload`

批量上传多个文档。

#### 请求参数 (multipart/form-data)
- `files`: 多个文档文件
- `collection_name`: 集合名称
- `auto_categorize`: 是否自动分类

## 🎯 提示词优化 API

### 1. 优化提示词
**POST** `/enhanced-chat/optimize-prompt`

基于查询类型和用户偏好优化提示词。

#### 请求参数
```json
{
  "query": "如何调试Vue组件错误？",
  "user_preferences": {
    "detailed_explanations": true,
    "prefer_code_examples": true,
    "beginner_friendly": false
  },
  "context_config": {
    "max_results": 5,
    "frameworks": ["vue"]
  }
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "optimized_prompt": "你是一个专业的技术问题解决专家...",
    "query_type": "debugging",
    "template_used": "debugging",
    "contexts_used": 3,
    "metadata": {
      "optimization_applied": true,
      "user_preferences_applied": {
        "detailed_explanations": true,
        "prefer_code_examples": true
      }
    }
  }
}
```

### 2. 上下文搜索
**POST** `/enhanced-chat/context-search`

搜索相关上下文（用于调试和测试）。

#### 请求参数
```json
{
  "query": "vue component",
  "max_results": 5,
  "include_code": true,
  "include_docs": true
}
```

## 📊 监控和统计 API

### 1. 系统健康检查
**GET** `/health`

检查系统健康状态。

#### 响应示例
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-15T10:30:00Z",
    "uptime_seconds": 86400,
    "version": "1.0.0",
    "services": {
      "database": "healthy",
      "vector_store": "healthy",
      "llm_providers": {
        "openai": "healthy",
        "claude": "healthy"
      },
      "cache": "healthy"
    }
  }
}
```

### 2. 获取统计信息
**GET** `/knowledge/stats`

获取知识库统计信息。

#### 响应示例
```json
{
  "success": true,
  "data": {
    "collections": {
      "total": 5,
      "by_category": {
        "frontend": 2,
        "backend": 2,
        "devops": 1
      }
    },
    "documents": {
      "total": 1250,
      "total_size_bytes": 52428800,
      "by_type": {
        "markdown": 800,
        "pdf": 300,
        "code": 150
      }
    },
    "queries": {
      "total_today": 150,
      "avg_response_time_ms": 45,
      "success_rate": 0.98
    }
  }
}
```

### 3. 获取性能指标
**GET** `/metrics`

获取Prometheus格式的性能指标。

#### 响应示例 (text/plain)
```
# HELP http_requests_total Total HTTP requests
# TYPE http_requests_total counter
http_requests_total{method="GET",endpoint="/api/health"} 1250

# HELP http_request_duration_seconds HTTP request duration
# TYPE http_request_duration_seconds histogram
http_request_duration_seconds_bucket{le="0.1"} 1000
http_request_duration_seconds_bucket{le="0.5"} 1200
```

## 🔒 认证和授权

### 1. 获取访问令牌
**POST** `/auth/token`

获取API访问令牌。

#### 请求参数
```json
{
  "username": "<EMAIL>",
  "password": "password"
}
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIs...",
    "token_type": "bearer",
    "expires_in": 3600,
    "refresh_token": "eyJhbGciOiJIUzI1NiIs..."
  }
}
```

### 2. 刷新令牌
**POST** `/auth/refresh`

使用刷新令牌获取新的访问令牌。

## 📝 错误代码

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| `INVALID_REQUEST` | 400 | 请求参数无效 |
| `UNAUTHORIZED` | 401 | 未授权访问 |
| `FORBIDDEN` | 403 | 权限不足 |
| `NOT_FOUND` | 404 | 资源不存在 |
| `RATE_LIMITED` | 429 | 请求频率超限 |
| `INTERNAL_ERROR` | 500 | 内部服务器错误 |
| `SERVICE_UNAVAILABLE` | 503 | 服务不可用 |

## 🔄 API版本控制

- 当前版本: `v1`
- 版本指定: 通过URL路径 `/api/v1/` 或 Header `API-Version: v1`
- 向后兼容: 保证同一主版本内的向后兼容性
- 废弃通知: 新版本发布前30天通知废弃计划

## 📈 限流策略

- **默认限制**: 每分钟100次请求
- **认证用户**: 每分钟500次请求
- **批量操作**: 每小时10次请求
- **文件上传**: 每小时50MB

## 🔧 SDK和工具

### Python SDK
```python
from artifacts_chat import ArtifactsClient

client = ArtifactsClient(
    base_url="http://localhost:8080/api",
    api_key="your-api-key"
)

# 智能搜索
results = await client.search("vue component lifecycle")

# 增强聊天
response = await client.chat_completion(
    messages=[{"role": "user", "content": "如何创建Vue组件？"}],
    enable_context=True
)
```

### JavaScript SDK
```javascript
import { ArtifactsClient } from '@artifacts-chat/sdk';

const client = new ArtifactsClient({
  baseURL: 'http://localhost:8080/api',
  apiKey: 'your-api-key'
});

// 智能搜索
const results = await client.search('vue component lifecycle');

// 增强聊天
const response = await client.chatCompletion({
  messages: [{ role: 'user', content: '如何创建Vue组件？' }],
  enableContext: true
});
```

---

**智能上下文引擎 API** - 为AI对话提供精准的技术支持 🚀
