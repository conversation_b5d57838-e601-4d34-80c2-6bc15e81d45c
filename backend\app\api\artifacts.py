"""
Artifacts API endpoints
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from app.db.database import get_db
from app.schemas.artifact import ArtifactResponse, ArtifactCreate
from app.services.database import get_database_service

router = APIRouter()

@router.get("/", response_model=List[ArtifactResponse])
async def get_artifacts(
    message_id: Optional[int] = None,
    limit: int = 10,
    db: Session = Depends(get_db)
):
    """
    Get artifacts, optionally filtered by message_id
    """
    db_service = get_database_service(db)
    if message_id:
        artifacts = db_service.get_artifacts(message_id)
    else:
        artifacts = db_service.get_recent_artifacts(limit)
    return artifacts

@router.get("/{artifact_id}", response_model=ArtifactResponse)
async def get_artifact(
    artifact_id: int,
    db: Session = Depends(get_db)
):
    """
    Get artifact by ID
    """
    db_service = get_database_service(db)
    artifact = db_service.get_artifact(artifact_id)
    if not artifact:
        raise HTTPException(status_code=404, detail="Artifact not found")
    return artifact

@router.post("/", response_model=ArtifactResponse)
async def create_artifact(
    artifact: ArtifactCreate,
    db: Session = Depends(get_db)
):
    """
    Create new artifact
    """
    db_service = get_database_service(db)
    new_artifact = db_service.create_artifact(artifact)
    return new_artifact

@router.put("/{artifact_id}", response_model=ArtifactResponse)
async def update_artifact(
    artifact_id: int,
    artifact: ArtifactCreate,
    db: Session = Depends(get_db)
):
    """
    Update existing artifact
    """
    db_service = get_database_service(db)
    updated_artifact = db_service.update_artifact(
        artifact_id,
        type=artifact.type,
        code=artifact.code,
        title=artifact.title
    )
    if not updated_artifact:
        raise HTTPException(status_code=404, detail="Artifact not found")
    return updated_artifact

@router.delete("/{artifact_id}")
async def delete_artifact(
    artifact_id: int,
    db: Session = Depends(get_db)
):
    """
    Delete artifact
    """
    db_service = get_database_service(db)
    success = db_service.delete_artifact(artifact_id)
    if not success:
        raise HTTPException(status_code=404, detail="Artifact not found")
    return {"message": "Artifact deleted successfully"}
