<template>
  <div class="user-avatar">
    <a-dropdown v-if="authStore.isAuthenticated" trigger="hover">
      <div class="avatar-container">
        <a-avatar :size="32">
          <img v-if="authStore.user?.avatar" :src="authStore.user.avatar" :alt="authStore.userDisplayName" />
          <icon-user v-else />
        </a-avatar>
        <span class="username">{{ authStore.userDisplayName }}</span>
      </div>
      
      <template #content>
        <a-doption @click="showProfile">
          <template #icon>
            <icon-user />
          </template>
          个人资料
        </a-doption>
        <a-doption @click="showSettings">
          <template #icon>
            <icon-settings />
          </template>
          设置
        </a-doption>
        <a-doption @click="exportData">
          <template #icon>
            <icon-download />
          </template>
          导出数据
        </a-doption>
        <a-doption class="logout-option" @click="handleLogout">
          <template #icon>
            <icon-poweroff />
          </template>
          退出登录
        </a-doption>
      </template>
    </a-dropdown>
    
    <a-button v-else type="primary" @click="showLogin">
      <template #icon>
        <icon-user />
      </template>
      登录
    </a-button>
    
    <!-- 登录模态框 -->
    <LoginModal v-model:visible="loginVisible" @success="onLoginSuccess" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { 
  IconUser, 
  IconSettings, 
  IconDownload, 
  IconPoweroff 
} from '@arco-design/web-vue/es/icon'
import { Message, Modal } from '@arco-design/web-vue'
import { useAuthStore } from '@/stores/auth'
import { useChatStore } from '@/stores/chat'
import LoginModal from './LoginModal.vue'

const authStore = useAuthStore()
const chatStore = useChatStore()

const loginVisible = ref(false)

const showLogin = () => {
  loginVisible.value = true
}

const onLoginSuccess = () => {
  // 登录成功后的处理
  Message.success(`欢迎回来，${authStore.userDisplayName}！`)
}

const showProfile = () => {
  Message.info('个人资料功能开发中...')
}

const showSettings = () => {
  Message.info('设置功能开发中...')
}

const exportData = () => {
  try {
    // 导出聊天数据
    const data = {
      user: authStore.user,
      sessions: chatStore.sessions,
      exportTime: new Date().toISOString()
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { 
      type: 'application/json' 
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `artifacts-chat-data-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    Message.success('数据导出成功！')
  } catch (error) {
    Message.error('数据导出失败')
  }
}

const handleLogout = () => {
  Modal.confirm({
    title: '确认退出',
    content: '退出登录后，未保存的聊天记录将会丢失。确定要退出吗？',
    onOk: () => {
      authStore.logout()
      Message.success('已退出登录')
    }
  })
}
</script>

<style scoped>
.user-avatar {
  display: flex;
  align-items: center;
}

.avatar-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.avatar-container:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.username {
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.logout-option {
  color: #f53f3f;
}

.logout-option:hover {
  background-color: #ffece8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .username {
    display: none;
  }
  
  .avatar-container {
    padding: 4px;
  }
}
</style>
