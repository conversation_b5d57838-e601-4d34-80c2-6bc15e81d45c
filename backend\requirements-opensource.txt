# 开源文档处理依赖
# 所有依赖都是免费开源的，无需付费许可

# 核心依赖
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
sqlalchemy>=2.0.0
alembic>=1.13.0

# 数据库
sqlite3  # Python内置，无需安装

# 文档处理 - 开源免费
markdown>=3.5.0           # Markdown处理
beautifulsoup4>=4.12.0     # HTML解析
pyyaml>=6.0.1             # YAML处理
lxml>=4.9.0               # XML/HTML解析器

# PDF处理 - 开源免费
PyPDF2>=3.0.0             # PDF文本提取
# 或者使用 pdfplumber>=0.9.0  # 更强大的PDF处理

# Office文档处理 - 开源免费
python-docx>=1.1.0        # Word文档处理
openpyxl>=3.1.0           # Excel文档处理
python-pptx>=0.6.0        # PowerPoint文档处理

# 文本处理
nltk>=3.8.0               # 自然语言处理
jieba>=0.42.0             # 中文分词
textstat>=0.7.0           # 文本统计

# 向量化和机器学习 - 开源免费
sentence-transformers>=2.2.0  # 句子嵌入
transformers>=4.35.0      # Hugging Face模型
torch>=2.1.0              # PyTorch
numpy>=1.24.0             # 数值计算
scikit-learn>=1.3.0       # 机器学习

# 向量数据库 - 开源免费选项
chromadb>=0.4.0           # Chroma向量数据库
# 或者 qdrant-client>=1.6.0  # Qdrant客户端
# 或者 faiss-cpu>=1.7.0      # Facebook AI相似性搜索

# 异步和并发
aiofiles>=23.2.0          # 异步文件操作
asyncio                   # Python内置

# 日志和监控
structlog>=23.2.0         # 结构化日志
python-multipart>=0.0.6  # 文件上传支持

# 开发和测试
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
isort>=5.12.0
mypy>=1.7.0

# 可选的增强功能
# 图像处理（如果需要处理包含图像的文档）
# pillow>=10.1.0          # 图像处理

# 表格处理增强
# pandas>=2.1.0           # 数据分析
# tabulate>=0.9.0         # 表格格式化

# 代码处理
# pygments>=2.16.0        # 代码高亮
# tree-sitter>=0.20.0     # 代码解析

# 网络爬虫（如果需要处理网页）
# requests>=2.31.0        # HTTP请求
# scrapy>=2.11.0          # 网页爬虫

# 压缩文件处理
# zipfile                 # Python内置
# tarfile                 # Python内置
# py7zr>=0.20.0          # 7z文件处理

# 邮件处理（如果需要）
# email                   # Python内置
# mailbox                 # Python内置

# CSV处理
# csv                     # Python内置

# 配置文件处理
# configparser            # Python内置
# toml>=0.10.0           # TOML文件处理

# 正则表达式增强
# regex>=2023.10.0       # 更强大的正则表达式

# 字符编码检测
# chardet>=5.2.0         # 字符编码检测

# 文件类型检测
# python-magic>=0.4.0    # 文件类型检测

# 性能优化
# cython>=3.0.0          # Python C扩展
# numba>=0.58.0          # JIT编译器
