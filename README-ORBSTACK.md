# Context Engine - OrbStack Development Guide

## 🚀 Quick Start with OrbStack

OrbStack是macOS上Docker Desktop的优秀替代品，提供更好的性能和资源使用效率。

### 📋 前置要求

1. **OrbStack** - [下载安装](https://orbstack.dev/)
2. **Node.js** (v18+) - 前端开发
3. **Python** (v3.11+) - 后端开发
4. **pnpm** (推荐) 或 npm/yarn

### ⚡ 一键启动

```bash
# 克隆项目后，直接运行
chmod +x quick-start-orbstack.sh
./quick-start-orbstack.sh
```

这将启动核心服务：Qdrant、Redis、PostgreSQL

### 🔧 完整开发环境

```bash
# 启动完整开发环境（包括前后端服务器）
chmod +x scripts/start-dev-orbstack.sh
./scripts/start-dev-orbstack.sh
```

### 🏥 健康检查

```bash
# 检查所有服务状态
./scripts/health-check.sh
```

### 🛑 停止服务

```bash
# 停止所有服务
./scripts/stop-dev.sh

# 停止并清理数据卷
./scripts/stop-dev.sh --clean-volumes
```

## 🐳 OrbStack 优化特性

### 1. 网络优化
- 所有服务通过 `localhost` 访问，无需复杂的网络配置
- OrbStack 自动处理容器间通信
- 支持热重载，文件变更自动同步

### 2. 性能优化
- 使用 OrbStack 的原生文件系统，避免 Docker Desktop 的性能问题
- 更快的容器启动时间
- 更低的资源占用

### 3. 开发体验
- 集成 macOS 通知
- 更好的日志查看体验
- 原生的文件权限处理

## 📊 服务端口映射

| 服务 | 端口 | 用途 |
|------|------|------|
| Frontend | 5173 | Vue.js 开发服务器 |
| Backend API | 8080 | FastAPI 应用 |
| Qdrant | 6333 | 向量数据库 |
| Redis | 6379 | 缓存服务 |
| PostgreSQL | 5432 | 关系数据库 |
| Prometheus | 9090 | 监控指标 |
| Grafana | 3000 | 监控面板 |

## 🔗 快速访问链接

开发环境启动后，可以通过以下链接访问：

- **前端应用**: http://localhost:5173
- **后端API**: http://localhost:8080
- **API文档**: http://localhost:8080/docs
- **Qdrant控制台**: http://localhost:6333/dashboard
- **监控面板**: http://localhost:9090 (Prometheus)

## 🛠️ 开发工作流

### 1. 日常开发
```bash
# 启动开发环境
./scripts/start-dev-orbstack.sh

# 在另一个终端检查状态
./scripts/health-check.sh

# 开发完成后停止
./scripts/stop-dev.sh
```

### 2. 调试服务
```bash
# 查看特定服务日志
docker-compose -f docker-compose.dev.yml logs -f qdrant

# 重启特定服务
docker-compose -f docker-compose.dev.yml restart redis

# 进入容器调试
docker-compose -f docker-compose.dev.yml exec qdrant /bin/bash
```

### 3. 数据管理
```bash
# 备份数据
docker-compose -f docker-compose.dev.yml exec postgres pg_dump -U context_user context_engine > backup.sql

# 重置数据
./scripts/stop-dev.sh --clean-volumes
./scripts/start-dev-orbstack.sh
```

## 🔧 故障排除

### 常见问题

#### 1. 端口冲突
```bash
# 检查端口占用
lsof -i :6333
lsof -i :8080

# 杀死占用进程
kill -9 <PID>
```

#### 2. OrbStack 连接问题
```bash
# 重启 OrbStack
# 在 OrbStack 应用中点击重启

# 检查 Docker 状态
docker info
```

#### 3. 服务启动失败
```bash
# 查看详细日志
docker-compose -f docker-compose.dev.yml logs

# 重新构建镜像
docker-compose -f docker-compose.dev.yml build --no-cache
```

#### 4. 文件权限问题
```bash
# OrbStack 通常不会有权限问题，但如果遇到：
sudo chown -R $(whoami) ./data
sudo chown -R $(whoami) ./knowledge
```

### 性能优化建议

1. **资源分配**: 在 OrbStack 设置中适当分配 CPU 和内存
2. **文件监控**: 使用 `.dockerignore` 排除不必要的文件
3. **缓存利用**: 充分利用 Docker 层缓存
4. **并行启动**: 脚本已优化为并行启动服务

## 📈 监控和日志

### 实时监控
```bash
# 查看容器资源使用
docker stats

# 查看 OrbStack 资源使用
# 在 OrbStack 应用中查看

# 查看服务健康状态
docker-compose -f docker-compose.dev.yml ps
```

### 日志管理
```bash
# 查看所有服务日志
docker-compose -f docker-compose.dev.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.dev.yml logs -f qdrant

# 查看应用日志
tail -f backend/logs/app.log
```

## 🚀 生产部署

虽然这个配置主要用于开发，但可以作为生产部署的基础：

1. 修改 `docker-compose.prod.yml`
2. 使用环境变量管理敏感信息
3. 配置反向代理 (Nginx)
4. 设置监控和告警
5. 配置备份策略

## 💡 最佳实践

1. **定期更新**: 保持 OrbStack 和依赖的最新版本
2. **资源监控**: 定期检查资源使用情况
3. **数据备份**: 重要数据定期备份
4. **安全配置**: 生产环境使用强密码和 HTTPS
5. **日志轮转**: 配置日志轮转避免磁盘空间不足

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 使用 OrbStack 进行本地测试
4. 提交 Pull Request

## 📞 支持

如果遇到 OrbStack 相关问题：
- 查看 [OrbStack 文档](https://docs.orbstack.dev/)
- 检查 [OrbStack GitHub Issues](https://github.com/orbstack/orbstack/issues)
- 使用项目的健康检查脚本诊断问题
