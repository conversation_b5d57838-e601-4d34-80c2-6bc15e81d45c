@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 停止 Artifacts Chat 开发环境

echo 🛑 停止 Artifacts Chat 开发环境
echo ==================================================

REM 停止开发服务器
:stop_dev_servers
echo 🔌 停止开发服务器...

REM 停止后端服务器
if exist ".backend.pid" (
    echo 停止后端服务器...
    for /f %%i in (.backend.pid) do taskkill /pid %%i /f >nul 2>&1
    del .backend.pid >nul 2>&1
    echo ✅ 后端服务器已停止
)

REM 停止前端服务器
if exist ".frontend.pid" (
    echo 停止前端服务器...
    for /f %%i in (.frontend.pid) do taskkill /pid %%i /f >nul 2>&1
    del .frontend.pid >nul 2>&1
    echo ✅ 前端服务器已停止
)

REM 清理可能残留的进程
echo 清理残留进程...
taskkill /f /im "python.exe" /fi "WINDOWTITLE eq uvicorn*" >nul 2>&1
taskkill /f /im "node.exe" /fi "WINDOWTITLE eq *vite*" >nul 2>&1

REM 更精确的进程清理
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo csv ^| findstr "uvicorn"') do (
    taskkill /pid %%i /f >nul 2>&1
)

for /f "tokens=2" %%i in ('tasklist /fi "imagename eq node.exe" /fo csv ^| findstr "vite"') do (
    taskkill /pid %%i /f >nul 2>&1
)

REM 清理日志文件
if exist "backend.log" del backend.log >nul 2>&1
if exist "frontend.log" del frontend.log >nul 2>&1

REM 显示最终状态
:show_final_status
echo.
echo ✅ 所有服务已停止
echo.
echo 💡 提示:
echo • 重新启动: scripts\start-dev-local.bat
echo • 检查进程: tasklist ^| findstr "python node"

pause