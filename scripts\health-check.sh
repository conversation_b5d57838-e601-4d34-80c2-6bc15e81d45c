#!/bin/bash

# Health Check Script for Context Engine (OrbStack optimized)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🏥 Context Engine Health Check${NC}"
echo "=================================="

# Check OrbStack status
check_orbstack() {
    echo -e "${YELLOW}📋 Checking OrbStack...${NC}"
    
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker command not found${NC}"
        return 1
    fi
    
    if ! docker info &> /dev/null; then
        echo -e "${RED}❌ Docker daemon not running${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ OrbStack is running${NC}"
    return 0
}

# Check Docker services
check_docker_services() {
    echo -e "${YELLOW}🐳 Checking Docker services...${NC}"
    
    local services=("qdrant-dev" "redis-dev" "postgres-dev")
    local all_healthy=true
    
    for service in "${services[@]}"; do
        if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "$service.*Up"; then
            # Check if service has health check
            health_status=$(docker inspect --format='{{.State.Health.Status}}' "$service" 2>/dev/null || echo "no-healthcheck")
            
            if [ "$health_status" = "healthy" ] || [ "$health_status" = "no-healthcheck" ]; then
                echo -e "${GREEN}✅ $service: Running${NC}"
            else
                echo -e "${YELLOW}⚠️  $service: Running but not healthy ($health_status)${NC}"
                all_healthy=false
            fi
        else
            echo -e "${RED}❌ $service: Not running${NC}"
            all_healthy=false
        fi
    done
    
    return $([ "$all_healthy" = true ] && echo 0 || echo 1)
}

# Check individual service endpoints
check_service_endpoints() {
    echo -e "${YELLOW}🔗 Checking service endpoints...${NC}"
    
    local all_accessible=true
    
    # Check Qdrant
    if curl -s -f http://localhost:6333/ > /dev/null; then
        echo -e "${GREEN}✅ Qdrant API: Accessible${NC}"
    else
        echo -e "${RED}❌ Qdrant API: Not accessible${NC}"
        all_accessible=false
    fi
    
    # Check Redis
    if docker-compose -f docker-compose.dev.yml exec -T redis redis-cli ping > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Redis: Accessible${NC}"
    else
        echo -e "${RED}❌ Redis: Not accessible${NC}"
        all_accessible=false
    fi

    # Check PostgreSQL
    if docker-compose -f docker-compose.dev.yml exec -T postgres pg_isready -U context_user > /dev/null 2>&1; then
        echo -e "${GREEN}✅ PostgreSQL: Accessible${NC}"
    else
        echo -e "${RED}❌ PostgreSQL: Not accessible${NC}"
        all_accessible=false
    fi
    
    return $([ "$all_accessible" = true ] && echo 0 || echo 1)
}

# Check backend service
check_backend() {
    echo -e "${YELLOW}🐍 Checking backend service...${NC}"
    
    if curl -s -f http://localhost:8080/health > /dev/null; then
        echo -e "${GREEN}✅ Backend API: Accessible${NC}"
        
        # Check API docs
        if curl -s -f http://localhost:8080/docs > /dev/null; then
            echo -e "${GREEN}✅ API Documentation: Accessible${NC}"
        else
            echo -e "${YELLOW}⚠️  API Documentation: Not accessible${NC}"
        fi
        
        return 0
    else
        echo -e "${RED}❌ Backend API: Not accessible${NC}"
        return 1
    fi
}

# Check frontend service
check_frontend() {
    echo -e "${YELLOW}📱 Checking frontend service...${NC}"
    
    if curl -s -f http://localhost:5173 > /dev/null; then
        echo -e "${GREEN}✅ Frontend: Accessible${NC}"
        return 0
    else
        echo -e "${RED}❌ Frontend: Not accessible${NC}"
        return 1
    fi
}

# Check system resources
check_resources() {
    echo -e "${YELLOW}💻 Checking system resources...${NC}"
    
    # Check disk space
    disk_usage=$(df -h . | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -lt 90 ]; then
        echo -e "${GREEN}✅ Disk space: ${disk_usage}% used${NC}"
    else
        echo -e "${YELLOW}⚠️  Disk space: ${disk_usage}% used (high)${NC}"
    fi
    
    # Check memory usage
    if command -v free &> /dev/null; then
        mem_usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
        if [ "$mem_usage" -lt 80 ]; then
            echo -e "${GREEN}✅ Memory usage: ${mem_usage}%${NC}"
        else
            echo -e "${YELLOW}⚠️  Memory usage: ${mem_usage}% (high)${NC}"
        fi
    elif command -v vm_stat &> /dev/null; then
        # macOS memory check
        echo -e "${GREEN}✅ Memory: Available (macOS)${NC}"
    fi
    
    # Check Docker resource usage
    echo -e "${BLUE}📊 Docker container resource usage:${NC}"
    docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}" 2>/dev/null || echo "Unable to get Docker stats"
}

# Show service URLs
show_service_urls() {
    echo ""
    echo -e "${BLUE}🔗 Service URLs:${NC}"
    echo "=================================="
    echo -e "${GREEN}Frontend:${NC} http://localhost:5173"
    echo -e "${GREEN}Backend API:${NC} http://localhost:8080"
    echo -e "${GREEN}API Docs:${NC} http://localhost:8080/docs"
    echo -e "${GREEN}Qdrant Dashboard:${NC} http://localhost:6333/dashboard"
    echo -e "${GREEN}Prometheus:${NC} http://localhost:9090"
    echo -e "${GREEN}Grafana:${NC} http://localhost:3000"
}

# Show troubleshooting tips
show_troubleshooting() {
    echo ""
    echo -e "${YELLOW}🔧 Troubleshooting Tips:${NC}"
    echo "=================================="
    echo "• If services are not running: ./scripts/start-dev-orbstack.sh"
    echo "• Check container logs: docker-compose -f docker-compose.dev.yml logs -f [service]"
    echo "• Restart specific service: docker-compose -f docker-compose.dev.yml restart [service]"
    echo "• Check OrbStack status in the OrbStack app"
    echo "• For port conflicts, check: lsof -i :PORT_NUMBER"
    echo "• Reset everything: ./scripts/stop-dev.sh --clean-volumes && ./scripts/start-dev-orbstack.sh"
}

# Main health check function
main() {
    local overall_status=0
    
    check_orbstack || overall_status=1
    echo ""
    
    check_docker_services || overall_status=1
    echo ""
    
    check_service_endpoints || overall_status=1
    echo ""
    
    check_backend || overall_status=1
    echo ""
    
    check_frontend || overall_status=1
    echo ""
    
    check_resources
    echo ""
    
    if [ $overall_status -eq 0 ]; then
        echo -e "${GREEN}🎉 All systems are healthy!${NC}"
        show_service_urls
    else
        echo -e "${RED}⚠️  Some issues detected${NC}"
        show_service_urls
        show_troubleshooting
    fi
    
    return $overall_status
}

# Run main function
main
