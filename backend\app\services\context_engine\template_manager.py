"""
提示词模板管理器

管理不同场景的提示词模板，支持动态加载、版本控制和A/B测试。
"""
import json
import yaml
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import logging

from app.services.context_engine.prompt_builder import QueryType

logger = logging.getLogger(__name__)

class TemplateFormat(str, Enum):
    """模板格式"""
    JSON = "json"
    YAML = "yaml"
    MARKDOWN = "markdown"

class TemplateVersion(str, Enum):
    """模板版本"""
    V1_0 = "1.0"
    V1_1 = "1.1"
    V2_0 = "2.0"

@dataclass
class TemplateMetadata:
    """模板元数据"""
    name: str
    version: str
    description: str
    author: str
    created_at: str
    updated_at: str
    tags: List[str]
    query_types: List[str]
    languages: List[str]
    frameworks: List[str]
    performance_metrics: Dict[str, float]

@dataclass
class PromptTemplate:
    """提示词模板"""
    id: str
    metadata: TemplateMetadata
    system_prompt: str
    context_format: str
    user_prompt_format: str
    variables: Dict[str, Any]
    constraints: Dict[str, Any]
    examples: List[Dict[str, str]]

class TemplateLoader:
    """模板加载器"""
    
    def __init__(self, templates_dir: str = "templates"):
        self.templates_dir = Path(templates_dir)
        self.templates_dir.mkdir(exist_ok=True)
        
    def load_template(self, template_id: str, version: str = "latest") -> Optional[PromptTemplate]:
        """加载指定模板"""
        try:
            template_file = self._find_template_file(template_id, version)
            if not template_file:
                logger.warning(f"Template not found: {template_id} v{version}")
                return None
            
            with open(template_file, 'r', encoding='utf-8') as f:
                if template_file.suffix == '.json':
                    data = json.load(f)
                elif template_file.suffix in ['.yml', '.yaml']:
                    data = yaml.safe_load(f)
                else:
                    logger.error(f"Unsupported template format: {template_file.suffix}")
                    return None
            
            return self._parse_template_data(data)
            
        except Exception as e:
            logger.error(f"Failed to load template {template_id}: {e}")
            return None
    
    def load_all_templates(self) -> Dict[str, PromptTemplate]:
        """加载所有模板"""
        templates = {}
        
        for template_file in self.templates_dir.glob("*.json"):
            try:
                template = self.load_template(template_file.stem)
                if template:
                    templates[template.id] = template
            except Exception as e:
                logger.error(f"Failed to load template from {template_file}: {e}")
        
        return templates
    
    def save_template(self, template: PromptTemplate, format: TemplateFormat = TemplateFormat.JSON):
        """保存模板"""
        try:
            template_data = self._serialize_template(template)
            
            if format == TemplateFormat.JSON:
                filename = f"{template.id}.json"
                with open(self.templates_dir / filename, 'w', encoding='utf-8') as f:
                    json.dump(template_data, f, indent=2, ensure_ascii=False)
            elif format == TemplateFormat.YAML:
                filename = f"{template.id}.yml"
                with open(self.templates_dir / filename, 'w', encoding='utf-8') as f:
                    yaml.dump(template_data, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"Template saved: {template.id}")
            
        except Exception as e:
            logger.error(f"Failed to save template {template.id}: {e}")
    
    def _find_template_file(self, template_id: str, version: str) -> Optional[Path]:
        """查找模板文件"""
        # 优先查找指定版本
        if version != "latest":
            versioned_file = self.templates_dir / f"{template_id}_v{version}.json"
            if versioned_file.exists():
                return versioned_file
        
        # 查找默认文件
        default_file = self.templates_dir / f"{template_id}.json"
        if default_file.exists():
            return default_file
        
        # 查找YAML文件
        yaml_file = self.templates_dir / f"{template_id}.yml"
        if yaml_file.exists():
            return yaml_file
        
        return None
    
    def _parse_template_data(self, data: Dict[str, Any]) -> PromptTemplate:
        """解析模板数据"""
        metadata = TemplateMetadata(**data['metadata'])
        
        return PromptTemplate(
            id=data['id'],
            metadata=metadata,
            system_prompt=data['system_prompt'],
            context_format=data.get('context_format', ''),
            user_prompt_format=data.get('user_prompt_format', ''),
            variables=data.get('variables', {}),
            constraints=data.get('constraints', {}),
            examples=data.get('examples', [])
        )
    
    def _serialize_template(self, template: PromptTemplate) -> Dict[str, Any]:
        """序列化模板"""
        return {
            'id': template.id,
            'metadata': asdict(template.metadata),
            'system_prompt': template.system_prompt,
            'context_format': template.context_format,
            'user_prompt_format': template.user_prompt_format,
            'variables': template.variables,
            'constraints': template.constraints,
            'examples': template.examples
        }

class TemplateManager:
    """模板管理器"""
    
    def __init__(self, templates_dir: str = "templates"):
        self.loader = TemplateLoader(templates_dir)
        self.templates: Dict[str, PromptTemplate] = {}
        self.template_cache: Dict[str, PromptTemplate] = {}
        self.performance_stats: Dict[str, Dict[str, float]] = {}
        
        # 加载默认模板
        self._load_default_templates()
        
        logger.info(f"TemplateManager initialized with {len(self.templates)} templates")
    
    def get_template(self, template_id: str, version: str = "latest") -> Optional[PromptTemplate]:
        """获取模板"""
        cache_key = f"{template_id}:{version}"
        
        if cache_key in self.template_cache:
            return self.template_cache[cache_key]
        
        template = self.loader.load_template(template_id, version)
        if template:
            self.template_cache[cache_key] = template
        
        return template
    
    def get_template_by_query_type(self, query_type: QueryType, language: str = None, framework: str = None) -> Optional[PromptTemplate]:
        """根据查询类型获取最适合的模板"""
        candidates = []
        
        for template in self.templates.values():
            # 检查查询类型匹配
            if query_type.value in template.metadata.query_types:
                score = 1.0
                
                # 语言匹配加分
                if language and language in template.metadata.languages:
                    score += 0.5
                
                # 框架匹配加分
                if framework and framework in template.metadata.frameworks:
                    score += 0.5
                
                # 性能指标加分
                performance = self.performance_stats.get(template.id, {})
                if performance:
                    avg_quality = performance.get('avg_quality', 0.5)
                    score += avg_quality * 0.3
                
                candidates.append((template, score))
        
        if not candidates:
            # 返回通用模板
            return self.get_template('general')
        
        # 返回得分最高的模板
        best_template = max(candidates, key=lambda x: x[1])[0]
        return best_template
    
    def list_templates(self, query_type: QueryType = None, language: str = None) -> List[PromptTemplate]:
        """列出模板"""
        templates = list(self.templates.values())
        
        if query_type:
            templates = [t for t in templates if query_type.value in t.metadata.query_types]
        
        if language:
            templates = [t for t in templates if language in t.metadata.languages]
        
        return templates
    
    def create_template(self, template_data: Dict[str, Any]) -> PromptTemplate:
        """创建新模板"""
        template = self.loader._parse_template_data(template_data)
        self.templates[template.id] = template
        self.loader.save_template(template)
        
        logger.info(f"Created new template: {template.id}")
        return template
    
    def update_template(self, template_id: str, updates: Dict[str, Any]) -> Optional[PromptTemplate]:
        """更新模板"""
        template = self.templates.get(template_id)
        if not template:
            logger.warning(f"Template not found for update: {template_id}")
            return None
        
        # 更新模板数据
        if 'system_prompt' in updates:
            template.system_prompt = updates['system_prompt']
        if 'context_format' in updates:
            template.context_format = updates['context_format']
        if 'variables' in updates:
            template.variables.update(updates['variables'])
        
        # 更新元数据
        if 'metadata' in updates:
            for key, value in updates['metadata'].items():
                if hasattr(template.metadata, key):
                    setattr(template.metadata, key, value)
        
        # 保存更新
        self.loader.save_template(template)
        
        # 清除缓存
        self._clear_template_cache(template_id)
        
        logger.info(f"Updated template: {template_id}")
        return template
    
    def delete_template(self, template_id: str) -> bool:
        """删除模板"""
        if template_id not in self.templates:
            logger.warning(f"Template not found for deletion: {template_id}")
            return False
        
        # 删除文件
        template_file = self.loader.templates_dir / f"{template_id}.json"
        if template_file.exists():
            template_file.unlink()
        
        # 从内存中删除
        del self.templates[template_id]
        
        # 清除缓存
        self._clear_template_cache(template_id)
        
        logger.info(f"Deleted template: {template_id}")
        return True
    
    def record_performance(self, template_id: str, metrics: Dict[str, float]):
        """记录模板性能"""
        if template_id not in self.performance_stats:
            self.performance_stats[template_id] = {}
        
        self.performance_stats[template_id].update(metrics)
        
        # 更新模板元数据中的性能指标
        if template_id in self.templates:
            self.templates[template_id].metadata.performance_metrics.update(metrics)
    
    def get_performance_stats(self, template_id: str) -> Dict[str, float]:
        """获取模板性能统计"""
        return self.performance_stats.get(template_id, {})
    
    def _load_default_templates(self):
        """加载默认模板"""
        # 从文件加载已有模板
        self.templates = self.loader.load_all_templates()
        
        # 如果没有模板，创建默认模板
        if not self.templates:
            self._create_default_templates()
    
    def _create_default_templates(self):
        """创建默认模板"""
        default_templates = [
            {
                'id': 'debugging',
                'metadata': {
                    'name': '调试专用模板',
                    'version': '1.0',
                    'description': '专门用于技术问题调试的提示词模板',
                    'author': 'system',
                    'created_at': '2024-01-15T00:00:00Z',
                    'updated_at': '2024-01-15T00:00:00Z',
                    'tags': ['debugging', 'troubleshooting'],
                    'query_types': ['debugging'],
                    'languages': ['javascript', 'python', 'java'],
                    'frameworks': ['vue', 'react', 'django'],
                    'performance_metrics': {}
                },
                'system_prompt': '''你是一个专业的技术问题解决专家。用户遇到了技术问题，需要你的帮助来调试和解决。

请基于提供的技术文档和代码示例，帮助用户：
1. 分析问题的可能原因
2. 提供具体的解决方案
3. 给出可执行的代码示例
4. 解释解决方案的原理

回答要求：
- 直接针对问题，提供实用的解决方案
- 优先使用提供的代码示例和文档
- 如果有多种解决方案，请说明各自的优缺点
- 提供完整、可运行的代码示例''',
                'context_format': '## 🔧 相关技术资料\n\n{contexts}\n\n---\n\n',
                'user_prompt_format': '用户问题：{query}',
                'variables': {
                    'max_context_length': 6000,
                    'prioritize_code': True
                },
                'constraints': {
                    'max_response_length': 2000,
                    'include_code_examples': True
                },
                'examples': []
            },
            {
                'id': 'learning',
                'metadata': {
                    'name': '学习专用模板',
                    'version': '1.0',
                    'description': '专门用于技术学习的提示词模板',
                    'author': 'system',
                    'created_at': '2024-01-15T00:00:00Z',
                    'updated_at': '2024-01-15T00:00:00Z',
                    'tags': ['learning', 'tutorial'],
                    'query_types': ['learning'],
                    'languages': ['javascript', 'python', 'java'],
                    'frameworks': ['vue', 'react', 'django'],
                    'performance_metrics': {}
                },
                'system_prompt': '''你是一个耐心的技术导师。用户想要学习新的技术概念，需要你提供清晰、易懂的解释。

请基于提供的技术文档和示例，帮助用户：
1. 理解核心概念和原理
2. 提供循序渐进的学习路径
3. 给出实践性的代码示例
4. 解释最佳实践和注意事项

回答要求：
- 从基础概念开始，循序渐进
- 使用简单易懂的语言解释复杂概念
- 提供丰富的代码示例和实践练习
- 突出重点和关键知识点''',
                'context_format': '## 📚 学习资料\n\n{contexts}\n\n---\n\n',
                'user_prompt_format': '学习需求：{query}',
                'variables': {
                    'max_context_length': 8000,
                    'prioritize_tutorials': True
                },
                'constraints': {
                    'max_response_length': 2500,
                    'beginner_friendly': True
                },
                'examples': []
            }
        ]
        
        for template_data in default_templates:
            template = self.create_template(template_data)
            logger.info(f"Created default template: {template.id}")
    
    def _clear_template_cache(self, template_id: str):
        """清除模板缓存"""
        keys_to_remove = [key for key in self.template_cache.keys() if key.startswith(f"{template_id}:")]
        for key in keys_to_remove:
            del self.template_cache[key]

# 全局模板管理器实例
template_manager = TemplateManager()
