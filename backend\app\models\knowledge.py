"""
知识库数据模型

定义知识库、文档、代码示例等相关的数据模型。
"""
from sqlalchemy import Column, String, Text, Integer, Float, Boolean, DateTime, JSON, ForeignKey, Index
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field

Base = declarative_base()

class Collection(Base):
    """知识库集合表"""
    __tablename__ = "collections"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False, unique=True, index=True)
    description = Column(Text)
    category = Column(String(100), index=True)
    subcategory = Column(String(100), index=True)
    
    # 统计信息
    document_count = Column(Integer, default=0)
    code_example_count = Column(Integer, default=0)
    total_size_bytes = Column(Integer, default=0)
    
    # 配置信息
    settings = Column(JSON, default=dict)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    documents = relationship("Document", back_populates="collection", cascade="all, delete-orphan")
    code_examples = relationship("CodeExample", back_populates="collection", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index('idx_collection_category', 'category', 'subcategory'),
    )

class Document(Base):
    """文档表"""
    __tablename__ = "documents"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    collection_id = Column(UUID(as_uuid=True), ForeignKey("collections.id"), nullable=False)
    
    # 基本信息
    title = Column(String(500), nullable=False, index=True)
    content = Column(Text, nullable=False)
    summary = Column(Text)  # 文档摘要
    
    # 文件信息
    file_path = Column(String(1000))
    file_name = Column(String(255))
    file_type = Column(String(50), index=True)
    file_size = Column(Integer)
    file_hash = Column(String(64), index=True)  # 文件内容哈希
    
    # 分类信息
    language = Column(String(50), index=True)
    framework = Column(String(100), index=True)
    category = Column(String(100), index=True)
    subcategory = Column(String(100), index=True)
    
    # 标签和元数据
    tags = Column(ARRAY(String), default=list)
    keywords = Column(ARRAY(String), default=list)
    metadata = Column(JSON, default=dict)
    
    # 质量评分
    quality_score = Column(Float, default=0.0)
    popularity_score = Column(Float, default=0.0)
    
    # 统计信息
    view_count = Column(Integer, default=0)
    reference_count = Column(Integer, default=0)
    chunk_count = Column(Integer, default=0)
    
    # 状态
    status = Column(String(20), default="active", index=True)  # active, archived, deleted
    is_public = Column(Boolean, default=True)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_accessed_at = Column(DateTime(timezone=True))
    
    # 关系
    collection = relationship("Collection", back_populates="documents")
    chunks = relationship("DocumentChunk", back_populates="document", cascade="all, delete-orphan")
    embeddings = relationship("VectorEmbedding", back_populates="document", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index('idx_document_type_lang', 'file_type', 'language'),
        Index('idx_document_framework', 'framework'),
        Index('idx_document_status', 'status'),
        Index('idx_document_quality', 'quality_score'),
    )

class CodeExample(Base):
    """代码示例表"""
    __tablename__ = "code_examples"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    collection_id = Column(UUID(as_uuid=True), ForeignKey("collections.id"), nullable=False)
    
    # 基本信息
    title = Column(String(500), nullable=False, index=True)
    description = Column(Text)
    code = Column(Text, nullable=False)
    
    # 代码信息
    language = Column(String(50), nullable=False, index=True)
    framework = Column(String(100), index=True)
    version = Column(String(50))
    
    # 分类信息
    category = Column(String(100), index=True)
    subcategory = Column(String(100), index=True)
    difficulty_level = Column(String(20), index=True)  # beginner, intermediate, advanced
    
    # 标签和依赖
    tags = Column(ARRAY(String), default=list)
    dependencies = Column(ARRAY(String), default=list)
    keywords = Column(ARRAY(String), default=list)
    
    # 元数据
    metadata = Column(JSON, default=dict)
    
    # 质量评分
    quality_score = Column(Float, default=0.0)
    popularity_score = Column(Float, default=0.0)
    complexity_score = Column(Float, default=0.0)
    
    # 统计信息
    view_count = Column(Integer, default=0)
    usage_count = Column(Integer, default=0)
    line_count = Column(Integer, default=0)
    
    # 状态
    status = Column(String(20), default="active", index=True)
    is_public = Column(Boolean, default=True)
    is_tested = Column(Boolean, default=False)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_accessed_at = Column(DateTime(timezone=True))
    
    # 关系
    collection = relationship("Collection", back_populates="code_examples")
    embeddings = relationship("VectorEmbedding", back_populates="code_example", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index('idx_code_lang_framework', 'language', 'framework'),
        Index('idx_code_category', 'category', 'subcategory'),
        Index('idx_code_difficulty', 'difficulty_level'),
        Index('idx_code_quality', 'quality_score'),
    )

class DocumentChunk(Base):
    """文档分块表"""
    __tablename__ = "document_chunks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    
    # 分块信息
    chunk_index = Column(Integer, nullable=False)
    content = Column(Text, nullable=False)
    content_hash = Column(String(64), index=True)
    
    # 位置信息
    start_position = Column(Integer)
    end_position = Column(Integer)
    
    # 分块元数据
    chunk_type = Column(String(50), default="text")  # text, code, table, image
    section_title = Column(String(500))
    metadata = Column(JSON, default=dict)
    
    # 质量评分
    quality_score = Column(Float, default=0.0)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    document = relationship("Document", back_populates="chunks")
    
    # 索引
    __table_args__ = (
        Index('idx_chunk_document_index', 'document_id', 'chunk_index'),
        Index('idx_chunk_type', 'chunk_type'),
    )

class VectorEmbedding(Base):
    """向量嵌入表"""
    __tablename__ = "vector_embeddings"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 关联对象
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=True)
    code_example_id = Column(UUID(as_uuid=True), ForeignKey("code_examples.id"), nullable=True)
    chunk_id = Column(UUID(as_uuid=True), ForeignKey("document_chunks.id"), nullable=True)
    
    # 向量信息
    embedding_model = Column(String(100), nullable=False, index=True)
    embedding_dimension = Column(Integer, nullable=False)
    content_hash = Column(String(64), nullable=False, index=True)
    
    # 向量存储引用（实际向量存储在向量数据库中）
    vector_id = Column(String(255), nullable=False, index=True)
    collection_name = Column(String(255), nullable=False, index=True)
    
    # 元数据
    metadata = Column(JSON, default=dict)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    document = relationship("Document", back_populates="embeddings")
    code_example = relationship("CodeExample", back_populates="embeddings")
    
    # 索引
    __table_args__ = (
        Index('idx_embedding_model', 'embedding_model'),
        Index('idx_embedding_vector_id', 'vector_id'),
        Index('idx_embedding_collection', 'collection_name'),
    )

class QueryLog(Base):
    """查询日志表"""
    __tablename__ = "query_logs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 用户信息
    user_id = Column(String(255), index=True)
    session_id = Column(String(255), index=True)
    
    # 查询信息
    query_text = Column(Text, nullable=False)
    query_type = Column(String(50), index=True)
    query_hash = Column(String(64), index=True)
    
    # 处理信息
    processing_time_ms = Column(Integer)
    results_count = Column(Integer)
    context_used = Column(Boolean, default=False)
    
    # 反馈信息
    satisfaction_score = Column(Float)
    feedback_text = Column(Text)
    
    # 元数据
    metadata = Column(JSON, default=dict)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 索引
    __table_args__ = (
        Index('idx_query_user_session', 'user_id', 'session_id'),
        Index('idx_query_type', 'query_type'),
        Index('idx_query_time', 'created_at'),
    )

class SystemMetric(Base):
    """系统指标表"""
    __tablename__ = "system_metrics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 指标信息
    metric_name = Column(String(100), nullable=False, index=True)
    metric_value = Column(Float, nullable=False)
    metric_unit = Column(String(50))
    
    # 标签
    tags = Column(JSON, default=dict)
    
    # 时间戳
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    
    # 索引
    __table_args__ = (
        Index('idx_metric_name_time', 'metric_name', 'timestamp'),
    )

# Pydantic模型用于API
class CollectionCreate(BaseModel):
    name: str = Field(..., max_length=255)
    description: Optional[str] = None
    category: Optional[str] = None
    subcategory: Optional[str] = None
    settings: Optional[Dict[str, Any]] = None

class CollectionResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    category: Optional[str]
    subcategory: Optional[str]
    document_count: int
    code_example_count: int
    total_size_bytes: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class DocumentCreate(BaseModel):
    title: str = Field(..., max_length=500)
    content: str
    summary: Optional[str] = None
    file_type: Optional[str] = None
    language: Optional[str] = None
    framework: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None

class DocumentResponse(BaseModel):
    id: str
    title: str
    summary: Optional[str]
    file_type: Optional[str]
    language: Optional[str]
    framework: Optional[str]
    category: Optional[str]
    tags: Optional[List[str]]
    quality_score: float
    view_count: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class CodeExampleCreate(BaseModel):
    title: str = Field(..., max_length=500)
    description: Optional[str] = None
    code: str
    language: str
    framework: Optional[str] = None
    category: Optional[str] = None
    difficulty_level: Optional[str] = None
    tags: Optional[List[str]] = None
    dependencies: Optional[List[str]] = None

class CodeExampleResponse(BaseModel):
    id: str
    title: str
    description: Optional[str]
    language: str
    framework: Optional[str]
    category: Optional[str]
    difficulty_level: Optional[str]
    tags: Optional[List[str]]
    quality_score: float
    view_count: int
    line_count: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
