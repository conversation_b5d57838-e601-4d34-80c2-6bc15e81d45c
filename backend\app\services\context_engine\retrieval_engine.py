"""
Intelligent Retrieval Engine for Context Engine

Implements hybrid search combining vector similarity and keyword matching.
"""
import asyncio
import re
from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import dataclass
from collections import Counter
import math

from app.services.context_engine.vector_store import VectorStoreManager
from app.services.context_engine.embedding_service import get_embedding_service
from app.core.context_config import context_settings
import logging

logger = logging.getLogger(__name__)

@dataclass
class SearchResult:
    """Search result with metadata"""
    id: str
    content: str
    title: str
    score: float
    source_type: str  # 'document' or 'code'
    metadata: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'content': self.content,
            'title': self.title,
            'score': self.score,
            'source_type': self.source_type,
            'metadata': self.metadata
        }

@dataclass
class SearchQuery:
    """Structured search query"""
    text: str
    keywords: List[str]
    filters: Dict[str, Any]
    limit: int = 10
    similarity_threshold: float = 0.7

class KeywordExtractor:
    """Extract keywords from queries using various strategies"""
    
    def __init__(self):
        # Technical terms and frameworks
        self.tech_keywords = {
            'vue', 'react', 'angular', 'javascript', 'typescript', 'python',
            'fastapi', 'django', 'flask', 'nodejs', 'express', 'nextjs',
            'component', 'api', 'database', 'authentication', 'authorization',
            'middleware', 'router', 'state', 'props', 'hooks', 'lifecycle',
            'async', 'await', 'promise', 'callback', 'event', 'handler',
            'css', 'html', 'scss', 'tailwind', 'bootstrap', 'responsive',
            'mobile', 'desktop', 'ui', 'ux', 'design', 'layout', 'grid',
            'flexbox', 'animation', 'transition', 'form', 'validation',
            'testing', 'unit', 'integration', 'e2e', 'jest', 'cypress',
            'deployment', 'docker', 'kubernetes', 'ci', 'cd', 'pipeline'
        }
        
        # Stop words to filter out
        self.stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to',
            'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be',
            'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
            'will', 'would', 'could', 'should', 'may', 'might', 'can',
            'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she',
            'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'
        }
    
    def extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text"""
        # Convert to lowercase and split
        words = re.findall(r'\b\w+\b', text.lower())
        
        # Filter out stop words and short words
        keywords = [
            word for word in words 
            if len(word) > 2 and word not in self.stop_words
        ]
        
        # Prioritize technical keywords
        tech_keywords = [word for word in keywords if word in self.tech_keywords]
        other_keywords = [word for word in keywords if word not in self.tech_keywords]
        
        # Combine with tech keywords first
        return tech_keywords + other_keywords[:10]  # Limit to prevent noise

class BM25Scorer:
    """BM25 scoring for keyword-based search"""
    
    def __init__(self, k1: float = 1.5, b: float = 0.75):
        self.k1 = k1
        self.b = b
        self.doc_freqs = {}
        self.idf_cache = {}
        self.avgdl = 0
        self.corpus_size = 0
    
    def build_index(self, documents: List[Dict[str, Any]]):
        """Build BM25 index from documents"""
        self.corpus_size = len(documents)
        doc_lengths = []
        term_doc_counts = Counter()
        
        for doc in documents:
            content = doc.get('content', '')
            terms = self._tokenize(content)
            doc_length = len(terms)
            doc_lengths.append(doc_length)
            
            # Count unique terms in this document
            unique_terms = set(terms)
            for term in unique_terms:
                term_doc_counts[term] += 1
        
        self.avgdl = sum(doc_lengths) / len(doc_lengths) if doc_lengths else 0
        
        # Calculate IDF for each term
        for term, doc_count in term_doc_counts.items():
            idf = math.log((self.corpus_size - doc_count + 0.5) / (doc_count + 0.5))
            self.idf_cache[term] = max(idf, 0.01)  # Prevent negative IDF
    
    def score(self, query_terms: List[str], document: Dict[str, Any]) -> float:
        """Calculate BM25 score for a document"""
        content = document.get('content', '')
        doc_terms = self._tokenize(content)
        doc_length = len(doc_terms)
        
        if doc_length == 0:
            return 0.0
        
        term_counts = Counter(doc_terms)
        score = 0.0
        
        for term in query_terms:
            if term in term_counts:
                tf = term_counts[term]
                idf = self.idf_cache.get(term, 0.01)
                
                # BM25 formula
                numerator = tf * (self.k1 + 1)
                denominator = tf + self.k1 * (1 - self.b + self.b * (doc_length / self.avgdl))
                score += idf * (numerator / denominator)
        
        return score
    
    def _tokenize(self, text: str) -> List[str]:
        """Simple tokenization"""
        return re.findall(r'\b\w+\b', text.lower())

class HybridRetriever:
    """Hybrid retrieval combining vector and keyword search"""
    
    def __init__(self):
        self.vector_store = VectorStoreManager()
        self.embedding_service = get_embedding_service()
        self.keyword_extractor = KeywordExtractor()
        self.bm25_scorer = BM25Scorer()
        
        # Weights for combining scores
        self.vector_weight = 0.7
        self.keyword_weight = 0.3
        
        logger.info("Initialized HybridRetriever")
    
    async def initialize(self):
        """Initialize the retriever"""
        await self.vector_store.initialize()
        logger.info("HybridRetriever initialized successfully")
    
    async def search(
        self,
        query: str,
        collection_names: Optional[List[str]] = None,
        filters: Optional[Dict[str, Any]] = None,
        limit: int = 10,
        similarity_threshold: float = 0.7,
        rerank: bool = True
    ) -> List[SearchResult]:
        """Perform hybrid search"""
        try:
            # Extract keywords from query
            keywords = self.keyword_extractor.extract_keywords(query)
            
            # Create structured query
            search_query = SearchQuery(
                text=query,
                keywords=keywords,
                filters=filters or {},
                limit=limit * 2,  # Get more results for reranking
                similarity_threshold=similarity_threshold
            )
            
            # Perform vector search
            vector_results = await self._vector_search(search_query, collection_names)
            
            # Perform keyword search (if we have indexed documents)
            keyword_results = await self._keyword_search(search_query, collection_names)
            
            # Combine and rerank results
            combined_results = self._combine_results(vector_results, keyword_results)
            
            if rerank:
                combined_results = await self._rerank_results(combined_results, query)
            
            # Apply final filtering and limiting
            final_results = [
                result for result in combined_results
                if result.score >= similarity_threshold
            ][:limit]
            
            logger.info(
                "Hybrid search completed",
                query_length=len(query),
                keywords_count=len(keywords),
                vector_results=len(vector_results),
                keyword_results=len(keyword_results),
                final_results=len(final_results)
            )
            
            return final_results
            
        except Exception as e:
            logger.error("Hybrid search failed", query=query[:100], error=str(e))
            return []
    
    async def _vector_search(
        self,
        query: SearchQuery,
        collection_names: Optional[List[str]] = None
    ) -> List[SearchResult]:
        """Perform vector similarity search"""
        try:
            # Generate query embedding
            query_embedding = await self.embedding_service.embed_text(query.text)
            
            # Build filters
            search_filters = query.filters.copy()
            if collection_names:
                search_filters['collection'] = {'$in': collection_names}
            
            # Search in vector store
            raw_results = await self.vector_store.search_similar(
                query_embedding,
                limit=query.limit,
                filters=search_filters if search_filters else None
            )
            
            # Convert to SearchResult objects
            results = []
            for vector_id, score, metadata in raw_results:
                result = SearchResult(
                    id=vector_id,
                    content=metadata.get('content', ''),
                    title=metadata.get('title', ''),
                    score=score,
                    source_type=metadata.get('type', 'document'),
                    metadata=metadata
                )
                results.append(result)
            
            return results
            
        except Exception as e:
            logger.error("Vector search failed", error=str(e))
            return []
    
    async def _keyword_search(
        self,
        query: SearchQuery,
        collection_names: Optional[List[str]] = None
    ) -> List[SearchResult]:
        """Perform keyword-based search using BM25"""
        # For now, return empty list as we need to implement document indexing
        # In a full implementation, this would search through indexed documents
        return []
    
    def _combine_results(
        self,
        vector_results: List[SearchResult],
        keyword_results: List[SearchResult]
    ) -> List[SearchResult]:
        """Combine vector and keyword search results"""
        # Create a map of results by ID
        result_map = {}
        
        # Add vector results
        for result in vector_results:
            result.score = result.score * self.vector_weight
            result_map[result.id] = result
        
        # Add or merge keyword results
        for result in keyword_results:
            keyword_score = result.score * self.keyword_weight
            
            if result.id in result_map:
                # Combine scores
                result_map[result.id].score += keyword_score
            else:
                # Add new result
                result.score = keyword_score
                result_map[result.id] = result
        
        # Sort by combined score
        combined_results = list(result_map.values())
        combined_results.sort(key=lambda x: x.score, reverse=True)
        
        return combined_results
    
    async def _rerank_results(
        self,
        results: List[SearchResult],
        query: str
    ) -> List[SearchResult]:
        """Rerank results using advanced scoring"""
        try:
            # Simple reranking based on title relevance and content quality
            for result in results:
                # Boost score if query terms appear in title
                title_boost = self._calculate_title_boost(query, result.title)
                
                # Boost score based on content quality indicators
                quality_boost = self._calculate_quality_boost(result)
                
                # Apply boosts
                result.score = result.score * (1 + title_boost + quality_boost)
            
            # Re-sort by updated scores
            results.sort(key=lambda x: x.score, reverse=True)
            
            return results
            
        except Exception as e:
            logger.error("Reranking failed", error=str(e))
            return results
    
    def _calculate_title_boost(self, query: str, title: str) -> float:
        """Calculate boost based on title relevance"""
        if not title:
            return 0.0
        
        query_terms = set(query.lower().split())
        title_terms = set(title.lower().split())
        
        # Calculate overlap
        overlap = len(query_terms.intersection(title_terms))
        total_query_terms = len(query_terms)
        
        if total_query_terms == 0:
            return 0.0
        
        # Return boost factor (0.0 to 0.3)
        return min(0.3, (overlap / total_query_terms) * 0.3)
    
    def _calculate_quality_boost(self, result: SearchResult) -> float:
        """Calculate boost based on content quality"""
        boost = 0.0
        
        # Boost for longer, more detailed content
        content_length = len(result.content)
        if content_length > 1000:
            boost += 0.1
        elif content_length > 500:
            boost += 0.05
        
        # Boost for code examples
        if result.source_type == 'code':
            boost += 0.1
        
        # Boost for certain metadata indicators
        metadata = result.metadata
        if metadata.get('framework') in ['vue', 'fastapi', 'react']:
            boost += 0.05
        
        if metadata.get('category') in ['tutorial', 'guide', 'example']:
            boost += 0.05
        
        return min(0.2, boost)  # Cap at 0.2

class QueryProcessor:
    """Process and enhance user queries"""
    
    def __init__(self):
        self.keyword_extractor = KeywordExtractor()
    
    async def process_query(self, raw_query: str) -> SearchQuery:
        """Process raw query into structured search query"""
        # Clean and normalize query
        cleaned_query = self._clean_query(raw_query)
        
        # Extract keywords
        keywords = self.keyword_extractor.extract_keywords(cleaned_query)
        
        # Detect query intent and filters
        filters = self._extract_filters(cleaned_query)
        
        return SearchQuery(
            text=cleaned_query,
            keywords=keywords,
            filters=filters
        )
    
    def _clean_query(self, query: str) -> str:
        """Clean and normalize query text"""
        # Remove extra whitespace
        query = re.sub(r'\s+', ' ', query.strip())
        
        # Handle common typos and variations
        replacements = {
            'vuejs': 'vue',
            'vue.js': 'vue',
            'reactjs': 'react',
            'react.js': 'react',
            'javascript': 'js',
            'typescript': 'ts'
        }
        
        for old, new in replacements.items():
            query = re.sub(r'\b' + old + r'\b', new, query, flags=re.IGNORECASE)
        
        return query
    
    def _extract_filters(self, query: str) -> Dict[str, Any]:
        """Extract filters from query text"""
        filters = {}
        
        # Language filters
        if re.search(r'\b(python|py)\b', query, re.IGNORECASE):
            filters['language'] = 'python'
        elif re.search(r'\b(javascript|js)\b', query, re.IGNORECASE):
            filters['language'] = 'javascript'
        elif re.search(r'\b(typescript|ts)\b', query, re.IGNORECASE):
            filters['language'] = 'typescript'
        
        # Framework filters
        if re.search(r'\bvue\b', query, re.IGNORECASE):
            filters['framework'] = 'vue'
        elif re.search(r'\breact\b', query, re.IGNORECASE):
            filters['framework'] = 'react'
        elif re.search(r'\bfastapi\b', query, re.IGNORECASE):
            filters['framework'] = 'fastapi'
        
        # Type filters
        if re.search(r'\b(component|components)\b', query, re.IGNORECASE):
            filters['type'] = 'component'
        elif re.search(r'\b(api|endpoint)\b', query, re.IGNORECASE):
            filters['type'] = 'api'
        
        return filters
