"""
Chat API endpoints
"""
from fastapi import API<PERSON>outer, Depends, HTTPException
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import List, Optional
from app.db.database import get_db
from app.schemas.chat import ChatRequest, MessageResponse
from app.services.llm import get_llm_service, ChatMessage as LLMChatMessage, ChatRequest as LLMChatRequest

router = APIRouter()

@router.post("/stream")
async def chat_stream(
    request: ChatRequest,
    db: Session = Depends(get_db)
):
    """
    Stream chat response from LLM
    """
    async def generate():
        try:
            llm_service = await get_llm_service()

            # Convert request messages to LLM format
            llm_messages = [
                LLMChatMessage(role=msg.get("role", "user"), content=msg.get("content", ""))
                for msg in request.messages
            ]

            llm_request = LLMChatRequest(
                messages=llm_messages,
                stream=True
            )

            # Stream response from LLM service
            async for chunk in llm_service._stream_completion({
                "model": llm_request.model,
                "messages": [{"role": msg.role, "content": msg.content} for msg in llm_messages],
                "stream": True
            }):
                yield chunk

        except Exception as e:
            error_response = {
                "error": str(e),
                "role": "assistant"
            }
            yield f"data: {error_response}\n\n"
            yield "data: [DONE]\n\n"

    return StreamingResponse(
        generate(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )

@router.post("/send")
async def send_message(
    request: ChatRequest
):
    """
    Send a message and get response (non-streaming)
    """
    try:
        llm_service = await get_llm_service()

        # Convert request messages to LLM format and add system prompt for Artifacts
        llm_messages = [
            LLMChatMessage(
                role="system",
                content="""你是一个专业的前端开发助手，擅长创建各种类型的代码和可视化内容。

当用户请求创建以下类型的内容时，请使用相应的代码块格式：

1. **HTML页面** - 使用 ```html 代码块
2. **Vue组件** - 使用 ```vue 代码块
3. **数据图表** - 使用 ```g2plot 代码块
4. **流程图** - 使用 ```mermaid 代码块
5. **SVG图形** - 使用 ```svg 代码块
6. **JavaScript代码** - 使用 ```javascript 代码块

重要规则：
- 代码必须完整且可运行
- HTML页面需要包含完整的DOCTYPE、head、body结构
- Vue组件需要包含template、script、style三个部分
- 为代码添加适当的注释和说明
- 代码块会自动在Artifacts面板中渲染和预览

示例格式：
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>示例页面</title>
</head>
<body>
    <!-- 页面内容 -->
</body>
</html>
```"""
            )
        ]

        # Add user messages
        llm_messages.extend([
            LLMChatMessage(role=msg.role, content=msg.content)
            for msg in request.messages
        ])

        llm_request = LLMChatRequest(
            messages=llm_messages,
            stream=False
        )

        # Get response from LLM service
        response = await llm_service.chat_completion(llm_request)

        # Extract content from response
        content = ""
        if "choices" in response and len(response["choices"]) > 0:
            choice = response["choices"][0]
            if "message" in choice and "content" in choice["message"]:
                content = choice["message"]["content"]

        return {
            "role": "assistant",
            "content": content,
            "timestamp": "2025-07-30T12:00:00Z"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/messages/{session_id}", response_model=List[MessageResponse])
async def get_messages(
    session_id: int,
    db: Session = Depends(get_db)
):
    """
    Get all messages for a chat session
    """
    from app.services.database import get_database_service
    db_service = get_database_service(db)
    messages = db_service.get_messages(session_id)
    return messages

@router.get("/health")
async def health_check():
    """
    Health check endpoint for chat service
    """
    return {"status": "healthy", "service": "chat"}
