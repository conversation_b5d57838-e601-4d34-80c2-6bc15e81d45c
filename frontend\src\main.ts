import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ArcoVue from '@arco-design/web-vue'
import '@arco-design/web-vue/dist/arco.css'
import App from './App.vue'
import router from './router'
import { performanceMonitor } from './utils/performance'
import { initializeSecurity } from './utils/security'

// 初始化安全设置
initializeSecurity()

// 开始性能监控
performanceMonitor.startTiming('app_initialization')

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(ArcoVue)

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Global error:', err, info)
  // 在生产环境中，这里应该发送错误报告到监控服务
}

app.mount('#app')

// 结束性能监控
performanceMonitor.endTiming('app_initialization')
