#!/usr/bin/env python3
"""
知识库初始化脚本

扫描knowledge目录下的文档，处理并存储到向量数据库中。
"""
import asyncio
import sys
import os
from pathlib import Path
from typing import List, Dict, Any
import logging

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from app.services.context_engine.document_processor import document_processor
from app.services.context_engine.open_source_document_processor import open_source_processor
from app.services.context_engine.vectorization_service import vectorization_service, vector_store
from app.models.knowledge import Collection, Document, CodeExample
from app.database import get_db, engine
from sqlalchemy.orm import Session

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class KnowledgeBaseInitializer:
    """知识库初始化器"""

    def __init__(self, knowledge_dir: str = "knowledge", use_opensource: bool = True):
        self.knowledge_dir = Path(knowledge_dir)
        self.use_opensource = use_opensource

        # 选择处理器
        if use_opensource:
            self.processor = open_source_processor
            self.supported_extensions = set(open_source_processor.supported_types.keys())
            logger.info("使用开源文档处理器")
        else:
            self.processor = document_processor
            self.supported_extensions = {'.md', '.txt', '.rst'}
            logger.info("使用Unstructured.io文档处理器")

        self.processed_count = 0
        self.error_count = 0
    
    async def initialize(self):
        """初始化知识库"""
        logger.info("开始初始化知识库...")
        
        try:
            # 确保知识库目录存在
            if not self.knowledge_dir.exists():
                logger.error(f"知识库目录不存在: {self.knowledge_dir}")
                return False
            
            # 扫描并处理文档
            await self._scan_and_process_documents()
            
            # 输出统计信息
            logger.info(f"知识库初始化完成!")
            logger.info(f"成功处理: {self.processed_count} 个文档")
            logger.info(f"处理失败: {self.error_count} 个文档")
            
            return True
            
        except Exception as e:
            logger.error(f"知识库初始化失败: {e}")
            return False
    
    async def _scan_and_process_documents(self):
        """扫描并处理文档"""
        # 获取所有支持的文档文件
        document_files = []
        for ext in self.supported_extensions:
            document_files.extend(self.knowledge_dir.rglob(f"*{ext}"))
        
        logger.info(f"发现 {len(document_files)} 个文档文件")
        
        # 按目录分组创建集合
        collections = self._organize_by_collections(document_files)
        
        # 处理每个集合
        for collection_name, files in collections.items():
            await self._process_collection(collection_name, files)
    
    def _organize_by_collections(self, files: List[Path]) -> Dict[str, List[Path]]:
        """按目录组织文件到集合中"""
        collections = {}
        
        for file_path in files:
            # 获取相对于knowledge目录的路径
            relative_path = file_path.relative_to(self.knowledge_dir)
            
            # 使用第一级目录作为集合名
            if len(relative_path.parts) > 1:
                collection_name = relative_path.parts[0]
            else:
                collection_name = "general"
            
            if collection_name not in collections:
                collections[collection_name] = []
            
            collections[collection_name].append(file_path)
        
        return collections
    
    async def _process_collection(self, collection_name: str, files: List[Path]):
        """处理单个集合"""
        logger.info(f"处理集合: {collection_name} ({len(files)} 个文件)")
        
        try:
            # 创建或获取集合
            collection = await self._get_or_create_collection(collection_name)
            
            # 处理集合中的每个文件
            for file_path in files:
                try:
                    await self._process_document(file_path, collection)
                    self.processed_count += 1
                    logger.info(f"✓ 处理完成: {file_path.name}")
                except Exception as e:
                    self.error_count += 1
                    logger.error(f"✗ 处理失败: {file_path.name} - {e}")
            
        except Exception as e:
            logger.error(f"处理集合失败 {collection_name}: {e}")
    
    async def _get_or_create_collection(self, name: str) -> Dict[str, Any]:
        """获取或创建集合"""
        # 这里简化处理，实际应该与数据库交互
        collection_info = {
            'name': name,
            'description': f'{name} 技术文档集合',
            'category': self._determine_category(name)
        }
        
        logger.info(f"集合: {name} ({collection_info['category']})")
        return collection_info
    
    def _determine_category(self, collection_name: str) -> str:
        """根据集合名确定分类"""
        category_mapping = {
            'frontend': 'frontend',
            'backend': 'backend',
            'devops': 'devops',
            'ai-ml': 'ai-ml',
            'mobile': 'mobile',
            'security': 'security',
            'best-practices': 'best-practices',
            'examples': 'examples'
        }
        
        return category_mapping.get(collection_name, 'general')
    
    async def _process_document(self, file_path: Path, collection: Dict[str, Any]):
        """处理单个文档"""
        try:
            # 根据选择的处理器处理文件
            if self.use_opensource:
                processed_doc = self.processor.process_file(file_path)
            else:
                # 对于Unstructured.io，只处理Markdown文件
                if file_path.suffix.lower() == '.md':
                    processed_doc = self.processor.process_markdown_file(file_path)
                else:
                    # 对于其他格式，使用开源处理器作为fallback
                    processed_doc = open_source_processor.process_file(file_path)

            # 向量化文档
            vectorized_doc = await vectorization_service.vectorize_document(processed_doc)

            # 存储到向量存储
            vector_store.add_document(vectorized_doc)

            # 这里可以添加数据库存储逻辑
            await self._store_to_database(processed_doc, collection)

        except Exception as e:
            logger.error(f"处理文档失败 {file_path}: {e}")
            raise
    
    async def _store_to_database(self, processed_doc, collection: Dict[str, Any]):
        """存储到数据库（模拟）"""
        # 这里应该实现实际的数据库存储逻辑
        # 由于数据库模型可能还没有完全设置，这里只是记录日志
        logger.debug(f"存储文档到数据库: {processed_doc.title}")
        
        # 示例数据库操作（需要实际的数据库连接）
        """
        db = next(get_db())
        try:
            # 创建文档记录
            db_doc = Document(
                title=processed_doc.title,
                content=processed_doc.content,
                summary=processed_doc.summary,
                file_path=processed_doc.file_path,
                file_hash=processed_doc.file_hash,
                metadata=processed_doc.metadata,
                keywords=processed_doc.keywords
            )
            db.add(db_doc)
            db.commit()
            
        except Exception as e:
            db.rollback()
            logger.error(f"数据库存储失败: {e}")
        finally:
            db.close()
        """

class KnowledgeBaseManager:
    """知识库管理器"""

    def __init__(self, use_opensource: bool = True):
        self.initializer = KnowledgeBaseInitializer(use_opensource=use_opensource)
    
    async def full_rebuild(self):
        """完全重建知识库"""
        logger.info("开始完全重建知识库...")
        
        # 清空现有数据
        vector_store.vectors.clear()
        vector_store.chunk_index.clear()
        
        # 重新初始化
        success = await self.initializer.initialize()
        
        if success:
            logger.info("知识库重建完成!")
        else:
            logger.error("知识库重建失败!")
        
        return success
    
    async def incremental_update(self, file_paths: List[str] = None):
        """增量更新知识库"""
        logger.info("开始增量更新知识库...")
        
        if file_paths:
            # 更新指定文件
            for file_path in file_paths:
                try:
                    path = Path(file_path)
                    if path.exists() and path.suffix in self.initializer.supported_extensions:
                        # 处理单个文件
                        collection = {'name': 'updated', 'category': 'general'}
                        await self.initializer._process_document(path, collection)
                        logger.info(f"更新文件: {file_path}")
                except Exception as e:
                    logger.error(f"更新文件失败 {file_path}: {e}")
        else:
            # 扫描所有文件进行增量更新
            await self.initializer._scan_and_process_documents()
        
        logger.info("增量更新完成!")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        stats = vector_store.get_stats()
        stats.update({
            'processed_documents': self.initializer.processed_count,
            'processing_errors': self.initializer.error_count
        })
        return stats

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='知识库初始化工具')
    parser.add_argument('--rebuild', action='store_true', help='完全重建知识库')
    parser.add_argument('--update', action='store_true', help='增量更新知识库')
    parser.add_argument('--files', nargs='+', help='指定要更新的文件')
    parser.add_argument('--stats', action='store_true', help='显示统计信息')
    parser.add_argument('--processor', choices=['opensource', 'unstructured'],
                       default='opensource', help='选择文档处理器 (默认: opensource)')
    parser.add_argument('--list-formats', action='store_true', help='列出支持的文件格式')
    
    args = parser.parse_args()

    # 确定使用哪个处理器
    use_opensource = args.processor == 'opensource'
    manager = KnowledgeBaseManager(use_opensource=use_opensource)

    # 显示支持的格式
    if args.list_formats:
        print("支持的文件格式:")
        if use_opensource:
            formats = list(open_source_processor.supported_types.keys())
            print("开源处理器支持:")
        else:
            formats = ['.md', '.txt', '.rst']
            print("Unstructured.io处理器支持:")

        for fmt in sorted(formats):
            print(f"  {fmt}")
        return
    
    try:
        if args.rebuild:
            await manager.full_rebuild()
        elif args.update:
            await manager.incremental_update(args.files)
        elif args.stats:
            stats = manager.get_stats()
            print("知识库统计信息:")
            for key, value in stats.items():
                print(f"  {key}: {value}")
        else:
            # 默认执行初始化
            await manager.initializer.initialize()
            
    except KeyboardInterrupt:
        logger.info("操作被用户中断")
    except Exception as e:
        logger.error(f"操作失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # 运行主函数
    asyncio.run(main())
