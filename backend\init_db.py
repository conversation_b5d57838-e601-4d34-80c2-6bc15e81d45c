"""
Database initialization script using raw SQL
"""
import sqlite3
import os

def create_database():
    """Create database and tables using SQL script"""
    # Create database file
    db_path = "artifacts_chat.db"
    
    # Check if database already exists
    if os.path.exists(db_path):
        print(f"Database already exists at {db_path}")
        return
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Read SQL script
    with open("migrations/001_initial_schema.sql", "r") as f:
        sql_script = f.read()
    
    # Execute SQL script
    cursor.executescript(sql_script)
    
    # Commit changes and close connection
    conn.commit()
    conn.close()
    
    print(f"Database created successfully at {db_path}")

if __name__ == "__main__":
    create_database()
